{"name": "open-assistant", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "2.4.0", "@docusaurus/preset-classic": "2.4.0", "@docusaurus/theme-mermaid": "2.4.0", "@mdx-js/react": "^1.6.22", "clsx": "^1.2.1", "docusaurus-preset-openapi": "^0.6.3", "prism-react-renderer": "^1.3.5", "react": "^17.0.2", "react-dom": "^17.0.2", "react-player": "^2.11.0", "url": "^0.11.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "2.4.0", "@tsconfig/docusaurus": "^1.0.5", "typescript": "^4.7.4"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.14"}}