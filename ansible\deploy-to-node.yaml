# ansible playbook to set up some docker containers

- name: Deploy to node
  hosts: dev
  gather_facts: true
  vars:
    stack_name: "dev"
    image_tag: latest
    backend_port: 8080
    website_port: 3000
    postgres_password: postgres
    web_api_key: "1234"
  tasks:
    # reduces the chance of oom when pulling new images
    - name: Clear unused images
      community.docker.docker_prune:
        images: true

    - name: Create network
      community.docker.docker_network:
        name: "oasst-{{ stack_name }}"
        state: present
        driver: bridge

    - name: Create stack files directory
      ansible.builtin.file:
        path: "./{{ stack_name }}"
        state: directory
        mode: 0755

    - name: Copy redis.conf to managed node
      ansible.builtin.copy:
        src: ./redis.conf
        dest: "./{{ stack_name }}/redis.conf"
        mode: 0644

    - name: Set up Redis
      community.docker.docker_container:
        name: "oasst-{{ stack_name }}-redis"
        image: redis
        state: started
        recreate: "{{ (stack_name == 'dev') | bool }}"
        restart_policy: always
        network_mode: "oasst-{{ stack_name }}"
        healthcheck:
          test: ["CMD-SHELL", "redis-cli ping | grep PONG"]
          interval: 2s
          timeout: 2s
          retries: 10
        command: redis-server /usr/local/etc/redis/redis.conf
        volumes:
          - "./{{ stack_name }}/redis.conf:/usr/local/etc/redis/redis.conf"

    - name: Create volumes for postgres
      community.docker.docker_volume:
        name: "oasst-{{ stack_name }}-postgres-{{ item.name }}"
        state: present
      loop:
        - name: backend
        - name: web

    - name: Create postgres containers
      community.docker.docker_container:
        name: "oasst-{{ stack_name }}-postgres-{{ item.name }}"
        image: ghcr.io/laion-ai/open-assistant/oasst-postgres
        platform: linux/x86_64
        state: started
        pull: true
        recreate: "{{ (stack_name == 'dev') | bool }}"
        restart_policy: always
        network_mode: "oasst-{{ stack_name }}"
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: "{{ postgres_password }}"
          POSTGRES_DB: postgres
          S3_BUCKET_NAME:
            "{{ lookup('ansible.builtin.env', 'S3_BUCKET_NAME') }}"
          S3_PREFIX: "{{ item.name }}"
          AWS_ACCESS_KEY_ID:
            "{{ lookup('ansible.builtin.env', 'AWS_ACCESS_KEY') }}"
          AWS_SECRET_ACCESS_KEY:
            "{{ lookup('ansible.builtin.env', 'AWS_SECRET_KEY') }}"
          AWS_DEFAULT_REGION: "{{ lookup('ansible.builtin.env', 'S3_REGION') }}"
        volumes:
          - "oasst-{{ stack_name }}-postgres-{{ item.name
            }}:/var/lib/postgresql/data"
        healthcheck:
          test: ["CMD", "pg_isready", "-U", "postgres"]
          interval: 2s
          timeout: 2s
          retries: 10
        shm_size: 1G
      loop:
        - name: backend
        - name: web

    - name: Run the oasst oasst-backend
      community.docker.docker_container:
        name: "oasst-{{ stack_name }}-backend"
        image: "ghcr.io/laion-ai/open-assistant/oasst-backend:{{ image_tag }}"
        platform: linux/x86_64
        state: started
        recreate: true
        pull: true
        restart_policy: always
        network_mode: "oasst-{{ stack_name }}"
        env:
          POSTGRES_HOST: "oasst-{{ stack_name }}-postgres-backend"
          POSTGRES_PASSWORD: "{{ postgres_password }}"
          REDIS_HOST: "oasst-{{ stack_name }}-redis"
          DEBUG_USE_SEED_DATA:
            "{{ 'true' if stack_name == 'dev' else 'false' }}"
          DEBUG_ALLOW_SELF_LABELING:
            "{{ 'true' if stack_name == 'dev' else 'false' }}"
          MAX_WORKERS: "1"
          RATE_LIMIT: "{{ 'false' if stack_name == 'dev' else 'true' }}"
          DEBUG_SKIP_EMBEDDING_COMPUTATION: "true"
          DEBUG_SKIP_TOXICITY_CALCULATION:
            "{{ lookup('ansible.builtin.env', 'SKIP_TOXICITY_CALCULATION') |
            default('true', true) }}"
          OFFICIAL_WEB_API_KEY: "{{ web_api_key }}"
          TREE_MANAGER__INIT_PROMPT_DISABLED_LANGS:
            "{{ lookup('ansible.builtin.env', 'INIT_PROMPT_DISABLED_LANGS') |
            default('', true) }}"
          TREE_MANAGER__MAX_ACTIVE_TREES:
            "{{ lookup('ansible.builtin.env', 'MAX_ACTIVE_TREES') |
            default('10', true) }}"
          TREE_MANAGER__MAX_INITIAL_PROMPT_REVIEW:
            "{{ lookup('ansible.builtin.env', 'MAX_INITIAL_PROMPT_REVIEW') |
            default('100', true) }}"
          TREE_MANAGER__MAX_TREE_DEPTH:
            "{{ lookup('ansible.builtin.env', 'MAX_TREE_DEPTH') | default('5',
            true) }}"
          TREE_MANAGER__GOAL_TREE_SIZE:
            "{{ lookup('ansible.builtin.env', 'GOAL_TREE_SIZE') | default('15',
            true) }}"
          TREE_MANAGER__MAX_CHILDREN_COUNT:
            "{{ lookup('ansible.builtin.env', 'MAX_CHILDREN_COUNT') |
            default('3', true) }}"
          TREE_MANAGER__LONELY_CHILDREN_COUNT:
            "{{ lookup('ansible.builtin.env', 'LONELY_CHILDREN_COUNT') |
            default('2', true) }}"
          TREE_MANAGER__P_LONELY_CHILD_EXTENSION:
            "{{ lookup('ansible.builtin.env', 'P_LONELY_CHILD_EXTENSION') |
            default('0.75', true) }}"
          TREE_MANAGER__P_ACTIVATE_BACKLOG_TREE:
            "{{ lookup('ansible.builtin.env', 'P_ACTIVATE_BACKLOG_TREE') |
            default('0.1', true) }}"
          TREE_MANAGER__NUM_REQUIRED_RANKINGS:
            "{{ lookup('ansible.builtin.env', 'NUM_REQUIRED_RANKINGS') |
            default('3', true) }}"
          TREE_MANAGER__NUM_REVIEWS_REPLY:
            "{{ lookup('ansible.builtin.env', 'NUM_REVIEWS_REPLY') |
            default('3', true) }}"
          TREE_MANAGER__NUM_REVIEWS_INITIAL_PROMPT:
            "{{ lookup('ansible.builtin.env', 'NUM_REVIEWS_INITIAL_PROMPT') |
            default('3', true) }}"
          TREE_MANAGER__MIN_ACTIVE_RANKINGS_PER_LANG:
            "{{ lookup('ansible.builtin.env', 'MIN_ACTIVE_RANKINGS_PER_LANG') |
            default('0', true) }}"
          TREE_MANAGER__MAX_PROMPT_LOTTERY_WAITING:
            "{{ lookup('ansible.builtin.env', 'MAX_PROMPT_LOTTERY_WAITING') |
            default('250', true) }}"
          MESSAGE_SIZE_LIMIT:
            "{{ lookup('ansible.builtin.env', 'MESSAGE_SIZE_LIMIT') |
            default('2000', true) }}"
          USER_STATS_INTERVAL_DAY:
            "{{ lookup('ansible.builtin.env', 'STATS_INTERVAL_DAY') |
            default('5', true) }}"
          USER_STATS_INTERVAL_WEEK:
            "{{ lookup('ansible.builtin.env', 'STATS_INTERVAL_WEEK') |
            default('15', true) }}"
          USER_STATS_INTERVAL_MONTH:
            "{{ lookup('ansible.builtin.env', 'STATS_INTERVAL_MONTH') |
            default('60', true) }}"
          USER_STATS_INTERVAL_TOTAL:
            "{{ lookup('ansible.builtin.env', 'STATS_INTERVAL_TOTAL') |
            default('240', true) }}"
          LOGURU_LEVEL:
            "{{ lookup('ansible.builtin.env', 'LOGURU_LEVEL') | default('INFO',
            true) }}"
          BACKEND_CORS_ORIGINS_CSV:
            "{{ lookup('ansible.builtin.env', 'BACKEND_CORS_ORIGINS') }}"
          DISCORD_API_KEY:
            "{{ lookup('ansible.builtin.env', 'DISCORD_API_KEY') }}"
          DISCORD_CHANNEL_ID:
            "{{ lookup('ansible.builtin.env', 'DISCORD_CHANNEL_ID') }}"
        ports:
          - "{{ backend_port }}:8080"

    - name: Setup Celery worker
      community.docker.docker_container:
        name: "oasst-{{ stack_name }}-celery-worker"
        image:
          "ghcr.io/laion-ai/open-assistant/oasst-backend-worker:{{ image_tag }}"
        state: started
        pull: true
        restart_policy: always
        network_mode: "oasst-{{ stack_name }}"
        env:
          CELERY_BROKER_URL: "redis://oasst-{{ stack_name }}-redis:6379/0"
          CELERY_RESULT_BACKEND: "redis://oasst-{{ stack_name }}-redis:6379/0"
          POSTGRES_HOST: "oasst-{{ stack_name }}-postgres-backend"
          POSTGRES_PASSWORD: "{{ postgres_password }}"
          REDIS_HOST: "oasst-{{ stack_name }}-redis"
          MAX_WORKERS: "1"
        command: celery -A oasst_backend.celery_worker worker -l info

    - name: Setup Celery worker beat
      community.docker.docker_container:
        name: "oasst-{{ stack_name }}-celery-worker-beat"
        image:
          "ghcr.io/laion-ai/open-assistant/oasst-backend-worker:{{ image_tag }}"
        state: started
        pull: true
        restart_policy: always
        network_mode: "oasst-{{ stack_name }}"
        env:
          CELERY_BROKER_URL: "redis://oasst-{{ stack_name }}-redis:6379/0"
          CELERY_RESULT_BACKEND: "redis://oasst-{{ stack_name }}-redis:6379/0"
          POSTGRES_HOST: "oasst-{{ stack_name }}-postgres-backend"
          POSTGRES_PASSWORD: "{{ postgres_password }}"
          REDIS_HOST: "oasst-{{ stack_name }}-redis"
          MAX_WORKERS: "1"
        command: celery -A oasst_backend.celery_worker beat -l INFO

    - name: Run the oasst oasst-web frontend
      community.docker.docker_container:
        name: "oasst-{{ stack_name }}-web"
        image: "ghcr.io/laion-ai/open-assistant/oasst-web:{{ image_tag }}"
        state: started
        recreate: true
        pull: true
        restart_policy: always
        network_mode: "oasst-{{ stack_name }}"
        env:
          ADMIN_USERS: "{{ lookup('ansible.builtin.env', 'WEB_ADMIN_USERS') }}"
          MODERATOR_USERS:
            "{{ lookup('ansible.builtin.env', 'WEB_MODERATOR_USERS') }}"
          DATABASE_URL:
            "postgres://postgres:{{ postgres_password }}@oasst-{{ stack_name
            }}-postgres-web/postgres"
          DEBUG_LOGIN: "{{ 'true' if stack_name == 'dev' else 'false' }}"
          DISCORD_CLIENT_ID:
            "{{ lookup('ansible.builtin.env', 'WEB_DISCORD_CLIENT_ID') }}"
          DISCORD_CLIENT_SECRET:
            "{{ lookup('ansible.builtin.env', 'WEB_DISCORD_CLIENT_SECRET') }}"
          GOOGLE_CLIENT_ID:
            "{{ lookup('ansible.builtin.env', 'WEB_GOOGLE_CLIENT_ID') }}"
          GOOGLE_CLIENT_SECRET:
            "{{ lookup('ansible.builtin.env', 'WEB_GOOGLE_CLIENT_SECRET') }}"
          EMAIL_FROM: <EMAIL>
          EMAIL_SERVER_HOST:
            "{{ lookup('ansible.builtin.env', 'WEB_EMAIL_SERVER_HOST') }}"
          EMAIL_SERVER_PASSWORD:
            "{{ lookup('ansible.builtin.env', 'WEB_EMAIL_SERVER_PASSWORD') }}"
          EMAIL_SERVER_PORT:
            "{{ lookup('ansible.builtin.env', 'WEB_EMAIL_SERVER_PORT') }}"
          EMAIL_SERVER_USER:
            "{{ lookup('ansible.builtin.env', 'WEB_EMAIL_SERVER_USER') }}"
          FASTAPI_URL: "{{ lookup('ansible.builtin.env', 'BACKEND_URL') }}"
          FASTAPI_KEY: "{{ web_api_key }}"
          NEXTAUTH_SECRET:
            "{{ lookup('ansible.builtin.env', 'WEB_NEXTAUTH_SECRET') }}"
          NEXTAUTH_URL:
            "{{ 'https://open-assistant.io/' if stack_name == 'production' else
            ('https://web.' + stack_name + '.open-assistant.io/') }}"
          CLOUDFLARE_CAPTCHA_SITE_KEY:
            "{{ lookup('ansible.builtin.env',
            'WEB_NEXT_PUBLIC_CLOUDFLARE_CAPTCHA_SITE_KEY') }}"
          CLOUDFLARE_CAPTCHA_SECRET_KEY:
            "{{ lookup('ansible.builtin.env',
            'WEB_CLOUDFLARE_CAPTCHA_SECRET_KEY') }}"
          ENABLE_EMAIL_SIGNIN_CAPTCHA:
            "{{ lookup('ansible.builtin.env',
            'WEB_NEXT_PUBLIC_ENABLE_EMAIL_SIGNIN_CAPTCHA') }}"
          ENABLE_EMAIL_SIGNIN:
            "{{ lookup('ansible.builtin.env',
            'WEB_NEXT_PUBLIC_ENABLE_EMAIL_SIGNIN') }}"
          MAINTENANCE_MODE:
            "{{ lookup('ansible.builtin.env', 'MAINTENANCE_MODE') }}"
          INFERENCE_SERVER_HOST:
            "{{ lookup('ansible.builtin.env', 'WEB_INFERENCE_SERVER_HOST') }}"
          INFERENCE_SERVER_API_KEY:
            "{{ lookup('ansible.builtin.env', 'WEB_INFERENCE_SERVER_API_KEY') }}"
          ENABLE_CHAT: "{{ lookup('ansible.builtin.env', 'WEB_ENABLE_CHAT') }}"
          BYE: "{{ lookup('ansible.builtin.env', 'WEB_BYE') }}"
          ENABLE_DRAFTS_WITH_PLUGINS:
            "{{ lookup('ansible.builtin.env',
            'WEB_ENABLE_DRAFTS_WITH_PLUGINS')}}"
          NUM_GENERATED_DRAFTS:
            "{{ lookup('ansible.builtin.env', 'WEB_NUM_GENERATED_DRAFTS') }}"
          CURRENT_ANNOUNCEMENT:
            "{{ lookup('ansible.builtin.env', 'WEB_CURRENT_ANNOUNCEMENT') }}"
        ports:
          - "{{ website_port }}:3000"
        command: bash wait-for-postgres.sh node server.js
