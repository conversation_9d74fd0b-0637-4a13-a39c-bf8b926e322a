name: Deploy docs to gitHub pages

on:
  push:
    branches:
      - main
    paths:
      - ".github/workflows/deploy-docs-site.yaml"
      - "docs/**"
  pull_request:
    paths:
      - ".github/workflows/deploy-docs-site.yaml"
      - "docs/**"

jobs:
  deploy:
    runs-on: ubuntu-22.04
    permissions:
      contents: write
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
    defaults:
      run:
        working-directory: docs
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: "18"

      - name: Install dependencies
        run: yarn install --frozen-lockfile
      - name: Build website
        run: yarn build

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        if: ${{ github.ref == 'refs/heads/main' }}
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs/build
