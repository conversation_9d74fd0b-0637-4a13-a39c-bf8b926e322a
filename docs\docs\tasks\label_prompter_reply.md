# Classifying an initial prompt or user reply

In this task, you'll be shown a random message written by another person. This
message is mimicking a request or question directed towards the assistant - a
**prompt**. This prompt could either be a start of a conversation, or a reply to
a message from the assistant. Your job is to rate parameters like quality or
politeness, as well as include any applicable labels, such as spam, PII or
sexual content.

If you are unsure of the definition of any of the labels, look up their
definition
[here](https://projects.laion.ai/Open-Assistant/docs/guides/guidelines#label-explanation).

Please make sure to read the
[guidelines](https://projects.laion.ai/Open-Assistant/docs/guides/guidelines#classifying-user)
before submitting.
