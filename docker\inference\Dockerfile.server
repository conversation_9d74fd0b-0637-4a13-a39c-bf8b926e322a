# syntax=docker/dockerfile:1

ARG MODULE="inference"
ARG SERVICE="server"

ARG APP_USER="${MODULE}-${SERVICE}"
ARG APP_RELATIVE_PATH="${MODULE}/${SERVICE}"


FROM python:3.10-slim as build
ARG APP_RELATIVE_PATH

WORKDIR /build

COPY ./${APP_RELATIVE_PATH}/requirements.txt .

RUN --mount=type=cache,target=/var/cache/pip \
    pip install                  \
      --cache-dir=/var/cache/pip \
      --target=lib               \
      -r requirements.txt



FROM python:3.10-slim as base-env
ARG APP_USER
ARG APP_RELATIVE_PATH
ARG MODULE
ARG SERVICE

ENV APP_BASE="/opt/${MODULE}"
ENV APP_ROOT="${APP_BASE}/${SERVICE}"
ENV APP_LIBS="/var/opt/${APP_RELATIVE_PATH}/lib"
ENV SHARED_LIBS_BASE="${APP_BASE}/lib"

ENV PATH="${PATH}:${APP_LIBS}/bin"
ENV PYTHONPATH="${PYTHONPATH}:${APP_LIBS}"

ENV PORT="8080"
ENV PROMETHEUS_MULTIPROC_DIR="/tmp/oasst-inference-prometheus-metrics"

RUN adduser               \
      --disabled-password \
      --no-create-home    \
      "${APP_USER}"

USER ${APP_USER}

RUN mkdir -p "${PROMETHEUS_MULTIPROC_DIR}"

WORKDIR ${APP_ROOT}


COPY --chown="${APP_USER}:${APP_USER}" --from=build /build/lib                     ${APP_LIBS}
COPY --chown="${APP_USER}:${APP_USER}"              ./${APP_RELATIVE_PATH}/alembic alembic
COPY --chown="${APP_USER}:${APP_USER}"              ./${APP_RELATIVE_PATH}/alembic.ini .
COPY --chown="${APP_USER}:${APP_USER}"              ./${APP_RELATIVE_PATH}/oasst_inference_server oasst_inference_server
COPY --chown="${APP_USER}:${APP_USER}"              ./${APP_RELATIVE_PATH}/main.py .
COPY --chown="${APP_USER}:${APP_USER}"              ./${APP_RELATIVE_PATH}/export.py .



FROM base-env as dev
ARG APP_USER


COPY --chown="${APP_USER}:${APP_USER}" ./oasst-shared ${SHARED_LIBS_BASE}/oasst-shared
COPY --chown="${APP_USER}:${APP_USER}" ./oasst-data ${SHARED_LIBS_BASE}/oasst-data

USER root
RUN --mount=type=cache,target=/var/cache/pip,from=build \
    pip install                  \
      --cache-dir=/var/cache/pip \
      -e "${SHARED_LIBS_BASE}/oasst-shared" "${SHARED_LIBS_BASE}/oasst-data"
USER ${APP_USER}


VOLUME [ "${APP_BASE}/lib/oasst-shared" ]
VOLUME [ "${APP_BASE}/lib/oasst-data" ]


CMD uvicorn main:app --reload --host 0.0.0.0 --port "${PORT}"



FROM base-env as prod
ARG APP_USER


COPY --chown="${APP_USER}:${APP_USER}" ./oasst-shared /tmp/lib/oasst-shared
COPY --chown="${APP_USER}:${APP_USER}" ./oasst-data /tmp/lib/oasst-data
RUN --mount=type=cache,target=/var/cache/pip,from=dev \
    pip install                  \
      --cache-dir=/var/cache/pip \
      --target="${APP_LIBS}"     \
      /tmp/lib/oasst-shared /tmp/lib/oasst-data

COPY --chown="${APP_USER}:${APP_USER}" ./inference/server/server_main.sh /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]
