"""restructure message_tree_state table

Revision ID: 92a367bb9f40
Revises: ba61fe17fb6e
Create Date: 2023-01-08 22:08:46.458195

"""
import sqlalchemy as sa
import sqlmodel
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "92a367bb9f40"
down_revision = "aac6b2f66006"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("message_tree_state")
    op.create_table(
        "message_tree_state",
        sa.Column("message_tree_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("goal_tree_size", sa.Integer(), nullable=False),
        sa.Column("max_depth", sa.Integer(), nullable=False),
        sa.Column("max_children_count", sa.Integer(), nullable=False),
        sa.<PERSON>umn("state", sqlmodel.sql.sqltypes.AutoString(length=128), nullable=False),
        sa.<PERSON>umn("active", sa.<PERSON>(), nullable=False),
        sa.Column("accepted_messages", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["message_tree_id"],
            ["message.id"],
        ),
        sa.PrimaryKeyConstraint("message_tree_id"),
    )
    op.create_index(op.f("ix_message_tree_state_active"), "message_tree_state", ["active"], unique=False)
    op.create_index(op.f("ix_message_tree_state_state"), "message_tree_state", ["state"], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_message_tree_state_state"), table_name="message_tree_state")
    op.drop_index(op.f("ix_message_tree_state_active"), table_name="message_tree_state")
    op.drop_table("message_tree_state")
    op.create_table(
        "message_tree_state",
        sa.Column("id", postgresql.UUID(as_uuid=True), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("message_tree_id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("state", sqlmodel.sql.sqltypes.AutoString(length=128), nullable=False),
        sa.Column("goal_tree_size", sa.Integer(), nullable=False),
        sa.Column("current_num_non_filtered_messages", sa.Integer(), nullable=False),
        sa.Column("max_depth", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_message_tree_state_message_tree_id"), "message_tree_state", ["message_tree_id"], unique=False
    )
    op.create_index("ix_message_tree_state_tree_id", "message_tree_state", ["message_tree_id"], unique=True)
    # ### end Alembic commands ###
