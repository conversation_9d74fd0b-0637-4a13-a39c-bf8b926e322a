"""move user_streak from user_stats to user table

Revision ID: 7b8f0011e0b0
Revises: 8a5feed819ee
Create Date: 2023-01-29 12:07:29.379326

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "7b8f0011e0b0"
down_revision = "49d8445b4c90"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user",
        sa.Column(
            "streak_last_day_date",
            sa.DateTime(timezone=True),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column("user", sa.Column("streak_days", sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column(
        "user", sa.Column("last_activity_date", sa.DateTime(timezone=True), autoincrement=False, nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user", "streak_days")
    op.drop_column("user", "streak_last_day_date")
    op.drop_column("user", "last_activity_date")
    # ### end Alembic commands ###
