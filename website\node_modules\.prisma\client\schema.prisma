datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl"]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  isNew         <PERSON>   @default(true)
  role          String    @default("general")
  paperackYes   <PERSON>olean   @default(false)
  paperackName  String    @default("")

  accounts Account[]
  sessions Session[]
  tasks    RegisteredTask[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model RegisteredTask {
  id   String @id @default(uuid())
  task Json

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  interaction TaskInteraction[]
}

model TaskInteraction {
  id      String @id @default(uuid())
  content Json

  task   RegisteredTask @relation(fields: [taskId], references: [id], onDelete: Cascade)
  taskId String
}
