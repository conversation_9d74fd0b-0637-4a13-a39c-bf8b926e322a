"""add rank and indices to user_stats

Revision ID: 0964ac95170d
Revises: 423557e869e4
Create Date: 2023-01-15 16:54:09.510018

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0964ac95170d"
down_revision = "423557e869e4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("user_stats", sa.Column("rank", sa.Integer(), nullable=True))
    op.create_index(
        "ix_user_stats__timeframe__rank__user_id", "user_stats", ["time_frame", "rank", "user_id"], unique=True
    )
    op.create_index("ix_user_stats__timeframe__user_id", "user_stats", ["time_frame", "user_id"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_user_stats__timeframe__user_id", table_name="user_stats")
    op.drop_index("ix_user_stats__timeframe__rank__user_id", table_name="user_stats")
    op.drop_column("user_stats", "rank")
    # ### end Alembic commands ###
