name: Test API Contract

on:
  push:
    branches:
      - main
    paths:
      - "oasst-shared/**"
      - "backend/**"
      - "website/**"
  pull_request:
    paths:
      - "oasst-shared/**"
      - "backend/**"
      - "website/**"
  workflow_call:

jobs:
  test-contract:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: "3.10"
          cache: "pip"
          cache-dependency-path: "**/requirements*.txt"
      - uses: actions/setup-node@v3
        with:
          node-version: 16

      - run: pip install ./oasst-shared[dev]

      - run: pip install ./oasst-data[dev]

      - run: cd backend && pip install -r requirements.txt

      - run: cd website && npm ci

      - run: ./scripts/backend-development/start-mock-server.sh

      - name: Run Python OasstApiClient contract tests
        run: ./scripts/oasst-shared-development/test.sh

      - name: Run JavaScript OasstApiClient contract tests
        run: ./scripts/frontend-development/run-contract-test.sh

      - run: ./scripts/backend-development/stop-mock-server.sh

      #- uses: stefanzweifel/git-auto-commit-action@v4
      #  with:
      #    file_pattern: "docs/docs/api/openapi.json"
      #    commit_message:
      #      update docs/docs/api/openapi.json by run ${{ github.run_id }}
