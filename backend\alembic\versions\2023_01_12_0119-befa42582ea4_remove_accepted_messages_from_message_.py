"""remove accepted_messages from message_tree_state

Revision ID: befa42582ea4
Revises: 05975b274a81
Create Date: 2023-01-12 01:19:59.654864

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "befa42582ea4"
down_revision = "05975b274a81"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("message_tree_state", "accepted_messages")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "message_tree_state", sa.Column("accepted_messages", sa.INTEGER(), autoincrement=False, nullable=False)
    )
    # ### end Alembic commands ###
