"""add review_count & ranking_count to message

Revision ID: 05975b274a81
Revises: 92a367bb9f40
Create Date: 2023-01-09 00:47:25.496036

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "05975b274a81"
down_revision = "92a367bb9f40"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("message", sa.Column("review_count", sa.Integer(), server_default=sa.text("0"), nullable=False))
    op.add_column("message", sa.Column("review_result", sa.<PERSON>(), server_default=sa.text("false"), nullable=False))
    op.add_column("message", sa.Column("ranking_count", sa.Integer(), server_default=sa.text("0"), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("message", "ranking_count")
    op.drop_column("message", "review_result")
    op.drop_column("message", "review_count")
    # ### end Alembic commands ###
