"""added MessageTreeState table

Revision ID: d4161e384f83
Revises: 8d269bc4fdbd
Create Date: 2023-01-05 11:44:02.630633

"""
import sqlalchemy as sa
import sqlmodel
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "d4161e384f83"
down_revision = "8d269bc4fdbd"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "message_tree_state",
        sa.Column("id", postgresql.UUID(as_uuid=True), server_default=sa.text("gen_random_uuid()"), nullable=False),
        sa.Column("created_date", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("deleted", sa.<PERSON>(), server_default=sa.text("false"), nullable=False),
        sa.Column("message_tree_id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("state", sqlmodel.sql.sqltypes.AutoString(length=128), nullable=False),
        sa.Column("goal_tree_size", sa.Integer(), nullable=False),
        sa.Column("current_num_non_filtered_messages", sa.Integer(), nullable=False),
        sa.Column("max_depth", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_message_tree_state_message_tree_id"), "message_tree_state", ["message_tree_id"], unique=False
    )
    op.create_index("ix_message_tree_state_tree_id", "message_tree_state", ["message_tree_id"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_message_tree_state_tree_id", table_name="message_tree_state")
    op.drop_index(op.f("ix_message_tree_state_message_tree_id"), table_name="message_tree_state")
    op.drop_table("message_tree_state")
    # ### end Alembic commands ###
