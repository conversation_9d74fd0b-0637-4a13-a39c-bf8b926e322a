{"name": "open-assistant-discord", "description": "This is a bot for the Open Assistant project. It is a Discord bot that allows you to interact with Open Assistant.", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "tsc && node .", "dev": "concurrently \"tsc --watch\" \"npx nodemon\"", "redis:start": "docker-compose up -d", "redis:stop": "docker-compose stop", "redis:logs": "docker-compose logs -f --tail=0"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "Apache-2.0", "private": true, "dependencies": {"@huggingface/inference": "^2.0.0", "axios": "^1.3.5", "chalk": "^5.2.0", "discord.js": "^14.7.1", "dotenv": "^16.0.3", "open-assistant.js": "^1.0.4", "redis": "^4.6.5"}, "devDependencies": {"@types/chalk": "^2.2.0", "@types/node": "^18.14.1", "concurrently": "^7.6.0", "nodemon": "^2.0.20", "typescript": "^4.9.5"}}