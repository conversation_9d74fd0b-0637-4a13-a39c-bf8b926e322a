---
title: Open Assistant Inference Backend Development (Hands-On Coding)
description: Open Assistant Inference Backend Development (Hands-On Coding)
authors: [yk]
tags: [open-assistant, youtube, live-coding]
image: https://img.youtube.com/vi/6OozhhI6U4g/0.jpg
---

import ReactPlayer from "react-player";

Open Assistant Inference Backend Development (Hands-On Coding)

Join me as I build streaming inference into the Hugging Face text generation
server, going through cuda, python, rust, grpc, websockets, server-sent events,
and more...

https://open-assistant.io

<ReactPlayer
  controls
  width="100%"
  url="https://www.youtube.com/embed/6OozhhI6U4g"
/>

<!--truncate-->
