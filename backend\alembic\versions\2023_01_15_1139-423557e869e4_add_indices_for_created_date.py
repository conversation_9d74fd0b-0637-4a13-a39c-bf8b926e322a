"""add indices for created_date

Revision ID: 423557e869e4
Revises: 7c98102efbca
Create Date: 2023-01-15 11:39:10.407859

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "423557e869e4"
down_revision = "7c98102efbca"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f("ix_message_created_date"), "message", ["created_date"], unique=False)
    op.create_index(op.f("ix_message_reaction_created_date"), "message_reaction", ["created_date"], unique=False)
    op.create_index(op.f("ix_text_labels_created_date"), "text_labels", ["created_date"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_text_labels_created_date"), table_name="text_labels")
    op.drop_index(op.f("ix_message_reaction_created_date"), table_name="message_reaction")
    op.drop_index(op.f("ix_message_created_date"), table_name="message")
    # ### end Alembic commands ###
