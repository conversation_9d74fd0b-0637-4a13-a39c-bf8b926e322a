{"openapi": "3.0.2", "info": {"title": "open-assistant backend", "version": "0.1.0"}, "paths": {"/api/v1/tasks/": {"post": {"tags": ["tasks"], "summary": "Request Task", "description": "Create new task.", "operationId": "request_task_api_v1_tasks__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Request Task Api V1 Tasks  Post", "anyOf": [{"$ref": "#/components/schemas/TaskDone"}, {"$ref": "#/components/schemas/SummarizeStoryTask"}, {"$ref": "#/components/schemas/RateSummaryTask"}, {"$ref": "#/components/schemas/InitialPromptTask"}, {"$ref": "#/components/schemas/ReplyToConversationTask"}, {"$ref": "#/components/schemas/PrompterReplyTask"}, {"$ref": "#/components/schemas/AssistantReplyTask"}, {"$ref": "#/components/schemas/RankInitialPromptsTask"}, {"$ref": "#/components/schemas/RankConversationRepliesTask"}, {"$ref": "#/components/schemas/RankPrompterRepliesTask"}, {"$ref": "#/components/schemas/RankAssistantRepliesTask"}, {"$ref": "#/components/schemas/LabelInitialPromptTask"}, {"$ref": "#/components/schemas/LabelConversationReplyTask"}, {"$ref": "#/components/schemas/LabelPrompterReplyTask"}, {"$ref": "#/components/schemas/LabelAssistantReplyTask"}]}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/tasks/availability": {"post": {"tags": ["tasks"], "summary": "Tasks Availability", "operationId": "tasks_availability_api_v1_tasks_availability_post", "parameters": [{"required": false, "schema": {"title": "<PERSON>", "type": "string", "default": "en"}, "name": "lang", "in": "query"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Tasks Availability Api V1 Tasks Availability Post", "type": "object", "additionalProperties": {"type": "integer"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/tasks/{task_id}/ack": {"post": {"tags": ["tasks"], "summary": "Tasks Acknowledge", "description": "The frontend acknowledges a task.", "operationId": "tasks_acknowledge_api_v1_tasks__task_id__ack_post", "parameters": [{"required": true, "schema": {"title": "Task Id", "type": "string", "format": "uuid"}, "name": "task_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskAck"}}}, "required": true}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}, {"oasst-user": []}, {"oasst-user": []}]}}, "/api/v1/tasks/{task_id}/nack": {"post": {"tags": ["tasks"], "summary": "Tasks Acknowledge Failure", "description": "The frontend reports failure to implement a task.", "operationId": "tasks_acknowledge_failure_api_v1_tasks__task_id__nack_post", "parameters": [{"required": true, "schema": {"title": "Task Id", "type": "string", "format": "uuid"}, "name": "task_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskNAck"}}}, "required": true}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}, {"oasst-user": []}, {"oasst-user": []}]}}, "/api/v1/tasks/interaction": {"post": {"tags": ["tasks"], "summary": "Tasks Interaction", "description": "The frontend reports an interaction.", "operationId": "tasks_interaction_api_v1_tasks_interaction_post", "requestBody": {"content": {"application/json": {"schema": {"title": "Interaction", "anyOf": [{"$ref": "#/components/schemas/TextReplyToMessage"}, {"$ref": "#/components/schemas/MessageRating"}, {"$ref": "#/components/schemas/MessageRanking"}, {"$ref": "#/components/schemas/TextLabels"}]}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskDone"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/tasks/close": {"post": {"tags": ["tasks"], "summary": "Close Collective Task", "operationId": "close_collective_task_api_v1_tasks_close_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskClose"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskDone"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/text_labels/": {"post": {"tags": ["text_labels"], "summary": "Label Text", "description": "Label a piece of text.", "operationId": "label_text_api_v1_text_labels__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TextLabels"}}}, "required": true}, "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/text_labels/valid_labels": {"get": {"tags": ["text_labels"], "summary": "<PERSON> Val<PERSON>", "operationId": "get_valid_lables_api_v1_text_labels_valid_labels_get", "parameters": [{"required": false, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/text_labels/report_labels": {"get": {"tags": ["text_labels"], "summary": "Get Report Lables", "operationId": "get_report_lables_api_v1_text_labels_report_labels_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/messages/": {"get": {"tags": ["messages"], "summary": "Query Messages", "description": "Query messages.", "operationId": "query_messages_api_v1_messages__get", "parameters": [{"required": false, "schema": {"title": "Auth Method", "type": "string"}, "name": "auth_method", "in": "query"}, {"required": false, "schema": {"title": "Username", "type": "string"}, "name": "username", "in": "query"}, {"required": false, "schema": {"title": "Api Client Id", "type": "string"}, "name": "api_client_id", "in": "query"}, {"required": false, "schema": {"title": "Max Count", "maximum": 1000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 10}, "name": "max_count", "in": "query"}, {"required": false, "schema": {"title": "Start Date", "type": "string", "format": "date-time"}, "name": "start_date", "in": "query"}, {"required": false, "schema": {"title": "End Date", "type": "string", "format": "date-time"}, "name": "end_date", "in": "query"}, {"required": false, "schema": {"title": "Only Roots", "type": "boolean", "default": false}, "name": "only_roots", "in": "query"}, {"required": false, "schema": {"title": "Desc", "type": "boolean", "default": true}, "name": "desc", "in": "query"}, {"required": false, "schema": {"title": "Allow Deleted", "type": "boolean", "default": false}, "name": "allow_deleted", "in": "query"}, {"required": false, "schema": {"title": "<PERSON>", "type": "string"}, "name": "lang", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Query Messages Api V1 Messages  Get", "type": "array", "items": {"$ref": "#/components/schemas/Message"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/cursor": {"get": {"tags": ["messages"], "summary": "Get Messages Cursor", "operationId": "get_messages_cursor_api_v1_messages_cursor_get", "parameters": [{"required": false, "schema": {"title": "Before", "type": "string"}, "name": "before", "in": "query"}, {"required": false, "schema": {"title": "After", "type": "string"}, "name": "after", "in": "query"}, {"required": false, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "query"}, {"required": false, "schema": {"title": "Auth Method", "type": "string"}, "name": "auth_method", "in": "query"}, {"required": false, "schema": {"title": "Username", "type": "string"}, "name": "username", "in": "query"}, {"required": false, "schema": {"title": "Api Client Id", "type": "string"}, "name": "api_client_id", "in": "query"}, {"required": false, "schema": {"title": "Only Roots", "type": "boolean", "default": false}, "name": "only_roots", "in": "query"}, {"required": false, "schema": {"title": "Include Deleted", "type": "boolean", "default": false}, "name": "include_deleted", "in": "query"}, {"required": false, "schema": {"title": "Max Count", "maximum": 1000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 10}, "name": "max_count", "in": "query"}, {"required": false, "schema": {"title": "Desc", "type": "boolean", "default": false}, "name": "desc", "in": "query"}, {"required": false, "schema": {"title": "<PERSON>", "type": "string"}, "name": "lang", "in": "query"}, {"required": false, "schema": {"title": "Include User", "type": "boolean"}, "name": "include_user", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessagePage"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/{message_id}": {"get": {"tags": ["messages"], "summary": "Get Message", "description": "Get a message by its internal ID.", "operationId": "get_message_api_v1_messages__message_id__get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}, "delete": {"tags": ["messages"], "summary": "Mark Message Deleted", "operationId": "mark_message_deleted_api_v1_messages__message_id__delete", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/{message_id}/conversation": {"get": {"tags": ["messages"], "summary": "Get Conv", "description": "Get a conversation from the tree root and up to the message with given internal ID.", "operationId": "get_conv_api_v1_messages__message_id__conversation_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Conversation"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/{message_id}/tree": {"get": {"tags": ["messages"], "summary": "Get Tree", "description": "Get all messages belonging to the same message tree.", "operationId": "get_tree_api_v1_messages__message_id__tree_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}, {"required": false, "schema": {"title": "Include Spam", "type": "boolean", "default": true}, "name": "include_spam", "in": "query"}, {"required": false, "schema": {"title": "Include Deleted", "type": "boolean", "default": false}, "name": "include_deleted", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageTree"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/{message_id}/tree/state": {"get": {"tags": ["messages"], "summary": "Get Message Tree State", "operationId": "get_message_tree_state_api_v1_messages__message_id__tree_state_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageTreeStateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}, "put": {"tags": ["messages"], "summary": "Put Message Tree State", "operationId": "put_message_tree_state_api_v1_messages__message_id__tree_state_put", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}, {"required": true, "schema": {"title": "Halt", "type": "boolean"}, "name": "halt", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageTreeStateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/{message_id}/children": {"get": {"tags": ["messages"], "summary": "Get Children", "description": "Get all messages belonging to the same message tree.", "operationId": "get_children_api_v1_messages__message_id__children_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Get Children Api V1 Messages  Message Id  Children Get", "type": "array", "items": {"$ref": "#/components/schemas/Message"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/{message_id}/descendants": {"get": {"tags": ["messages"], "summary": "Get Descendants", "description": "Get a subtree which starts with this message.", "operationId": "get_descendants_api_v1_messages__message_id__descendants_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageTree"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/{message_id}/longest_conversation_in_tree": {"get": {"tags": ["messages"], "summary": "Get Longest Conv", "description": "Get the longest conversation from the tree of the message.", "operationId": "get_longest_conv_api_v1_messages__message_id__longest_conversation_in_tree_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Conversation"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/{message_id}/max_children_in_tree": {"get": {"tags": ["messages"], "summary": "Get Max Children", "description": "Get message with the most children from the tree of the provided message.", "operationId": "get_max_children_api_v1_messages__message_id__max_children_in_tree_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageTree"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/{message_id}/undelete": {"put": {"tags": ["messages"], "summary": "Undelete Message", "operationId": "undelete_message_api_v1_messages__message_id__undelete_put", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/messages/{message_id}/emoji": {"post": {"tags": ["messages"], "summary": "Post Message Emoji", "description": "Toggle, add or remove message emoji.", "operationId": "post_message_emoji_api_v1_messages__message_id__emoji_post", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageEmojiRequest"}}}, "required": true}, "responses": {"202": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_messages/{message_id}": {"get": {"tags": ["frontend_messages"], "summary": "Get Message By Frontend Id", "description": "Get a message by its frontend ID.", "operationId": "get_message_by_frontend_id_api_v1_frontend_messages__message_id__get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_messages/{message_id}/conversation": {"get": {"tags": ["frontend_messages"], "summary": "Get Conv By Frontend Id", "description": "Get a conversation from the tree root and up to the message with given frontend ID.", "operationId": "get_conv_by_frontend_id_api_v1_frontend_messages__message_id__conversation_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Conversation"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_messages/{message_id}/tree": {"get": {"tags": ["frontend_messages"], "summary": "Get Tree By Frontend Id", "description": "Get all messages belonging to the same message tree.\nMessage is identified by its frontend ID.", "operationId": "get_tree_by_frontend_id_api_v1_frontend_messages__message_id__tree_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string"}, "name": "message_id", "in": "path"}, {"required": false, "schema": {"title": "Include Spam", "type": "boolean", "default": true}, "name": "include_spam", "in": "query"}, {"required": false, "schema": {"title": "Include Deleted", "type": "boolean", "default": false}, "name": "include_deleted", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageTree"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_messages/{message_id}/children": {"get": {"tags": ["frontend_messages"], "summary": "Get Children By Frontend Id", "description": "Get all messages belonging to the same message tree.", "operationId": "get_children_by_frontend_id_api_v1_frontend_messages__message_id__children_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Get Children By Frontend Id Api V1 Frontend Messages  Message Id  Children Get", "type": "array", "items": {"$ref": "#/components/schemas/Message"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_messages/{message_id}/descendants": {"get": {"tags": ["frontend_messages"], "summary": "Get Descendants By Frontend Id", "description": "Get a subtree which starts with this message.\nThe message is identified by its frontend ID.", "operationId": "get_descendants_by_frontend_id_api_v1_frontend_messages__message_id__descendants_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageTree"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_messages/{message_id}/longest_conversation_in_tree": {"get": {"tags": ["frontend_messages"], "summary": "Get Longest Conv By Frontend Id", "description": "Get the longest conversation from the tree of the message.\nThe message is identified by its frontend ID.", "operationId": "get_longest_conv_by_frontend_id_api_v1_frontend_messages__message_id__longest_conversation_in_tree_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Conversation"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_messages/{message_id}/max_children_in_tree": {"get": {"tags": ["frontend_messages"], "summary": "Get Max Children By Frontend Id", "description": "Get message with the most children from the tree of the provided message.\nThe message is identified by its frontend ID.", "operationId": "get_max_children_by_frontend_id_api_v1_frontend_messages__message_id__max_children_in_tree_get", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageTree"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/users/by_username": {"get": {"tags": ["users"], "summary": "Get Users Ordered By Username", "operationId": "get_users_ordered_by_username_api_v1_users_by_username_get", "parameters": [{"required": false, "schema": {"title": "Api Client Id", "type": "string", "format": "uuid"}, "name": "api_client_id", "in": "query"}, {"required": false, "schema": {"title": "Gte Username", "type": "string"}, "name": "gte_username", "in": "query"}, {"required": false, "schema": {"title": "Gt Id", "type": "string", "format": "uuid"}, "name": "gt_id", "in": "query"}, {"required": false, "schema": {"title": "<PERSON><PERSON>", "type": "string"}, "name": "lte_username", "in": "query"}, {"required": false, "schema": {"title": "Lt Id", "type": "string", "format": "uuid"}, "name": "lt_id", "in": "query"}, {"required": false, "schema": {"title": "Search Text", "type": "string"}, "name": "search_text", "in": "query"}, {"required": false, "schema": {"title": "Auth Method", "type": "string"}, "name": "auth_method", "in": "query"}, {"required": false, "schema": {"title": "Max Count", "maximum": 10000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 100}, "name": "max_count", "in": "query"}, {"required": false, "schema": {"title": "Desc", "type": "boolean", "default": false}, "name": "desc", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Get Users Ordered By Username Api V1 Users By Username Get", "type": "array", "items": {"$ref": "#/components/schemas/FrontEndUser"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/users/by_display_name": {"get": {"tags": ["users"], "summary": "Get Users Ordered By Display Name", "operationId": "get_users_ordered_by_display_name_api_v1_users_by_display_name_get", "parameters": [{"required": false, "schema": {"title": "Api Client Id", "type": "string", "format": "uuid"}, "name": "api_client_id", "in": "query"}, {"required": false, "schema": {"title": "Gte Display Name", "type": "string"}, "name": "gte_display_name", "in": "query"}, {"required": false, "schema": {"title": "Gt Id", "type": "string", "format": "uuid"}, "name": "gt_id", "in": "query"}, {"required": false, "schema": {"title": "<PERSON>e Display Name", "type": "string"}, "name": "lte_display_name", "in": "query"}, {"required": false, "schema": {"title": "Lt Id", "type": "string", "format": "uuid"}, "name": "lt_id", "in": "query"}, {"required": false, "schema": {"title": "Auth Method", "type": "string"}, "name": "auth_method", "in": "query"}, {"required": false, "schema": {"title": "Search Text", "type": "string"}, "name": "search_text", "in": "query"}, {"required": false, "schema": {"title": "Max Count", "maximum": 10000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 100}, "name": "max_count", "in": "query"}, {"required": false, "schema": {"title": "Desc", "type": "boolean", "default": false}, "name": "desc", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Get Users Ordered By Display Name Api V1 Users By Display Name Get", "type": "array", "items": {"$ref": "#/components/schemas/FrontEndUser"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/users/cursor": {"get": {"tags": ["users"], "summary": "Get Users Cursor", "operationId": "get_users_cursor_api_v1_users_cursor_get", "parameters": [{"required": false, "schema": {"title": "Before", "type": "string"}, "name": "before", "in": "query"}, {"required": false, "schema": {"title": "After", "type": "string"}, "name": "after", "in": "query"}, {"required": false, "schema": {"title": "Sort Key", "maxLength": 32, "type": "string", "default": "username"}, "name": "sort_key", "in": "query"}, {"required": false, "schema": {"title": "Max Count", "maximum": 10000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 100}, "name": "max_count", "in": "query"}, {"required": false, "schema": {"title": "Api Client Id", "type": "string", "format": "uuid"}, "name": "api_client_id", "in": "query"}, {"required": false, "schema": {"title": "Search Text", "type": "string"}, "name": "search_text", "in": "query"}, {"required": false, "schema": {"title": "Auth Method", "type": "string"}, "name": "auth_method", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrontEndUserPage"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/users/{user_id}": {"get": {"tags": ["users"], "summary": "Get User", "description": "Get a user by global user ID. Only trusted clients can resolve users they did not register.", "operationId": "get_user_api_v1_users__user_id__get", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}, {"required": false, "schema": {"title": "Api Client Id", "type": "string", "format": "uuid"}, "name": "api_client_id", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrontEndUser"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}, "put": {"tags": ["users"], "summary": "Update User", "description": "Update a user by global user ID. Only trusted clients can update users.", "operationId": "update_user_api_v1_users__user_id__put", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}, {"required": false, "schema": {"title": "Enabled", "type": "boolean"}, "name": "enabled", "in": "query"}, {"required": false, "schema": {"title": "Notes", "type": "string"}, "name": "notes", "in": "query"}, {"required": false, "schema": {"title": "Show On Leaderboard", "type": "boolean"}, "name": "show_on_leaderboard", "in": "query"}, {"required": false, "schema": {"title": "Tos Acceptance", "type": "boolean"}, "name": "tos_acceptance", "in": "query"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}, "delete": {"tags": ["users"], "summary": "Delete User", "description": "Delete a user by global user ID. Only trusted clients can delete users.\nUser deletion anonymises the data of the user.", "operationId": "delete_user_api_v1_users__user_id__delete", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/users/{user_id}/messages": {"get": {"tags": ["users"], "summary": "Query User Messages", "description": "Query user messages.", "operationId": "query_user_messages_api_v1_users__user_id__messages_get", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}, {"required": false, "schema": {"title": "Api Client Id", "type": "string", "format": "uuid"}, "name": "api_client_id", "in": "query"}, {"required": false, "schema": {"title": "Max Count", "maximum": 1000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 10}, "name": "max_count", "in": "query"}, {"required": false, "schema": {"title": "Start Date", "type": "string", "format": "date-time"}, "name": "start_date", "in": "query"}, {"required": false, "schema": {"title": "End Date", "type": "string", "format": "date-time"}, "name": "end_date", "in": "query"}, {"required": false, "schema": {"title": "Only Roots", "type": "boolean", "default": false}, "name": "only_roots", "in": "query"}, {"required": false, "schema": {"title": "Desc", "type": "boolean", "default": true}, "name": "desc", "in": "query"}, {"required": false, "schema": {"title": "Include Deleted", "type": "boolean", "default": false}, "name": "include_deleted", "in": "query"}, {"required": false, "schema": {"title": "<PERSON>", "type": "string"}, "name": "lang", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Query User Messages Api V1 Users  User Id  Messages Get", "type": "array", "items": {"$ref": "#/components/schemas/Message"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}, "delete": {"tags": ["users"], "summary": "Mark User Messages Deleted", "operationId": "mark_user_messages_deleted_api_v1_users__user_id__messages_delete", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/users/{user_id}/messages/cursor": {"get": {"tags": ["users"], "summary": "Query User Messages Cursor", "operationId": "query_user_messages_cursor_api_v1_users__user_id__messages_cursor_get", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}, {"required": false, "schema": {"title": "Before", "type": "string"}, "name": "before", "in": "query"}, {"required": false, "schema": {"title": "After", "type": "string"}, "name": "after", "in": "query"}, {"required": false, "schema": {"title": "Only Roots", "type": "boolean", "default": false}, "name": "only_roots", "in": "query"}, {"required": false, "schema": {"title": "Include Deleted", "type": "boolean", "default": false}, "name": "include_deleted", "in": "query"}, {"required": false, "schema": {"title": "Max Count", "maximum": 1000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 10}, "name": "max_count", "in": "query"}, {"required": false, "schema": {"title": "Desc", "type": "boolean", "default": false}, "name": "desc", "in": "query"}, {"required": false, "schema": {"title": "<PERSON>", "type": "string"}, "name": "lang", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessagePage"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/users/{user_id}/stats": {"get": {"tags": ["users"], "summary": "Query User Stats", "operationId": "query_user_stats_api_v1_users__user_id__stats_get", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Query User Stats Api V1 Users  User Id  Stats Get", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/UserScore"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/users/{user_id}/stats/{time_frame}": {"get": {"tags": ["users"], "summary": "Query User Stats Timeframe", "operationId": "query_user_stats_timeframe_api_v1_users__user_id__stats__time_frame__get", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}, {"required": true, "schema": {"$ref": "#/components/schemas/UserStatsTimeFrame"}, "name": "time_frame", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserScore"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/users/{user_id}/stats/{time_frame}/window": {"get": {"tags": ["users"], "summary": "Query User Stats Timeframe Window", "operationId": "query_user_stats_timeframe_window_api_v1_users__user_id__stats__time_frame__window_get", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}, {"required": true, "schema": {"$ref": "#/components/schemas/UserStatsTimeFrame"}, "name": "time_frame", "in": "path"}, {"required": false, "schema": {"title": "Window Size", "maximum": 100.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 5}, "name": "window_size", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaderboardStats"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_users/": {"get": {"tags": ["frontend_users"], "summary": "Get Users Ordered By Username", "operationId": "get_users_ordered_by_username_api_v1_frontend_users__get", "parameters": [{"required": false, "schema": {"title": "Api Client Id", "type": "string", "format": "uuid"}, "name": "api_client_id", "in": "query"}, {"required": false, "schema": {"title": "Gte Username", "type": "string"}, "name": "gte_username", "in": "query"}, {"required": false, "schema": {"title": "Gt Id", "type": "string", "format": "uuid"}, "name": "gt_id", "in": "query"}, {"required": false, "schema": {"title": "<PERSON><PERSON>", "type": "string"}, "name": "lte_username", "in": "query"}, {"required": false, "schema": {"title": "Lt Id", "type": "string", "format": "uuid"}, "name": "lt_id", "in": "query"}, {"required": false, "schema": {"title": "Search Text", "type": "string"}, "name": "search_text", "in": "query"}, {"required": false, "schema": {"title": "Auth Method", "type": "string"}, "name": "auth_method", "in": "query"}, {"required": false, "schema": {"title": "Max Count", "maximum": 10000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 100}, "name": "max_count", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Get Users Ordered By Username Api V1 Frontend Users  Get", "type": "array", "items": {"$ref": "#/components/schemas/FrontEndUser"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "deprecated": true, "security": [{"api-key": []}, {"api-key": []}]}, "post": {"tags": ["frontend_users"], "summary": "Create Frontend User", "operationId": "create_frontend_user_api_v1_frontend_users__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFrontendUserRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrontEndUser"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_users/{auth_method}/{username}": {"get": {"tags": ["frontend_users"], "summary": "Query Frontend User", "description": "Query frontend user.", "operationId": "query_frontend_user_api_v1_frontend_users__auth_method___username__get", "parameters": [{"required": true, "schema": {"title": "Auth Method", "type": "string"}, "name": "auth_method", "in": "path"}, {"required": true, "schema": {"title": "Username", "type": "string"}, "name": "username", "in": "path"}, {"required": false, "schema": {"title": "Api Client Id", "type": "string", "format": "uuid"}, "name": "api_client_id", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrontEndUser"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_users/{auth_method}/{username}/messages": {"get": {"tags": ["frontend_users"], "summary": "Query Frontend User Messages", "description": "Query frontend user messages.", "operationId": "query_frontend_user_messages_api_v1_frontend_users__auth_method___username__messages_get", "parameters": [{"required": true, "schema": {"title": "Auth Method", "type": "string"}, "name": "auth_method", "in": "path"}, {"required": true, "schema": {"title": "Username", "type": "string"}, "name": "username", "in": "path"}, {"required": false, "schema": {"title": "Api Client Id", "type": "string", "format": "uuid"}, "name": "api_client_id", "in": "query"}, {"required": false, "schema": {"title": "Max Count", "maximum": 1000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 10}, "name": "max_count", "in": "query"}, {"required": false, "schema": {"title": "Start Date", "type": "string", "format": "date-time"}, "name": "start_date", "in": "query"}, {"required": false, "schema": {"title": "End Date", "type": "string", "format": "date-time"}, "name": "end_date", "in": "query"}, {"required": false, "schema": {"title": "Only Roots", "type": "boolean", "default": false}, "name": "only_roots", "in": "query"}, {"required": false, "schema": {"title": "Desc", "type": "boolean", "default": true}, "name": "desc", "in": "query"}, {"required": false, "schema": {"title": "Include Deleted", "type": "boolean", "default": false}, "name": "include_deleted", "in": "query"}, {"required": false, "schema": {"title": "<PERSON>", "type": "string"}, "name": "lang", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Query Frontend User Messages Api V1 Frontend Users  Auth Method   Username  Messages Get", "type": "array", "items": {"$ref": "#/components/schemas/Message"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}, "delete": {"tags": ["frontend_users"], "summary": "Mark <PERSON>end User Messages Deleted", "operationId": "mark_frontend_user_messages_deleted_api_v1_frontend_users__auth_method___username__messages_delete", "parameters": [{"required": true, "schema": {"title": "Auth Method", "type": "string"}, "name": "auth_method", "in": "path"}, {"required": true, "schema": {"title": "Username", "type": "string"}, "name": "username", "in": "path"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/frontend_users/{auth_method}/{username}/messages/cursor": {"get": {"tags": ["frontend_users"], "summary": "Query Frontend User Messages Cursor", "operationId": "query_frontend_user_messages_cursor_api_v1_frontend_users__auth_method___username__messages_cursor_get", "parameters": [{"required": true, "schema": {"title": "Auth Method", "type": "string"}, "name": "auth_method", "in": "path"}, {"required": true, "schema": {"title": "Username", "type": "string"}, "name": "username", "in": "path"}, {"required": false, "schema": {"title": "Before", "type": "string"}, "name": "before", "in": "query"}, {"required": false, "schema": {"title": "After", "type": "string"}, "name": "after", "in": "query"}, {"required": false, "schema": {"title": "Only Roots", "type": "boolean", "default": false}, "name": "only_roots", "in": "query"}, {"required": false, "schema": {"title": "Include Deleted", "type": "boolean", "default": false}, "name": "include_deleted", "in": "query"}, {"required": false, "schema": {"title": "Max Count", "maximum": 1000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 10}, "name": "max_count", "in": "query"}, {"required": false, "schema": {"title": "Desc", "type": "boolean", "default": false}, "name": "desc", "in": "query"}, {"required": false, "schema": {"title": "<PERSON>", "type": "string"}, "name": "lang", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessagePage"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/stats/": {"get": {"tags": ["stats"], "summary": "Get Message Stats", "operationId": "get_message_stats_api_v1_stats__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemStats"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/stats/tree_manager/state_counts": {"get": {"tags": ["stats"], "summary": "Get Tree Manager  State Counts", "operationId": "get_tree_manager__state_counts_api_v1_stats_tree_manager_state_counts_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Get Tree Manager  State Counts Api V1 Stats Tree Manager State Counts Get", "type": "object", "additionalProperties": {"type": "integer"}}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/stats/tree_manager/message_counts": {"get": {"tags": ["stats"], "summary": "Get Tree Manager  Message Counts", "operationId": "get_tree_manager__message_counts_api_v1_stats_tree_manager_message_counts_get", "parameters": [{"required": false, "schema": {"title": "Only Active", "type": "boolean", "default": true}, "name": "only_active", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Get Tree Manager  Message Counts Api V1 Stats Tree Manager Message Counts Get", "type": "array", "items": {"$ref": "#/components/schemas/TreeMessageCountStats"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/stats/tree_manager": {"get": {"tags": ["stats"], "summary": "Get Tree Manager  Stats", "operationId": "get_tree_manager__stats_api_v1_stats_tree_manager_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TreeManagerStats"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/stats/cached/{name}": {"get": {"tags": ["stats"], "summary": "Get Cached Stats", "operationId": "get_cached_stats_api_v1_stats_cached__name__get", "parameters": [{"required": true, "schema": {"$ref": "#/components/schemas/CachedStatsName"}, "name": "name", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CachedStatsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/stats/cached": {"get": {"tags": ["stats"], "summary": "Get Cached Stats All", "operationId": "get_cached_stats_all_api_v1_stats_cached_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllCachedStatsResponse"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/stats/cached/update": {"post": {"tags": ["stats"], "summary": "Update Cached Stats", "operationId": "update_cached_stats_api_v1_stats_cached_update_post", "responses": {"204": {"description": "Successful Response"}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/leaderboards/{time_frame}": {"get": {"tags": ["leaderboards"], "summary": "Get Leaderboard", "operationId": "get_leaderboard_api_v1_leaderboards__time_frame__get", "parameters": [{"required": true, "schema": {"$ref": "#/components/schemas/UserStatsTimeFrame"}, "name": "time_frame", "in": "path"}, {"required": false, "schema": {"title": "Max Count", "maximum": 10000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 100}, "name": "max_count", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeaderboardStats"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"oasst-user": []}, {"oasst-user": []}, {"api-key": []}, {"api-key": []}]}}, "/api/v1/leaderboards/update/{time_frame}": {"post": {"tags": ["leaderboards"], "summary": "Update Leaderboard Time Frame", "operationId": "update_leaderboard_time_frame_api_v1_leaderboards_update__time_frame__post", "parameters": [{"required": true, "schema": {"$ref": "#/components/schemas/UserStatsTimeFrame"}, "name": "time_frame", "in": "path"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/leaderboards/update": {"post": {"tags": ["leaderboards"], "summary": "Update Leaderboards All", "operationId": "update_leaderboards_all_api_v1_leaderboards_update_post", "responses": {"204": {"description": "Successful Response"}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/trollboards/{time_frame}": {"get": {"tags": ["trollboards"], "summary": "Get Trollboard", "operationId": "get_trollboard_api_v1_trollboards__time_frame__get", "parameters": [{"required": true, "schema": {"$ref": "#/components/schemas/UserStatsTimeFrame"}, "name": "time_frame", "in": "path"}, {"required": false, "schema": {"title": "Max Count", "maximum": 10000.0, "exclusiveMinimum": 0.0, "type": "integer", "default": 100}, "name": "max_count", "in": "query"}, {"required": false, "schema": {"title": "Enabled", "type": "boolean"}, "name": "enabled", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrollboardStats"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/hf/text_toxicity": {"get": {"tags": ["hugging_face"], "summary": "Get Text Toxicity", "description": "Get the Message Toxicity from HuggingFace Roberta model.\n\nArgs:\n    msg (str): the message that we want to analyze.\n    api_client (ApiClient, optional): authentication of the user of the request.\n        Defaults to Depends(deps.get_trusted_api_client).\n\nReturns:\n    ToxicityClassification: the score of toxicity of the message.", "operationId": "get_text_toxicity_api_v1_hf_text_toxicity_get", "parameters": [{"required": true, "schema": {"title": "Msg", "type": "string"}, "name": "msg", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/admin/api_client": {"post": {"tags": ["admin"], "summary": "Create Api Client", "operationId": "create_api_client_api_v1_admin_api_client_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateApiClientRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Create Api Client Api V1 Admin Api Client Post", "type": "string"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/backend_settings/full": {"get": {"tags": ["admin"], "summary": "Get Backend Settings Full", "operationId": "get_backend_settings_full_api_v1_admin_backend_settings_full_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Settings"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/admin/backend_settings/public": {"get": {"tags": ["admin"], "summary": "Get Backend Settings Public", "operationId": "get_backend_settings_public_api_v1_admin_backend_settings_public_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicSettings"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/admin/purge_user/{user_id}": {"post": {"tags": ["admin"], "summary": "Purge User", "operationId": "purge_user_api_v1_admin_purge_user__user_id__post", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}, {"required": false, "schema": {"title": "Preview", "type": "boolean", "default": true}, "name": "preview", "in": "query"}, {"required": false, "schema": {"title": "Ban", "type": "boolean", "default": true}, "name": "ban", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PurgeResultModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/admin/purge_user/{user_id}/messages": {"post": {"tags": ["admin"], "summary": "Purge User Messages", "operationId": "purge_user_messages_api_v1_admin_purge_user__user_id__messages_post", "parameters": [{"required": true, "schema": {"title": "User Id", "type": "string", "format": "uuid"}, "name": "user_id", "in": "path"}, {"required": false, "schema": {"title": "Purge Initial Prompts", "type": "boolean", "default": false}, "name": "purge_initial_prompts", "in": "query"}, {"required": false, "schema": {"title": "Min Date", "type": "string", "format": "date-time"}, "name": "min_date", "in": "query"}, {"required": false, "schema": {"title": "Max Date", "type": "string", "format": "date-time"}, "name": "max_date", "in": "query"}, {"required": false, "schema": {"title": "Preview", "type": "boolean", "default": true}, "name": "preview", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PurgeResultModel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/admin/flagged_messages": {"get": {"tags": ["admin"], "summary": "Get Flagged Messages", "operationId": "get_flagged_messages_api_v1_admin_flagged_messages_get", "parameters": [{"required": true, "schema": {"title": "Max Count", "type": "integer"}, "name": "max_count", "in": "query"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Get Flagged Messages Api V1 Admin Flagged Messages Get", "type": "array", "items": {"$ref": "#/components/schemas/FlaggedMessageResponse"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/admin/admin/flagged_messages/{message_id}/processed": {"post": {"tags": ["admin"], "summary": "Process Flagged Messages", "operationId": "process_flagged_messages_api_v1_admin_admin_flagged_messages__message_id__processed_post", "parameters": [{"required": true, "schema": {"title": "Message Id", "type": "string", "format": "uuid"}, "name": "message_id", "in": "path"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FlaggedMessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"api-key": []}, {"api-key": []}]}}, "/api/v1/auth/check": {"get": {"tags": ["auth"], "summary": "Auth Check", "description": "Returns the user's email if it can be decrypted.", "operationId": "auth_check_api_v1_auth_check_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"title": "Response Auth Check Api V1 Auth Check Get", "type": "string"}}}}}, "security": [{"APIKeyCookie": []}]}}, "/metrics": {"get": {"summary": "Metrics", "description": "Endpoint that serves Prometheus metrics.", "operationId": "metrics_metrics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AllCachedStatsResponse": {"title": "AllCachedStatsResponse", "required": ["stats_by_name"], "type": "object", "properties": {"stats_by_name": {"title": "Stats By Name", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/CachedStatsResponse"}}}}, "AssistantReplyTask": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "required": ["conversation"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["assistant_reply"], "type": "string", "default": "assistant_reply"}, "conversation": {"$ref": "#/components/schemas/Conversation"}}, "description": "A task to prompt the user to act as the assistant."}, "CachedStatsName": {"title": "CachedStatsName", "enum": ["human_messages_by_lang", "human_messages_by_role", "message_trees_by_state", "message_trees_states_by_lang", "users_accepted_tos"], "type": "string", "description": "An enumeration."}, "CachedStatsResponse": {"title": "CachedStatsResponse", "required": ["name", "last_updated", "stats"], "type": "object", "properties": {"name": {"title": "Name", "anyOf": [{"$ref": "#/components/schemas/CachedStatsName"}, {"type": "string"}]}, "last_updated": {"title": "Last Updated", "type": "string", "format": "date-time"}, "stats": {"title": "Stats", "anyOf": [{"type": "object"}, {"type": "array", "items": {}}]}}}, "Conversation": {"title": "Conversation", "type": "object", "properties": {"messages": {"title": "Messages", "type": "array", "items": {"$ref": "#/components/schemas/ConversationMessage"}, "default": []}}, "description": "Represents a conversation between the prompter and the assistant."}, "ConversationMessage": {"title": "ConversationMessage", "required": ["text", "is_assistant"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "user_id": {"title": "User Id", "type": "string", "format": "uuid"}, "frontend_message_id": {"title": "Frontend Message Id", "type": "string"}, "text": {"title": "Text", "type": "string"}, "lang": {"title": "<PERSON>", "type": "string"}, "is_assistant": {"title": "Is Assistant", "type": "boolean"}, "emojis": {"title": "Emojis", "type": "object", "additionalProperties": {"type": "integer"}}, "user_emojis": {"title": "User <PERSON>s", "type": "array", "items": {"type": "string"}}, "user_is_author": {"title": "User Is Author", "type": "boolean"}, "synthetic": {"title": "Synthetic", "type": "boolean"}}, "description": "Represents a message in a conversation between the user and the assistant."}, "CreateApiClientRequest": {"title": "CreateApiClientRequest", "required": ["description", "frontend_type"], "type": "object", "properties": {"description": {"title": "Description", "type": "string"}, "frontend_type": {"title": "Frontend Type", "type": "string"}, "trusted": {"title": "Trusted", "type": "boolean", "default": false}, "admin_email": {"title": "<PERSON><PERSON>", "type": "string"}}}, "CreateFrontendUserRequest": {"title": "CreateFrontendUserRequest", "required": ["id", "display_name", "auth_method"], "type": "object", "properties": {"id": {"title": "Id", "type": "string"}, "display_name": {"title": "Display Name", "type": "string"}, "auth_method": {"title": "Auth Method", "enum": ["discord", "google", "local", "system"], "type": "string"}, "show_on_leaderboard": {"title": "Show On Leaderboard", "type": "boolean", "default": true}, "enabled": {"title": "Enabled", "type": "boolean", "default": true}, "tos_acceptance": {"title": "Tos Acceptance", "type": "boolean"}, "notes": {"title": "Notes", "type": "string"}}}, "EmojiCode": {"title": "EmojiCode", "enum": ["+1", "-1", "red_flag", "100", "rofl", "clap", "diamond", "heart_eyes", "disappointed", "poop", "skull", "_skip_reply", "_skip_ranking", "_skip_labeling"], "type": "string", "description": "An enumeration."}, "EmojiOp": {"title": "EmojiOp", "enum": ["toggle", "add", "remove"], "type": "string", "description": "An enumeration."}, "FlaggedMessageResponse": {"title": "FlaggedMessageResponse", "required": ["message_id", "processed"], "type": "object", "properties": {"message_id": {"title": "Message Id", "type": "string", "format": "uuid"}, "processed": {"title": "Processed", "type": "boolean"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time"}}}, "FrontEndUser": {"title": "FrontEndUser", "required": ["id", "display_name", "auth_method", "user_id", "enabled", "deleted", "notes", "show_on_leaderboard"], "type": "object", "properties": {"id": {"title": "Id", "type": "string"}, "display_name": {"title": "Display Name", "type": "string"}, "auth_method": {"title": "Auth Method", "enum": ["discord", "google", "local", "system"], "type": "string"}, "user_id": {"title": "User Id", "type": "string", "format": "uuid"}, "enabled": {"title": "Enabled", "type": "boolean"}, "deleted": {"title": "Deleted", "type": "boolean"}, "notes": {"title": "Notes", "type": "string"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time"}, "show_on_leaderboard": {"title": "Show On Leaderboard", "type": "boolean"}, "streak_days": {"title": "Streak Days", "type": "integer"}, "streak_last_day_date": {"title": "Streak Last Day Date", "type": "string", "format": "date-time"}, "last_activity_date": {"title": "Last Activity Date", "type": "string", "format": "date-time"}, "tos_acceptance_date": {"title": "Tos Acceptance Date", "type": "string", "format": "date-time"}}}, "FrontEndUserPage": {"title": "FrontEndUserPage", "required": ["sort_key", "items", "order"], "type": "object", "properties": {"prev": {"title": "Prev", "type": "string"}, "next": {"title": "Next", "type": "string"}, "sort_key": {"title": "Sort Key", "type": "string"}, "items": {"title": "Items", "type": "array", "items": {"$ref": "#/components/schemas/FrontEndUser"}}, "order": {"title": "Order", "enum": ["asc", "desc"], "type": "string"}}}, "HTTPValidationError": {"title": "HTTPValidationError", "type": "object", "properties": {"detail": {"title": "Detail", "type": "array", "items": {"$ref": "#/components/schemas/ValidationError"}}}}, "InitialPromptTask": {"title": "InitialPromptTask", "type": "object", "properties": {"hint": {"title": "Hint", "type": "string"}, "id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["initial_prompt"], "type": "string", "default": "initial_prompt"}}, "description": "A task to prompt the user to submit an initial prompt to the assistant."}, "LabelAssistantReplyTask": {"title": "LabelAssistantReplyTask", "required": ["message_id", "valid_labels", "conversation"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["label_assistant_reply"], "type": "string", "default": "label_assistant_reply"}, "message_id": {"title": "Message Id", "type": "string", "format": "uuid"}, "valid_labels": {"title": "Valid Labels", "type": "array", "items": {"type": "string"}}, "mandatory_labels": {"title": "Mandatory Labels", "type": "array", "items": {"type": "string"}}, "mode": {"$ref": "#/components/schemas/LabelTaskMode"}, "disposition": {"$ref": "#/components/schemas/LabelTaskDisposition"}, "labels": {"title": "Labels", "type": "array", "items": {"$ref": "#/components/schemas/LabelDescription"}}, "conversation": {"$ref": "#/components/schemas/Conversation"}, "reply": {"title": "Reply", "type": "string", "description": "deprecated, use last message of `conversation`", "deprecated": true}}, "description": "A task to label an assistant reply to a conversation."}, "LabelConversationReplyTask": {"title": "LabelConversationReplyTask", "required": ["message_id", "valid_labels", "conversation"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["label_conversation_reply"], "type": "string", "default": "label_conversation_reply"}, "message_id": {"title": "Message Id", "type": "string", "format": "uuid"}, "valid_labels": {"title": "Valid Labels", "type": "array", "items": {"type": "string"}}, "mandatory_labels": {"title": "Mandatory Labels", "type": "array", "items": {"type": "string"}}, "mode": {"$ref": "#/components/schemas/LabelTaskMode"}, "disposition": {"$ref": "#/components/schemas/LabelTaskDisposition"}, "labels": {"title": "Labels", "type": "array", "items": {"$ref": "#/components/schemas/LabelDescription"}}, "conversation": {"$ref": "#/components/schemas/Conversation"}, "reply": {"title": "Reply", "type": "string", "description": "deprecated, use last message of `conversation`", "deprecated": true}}, "description": "A task to label a reply to a conversation."}, "LabelDescription": {"title": "LabelDescription", "required": ["name", "widget", "display_text"], "type": "object", "properties": {"name": {"title": "Name", "type": "string"}, "widget": {"title": "Widget", "type": "string"}, "display_text": {"title": "Display Text", "type": "string"}, "help_text": {"title": "Help Text", "type": "string"}}}, "LabelInitialPromptTask": {"title": "LabelInitialPromptTask", "required": ["message_id", "valid_labels", "conversation"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["label_initial_prompt"], "type": "string", "default": "label_initial_prompt"}, "message_id": {"title": "Message Id", "type": "string", "format": "uuid"}, "valid_labels": {"title": "Valid Labels", "type": "array", "items": {"type": "string"}}, "mandatory_labels": {"title": "Mandatory Labels", "type": "array", "items": {"type": "string"}}, "mode": {"$ref": "#/components/schemas/LabelTaskMode"}, "disposition": {"$ref": "#/components/schemas/LabelTaskDisposition"}, "labels": {"title": "Labels", "type": "array", "items": {"$ref": "#/components/schemas/LabelDescription"}}, "conversation": {"$ref": "#/components/schemas/Conversation"}, "prompt": {"title": "Prompt", "type": "string", "description": "deprecated, use `prompt_message`", "deprecated": true}}, "description": "A task to label an initial prompt."}, "LabelPrompterReplyTask": {"title": "LabelPrompterReplyTask", "required": ["message_id", "valid_labels", "conversation"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["label_prompter_reply"], "type": "string", "default": "label_prompter_reply"}, "message_id": {"title": "Message Id", "type": "string", "format": "uuid"}, "valid_labels": {"title": "Valid Labels", "type": "array", "items": {"type": "string"}}, "mandatory_labels": {"title": "Mandatory Labels", "type": "array", "items": {"type": "string"}}, "mode": {"$ref": "#/components/schemas/LabelTaskMode"}, "disposition": {"$ref": "#/components/schemas/LabelTaskDisposition"}, "labels": {"title": "Labels", "type": "array", "items": {"$ref": "#/components/schemas/LabelDescription"}}, "conversation": {"$ref": "#/components/schemas/Conversation"}, "reply": {"title": "Reply", "type": "string", "description": "deprecated, use last message of `conversation`", "deprecated": true}}, "description": "A task to label a prompter reply to a conversation."}, "LabelTaskDisposition": {"title": "LabelTaskDisposition", "enum": ["quality", "spam"], "type": "string", "description": "Reason why the task was issued."}, "LabelTaskMode": {"title": "LabelTaskMode", "enum": ["simple", "full"], "type": "string", "description": "Label task mode that allows frontends to select an appropriate UI."}, "LeaderboardStats": {"title": "LeaderboardStats", "required": ["time_frame", "last_updated", "leaderboard"], "type": "object", "properties": {"time_frame": {"title": "Time Frame", "type": "string"}, "last_updated": {"title": "Last Updated", "type": "string", "format": "date-time"}, "leaderboard": {"title": "Leaderboard", "type": "array", "items": {"$ref": "#/components/schemas/UserScore"}}}}, "Message": {"title": "Message", "required": ["text", "is_assistant"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "user_id": {"title": "User Id", "type": "string", "format": "uuid"}, "frontend_message_id": {"title": "Frontend Message Id", "type": "string"}, "text": {"title": "Text", "type": "string"}, "lang": {"title": "<PERSON>", "type": "string"}, "is_assistant": {"title": "Is Assistant", "type": "boolean"}, "emojis": {"title": "Emojis", "type": "object", "additionalProperties": {"type": "integer"}}, "user_emojis": {"title": "User <PERSON>s", "type": "array", "items": {"type": "string"}}, "user_is_author": {"title": "User Is Author", "type": "boolean"}, "synthetic": {"title": "Synthetic", "type": "boolean"}, "parent_id": {"title": "Parent Id", "type": "string", "format": "uuid"}, "created_date": {"title": "Created Date", "type": "string", "format": "date-time"}, "review_result": {"title": "Review Result", "type": "boolean"}, "review_count": {"title": "Review Count", "type": "integer"}, "deleted": {"title": "Deleted", "type": "boolean"}, "model_name": {"title": "Model Name", "type": "string"}, "message_tree_id": {"title": "Message Tree Id", "type": "string", "format": "uuid"}, "ranking_count": {"title": "Ranking Count", "type": "integer"}, "rank": {"title": "Rank", "type": "integer"}, "user": {"$ref": "#/components/schemas/FrontEndUser"}}, "description": "Represents a message in a conversation between the user and the assistant."}, "MessageEmojiRequest": {"title": "MessageEmojiRequest", "required": ["user", "emoji"], "type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "op": {"allOf": [{"$ref": "#/components/schemas/EmojiOp"}], "default": "toggle"}, "emoji": {"$ref": "#/components/schemas/EmojiCode"}}}, "MessagePage": {"title": "MessagePage", "required": ["sort_key", "items", "order"], "type": "object", "properties": {"prev": {"title": "Prev", "type": "string"}, "next": {"title": "Next", "type": "string"}, "sort_key": {"title": "Sort Key", "type": "string"}, "items": {"title": "Items", "type": "array", "items": {"$ref": "#/components/schemas/Message"}}, "order": {"title": "Order", "enum": ["asc", "desc"], "type": "string"}}}, "MessageRanking": {"title": "MessageRanking", "required": ["user", "message_id", "ranking"], "type": "object", "properties": {"type": {"title": "Type", "enum": ["message_ranking"], "type": "string", "default": "message_ranking"}, "user": {"$ref": "#/components/schemas/User"}, "message_id": {"title": "Message Id", "type": "string"}, "ranking": {"title": "Ranking", "minItems": 1, "type": "array", "items": {"type": "integer"}}, "not_rankable": {"title": "Not Rankable", "type": "boolean"}}, "description": "A user has given a ranking for a message."}, "MessageRating": {"title": "MessageRating", "required": ["user", "message_id", "rating"], "type": "object", "properties": {"type": {"title": "Type", "enum": ["message_rating"], "type": "string", "default": "message_rating"}, "user": {"$ref": "#/components/schemas/User"}, "message_id": {"title": "Message Id", "type": "string"}, "rating": {"title": "Rating", "exclusiveMinimum": 0.0, "type": "integer"}}, "description": "A user has rated a message."}, "MessageTree": {"title": "MessageTree", "required": ["id"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "messages": {"title": "Messages", "type": "array", "items": {"$ref": "#/components/schemas/Message"}, "default": []}}, "description": "All messages belonging to the same message tree."}, "MessageTreeStateResponse": {"title": "MessageTreeStateResponse", "required": ["message_tree_id", "state", "goal_tree_size", "max_depth", "max_children_count", "active"], "type": "object", "properties": {"message_tree_id": {"title": "Message Tree Id", "type": "string", "format": "uuid"}, "state": {"$ref": "#/components/schemas/State"}, "goal_tree_size": {"title": "Goal Tree Size", "type": "integer"}, "max_depth": {"title": "<PERSON>", "type": "integer"}, "max_children_count": {"title": "Max Children Count", "type": "integer"}, "active": {"title": "Active", "type": "boolean"}, "origin": {"title": "Origin", "type": "string"}}}, "PrompterReplyTask": {"title": "Prompter<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": ["conversation"], "type": "object", "properties": {"hint": {"title": "Hint", "type": "string"}, "id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["prompter_reply"], "type": "string", "default": "prompter_reply"}, "conversation": {"$ref": "#/components/schemas/Conversation"}}, "description": "A task to prompt the user to submit a reply to the assistant."}, "PublicSettings": {"title": "PublicSettings", "required": ["PROJECT_NAME", "API_V1_STR", "MESSAGE_SIZE_LIMIT", "DEBUG_USE_SEED_DATA", "DEBUG_ALLOW_SELF_LABELING", "DEBUG_SKIP_EMBEDDING_COMPUTATION", "DEBUG_SKIP_TOXICITY_CALCULATION", "DEBUG_DATABASE_ECHO", "USER_STATS_INTERVAL_DAY", "USER_STATS_INTERVAL_WEEK", "USER_STATS_INTERVAL_MONTH", "USER_STATS_INTERVAL_TOTAL"], "type": "object", "properties": {"PROJECT_NAME": {"title": "Project Name", "type": "string"}, "API_V1_STR": {"title": "Api V1 Str", "type": "string"}, "MESSAGE_SIZE_LIMIT": {"title": "Message Size Limit", "type": "integer"}, "DEBUG_USE_SEED_DATA": {"title": "Debug Use Seed Data", "type": "boolean"}, "DEBUG_ALLOW_SELF_LABELING": {"title": "Debug Allow Self Labeling", "type": "boolean"}, "DEBUG_SKIP_EMBEDDING_COMPUTATION": {"title": "Debug Skip Embedding Computation", "type": "boolean"}, "DEBUG_SKIP_TOXICITY_CALCULATION": {"title": "Debug Skip Toxicity Calculation", "type": "boolean"}, "DEBUG_DATABASE_ECHO": {"title": "Debug Database Echo", "type": "boolean"}, "USER_STATS_INTERVAL_DAY": {"title": "User Stats Interval Day", "type": "integer"}, "USER_STATS_INTERVAL_WEEK": {"title": "User Stats Interval Week", "type": "integer"}, "USER_STATS_INTERVAL_MONTH": {"title": "User Stats Interval Month", "type": "integer"}, "USER_STATS_INTERVAL_TOTAL": {"title": "User Stats Interval Total", "type": "integer"}}, "description": "Subset of backend settings which can be retrieved by untrusted API clients."}, "PurgeResultModel": {"title": "PurgeResultModel", "required": ["before", "after", "preview", "duration"], "type": "object", "properties": {"before": {"$ref": "#/components/schemas/SystemStats"}, "after": {"$ref": "#/components/schemas/SystemStats"}, "preview": {"title": "Preview", "type": "boolean"}, "duration": {"title": "Duration", "type": "number"}}}, "RankAssistantRepliesTask": {"title": "RankAssistantRepliesTask", "required": ["conversation", "replies", "reply_messages", "message_tree_id", "ranking_parent_id", "reveal_synthetic"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["rank_assistant_replies"], "type": "string", "default": "rank_assistant_replies"}, "conversation": {"$ref": "#/components/schemas/Conversation"}, "replies": {"title": "Replies", "type": "array", "items": {"type": "string"}}, "reply_messages": {"title": "Reply Messages", "type": "array", "items": {"$ref": "#/components/schemas/ConversationMessage"}}, "message_tree_id": {"title": "Message Tree Id", "type": "string", "format": "uuid"}, "ranking_parent_id": {"title": "Ranking Parent Id", "type": "string", "format": "uuid"}, "reveal_synthetic": {"title": "Reveal Synthetic", "type": "boolean"}}, "description": "A task to rank a set of assistant replies to a conversation."}, "RankConversationRepliesTask": {"title": "RankConversationRepliesTask", "required": ["conversation", "replies", "reply_messages", "message_tree_id", "ranking_parent_id", "reveal_synthetic"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["rank_conversation_replies"], "type": "string", "default": "rank_conversation_replies"}, "conversation": {"$ref": "#/components/schemas/Conversation"}, "replies": {"title": "Replies", "type": "array", "items": {"type": "string"}}, "reply_messages": {"title": "Reply Messages", "type": "array", "items": {"$ref": "#/components/schemas/ConversationMessage"}}, "message_tree_id": {"title": "Message Tree Id", "type": "string", "format": "uuid"}, "ranking_parent_id": {"title": "Ranking Parent Id", "type": "string", "format": "uuid"}, "reveal_synthetic": {"title": "Reveal Synthetic", "type": "boolean"}}, "description": "A task to rank a set of replies to a conversation."}, "RankInitialPromptsTask": {"title": "RankInitialPromptsTask", "required": ["prompts", "prompt_messages"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["rank_initial_prompts"], "type": "string", "default": "rank_initial_prompts"}, "prompts": {"title": "Prompts", "type": "array", "items": {"type": "string"}}, "prompt_messages": {"title": "Prompt Messages", "type": "array", "items": {"$ref": "#/components/schemas/ConversationMessage"}}}, "description": "A task to rank a set of initial prompts."}, "RankPrompterRepliesTask": {"title": "RankPrompterRepliesTask", "required": ["conversation", "replies", "reply_messages", "message_tree_id", "ranking_parent_id", "reveal_synthetic"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["rank_prompter_replies"], "type": "string", "default": "rank_prompter_replies"}, "conversation": {"$ref": "#/components/schemas/Conversation"}, "replies": {"title": "Replies", "type": "array", "items": {"type": "string"}}, "reply_messages": {"title": "Reply Messages", "type": "array", "items": {"$ref": "#/components/schemas/ConversationMessage"}}, "message_tree_id": {"title": "Message Tree Id", "type": "string", "format": "uuid"}, "ranking_parent_id": {"title": "Ranking Parent Id", "type": "string", "format": "uuid"}, "reveal_synthetic": {"title": "Reveal Synthetic", "type": "boolean"}}, "description": "A task to rank a set of prompter replies to a conversation."}, "RateSummaryTask": {"title": "RateSummaryTask", "required": ["full_text", "summary"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["rate_summary"], "type": "string", "default": "rate_summary"}, "scale": {"title": "Scale", "allOf": [{"$ref": "#/components/schemas/RatingScale"}], "default": {"min": 1, "max": 5}}, "full_text": {"title": "Full Text", "type": "string"}, "summary": {"title": "Summary", "type": "string"}}, "description": "A task to rate a summary."}, "RatingScale": {"title": "RatingScale", "required": ["min", "max"], "type": "object", "properties": {"min": {"title": "Min", "type": "integer"}, "max": {"title": "Max", "type": "integer"}}}, "ReplyToConversationTask": {"title": "ReplyToConversationTask", "required": ["conversation"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["reply_to_conversation"], "type": "string", "default": "reply_to_conversation"}, "conversation": {"$ref": "#/components/schemas/Conversation"}}, "description": "A task to prompt the user to submit a reply to a conversation."}, "Settings": {"title": "Settings", "type": "object", "properties": {"PROJECT_NAME": {"title": "Project Name", "type": "string", "default": "open-assistant backend", "env_names": ["project_name"]}, "API_V1_STR": {"title": "Api V1 Str", "type": "string", "default": "/api/v1", "env_names": ["api_v1_str"]}, "OFFICIAL_WEB_API_KEY": {"title": "Official Web Api Key", "type": "string", "default": "1234", "env_names": ["official_web_api_key"]}, "AUTH_INFO": {"title": "Auth Info", "type": "string", "format": "binary", "default": "NextAuth.js Generated Encryption Key", "env_names": ["auth_info"]}, "AUTH_SALT": {"title": "Auth Salt", "type": "string", "format": "binary", "default": "", "env_names": ["auth_salt"]}, "AUTH_LENGTH": {"title": "Auth Length", "type": "integer", "default": 32, "env_names": ["auth_length"]}, "AUTH_SECRET": {"title": "Auth Secret", "type": "string", "format": "binary", "default": "O/M2uIbGj+lDD2oyNa8ax4jEOJqCPJzO53UbWShmq98=", "env_names": ["auth_secret"]}, "AUTH_COOKIE_NAME": {"title": "Auth Cookie Name", "type": "string", "default": "next-auth.session-token", "env_names": ["auth_cookie_name"]}, "AUTH_ALGORITHM": {"title": "Auth Algorithm", "type": "string", "default": "HS256", "env_names": ["auth_algorithm"]}, "AUTH_ACCESS_TOKEN_EXPIRE_MINUTES": {"title": "Auth Access Token Expire Minutes", "type": "integer", "default": 30, "env_names": ["auth_access_token_expire_minutes"]}, "AUTH_DISCORD_CLIENT_ID": {"title": "Auth Discord Client Id", "type": "string", "default": "", "env_names": ["auth_discord_client_id"]}, "AUTH_DISCORD_CLIENT_SECRET": {"title": "Auth Discord Client Secret", "type": "string", "default": "", "env_names": ["auth_discord_client_secret"]}, "POSTGRES_HOST": {"title": "Postgres Host", "type": "string", "default": "localhost", "env_names": ["postgres_host"]}, "POSTGRES_PORT": {"title": "Postgres Port", "type": "string", "default": "5432", "env_names": ["postgres_port"]}, "POSTGRES_USER": {"title": "Postgres User", "type": "string", "default": "postgres", "env_names": ["postgres_user"]}, "POSTGRES_PASSWORD": {"title": "Postgres Password", "type": "string", "default": "postgres", "env_names": ["postgres_password"]}, "POSTGRES_DB": {"title": "Postgres Db", "type": "string", "default": "postgres", "env_names": ["postgres_db"]}, "DATABASE_URI": {"title": "Database Uri", "maxLength": 65536, "minLength": 1, "type": "string", "format": "uri", "env_names": ["database_uri"]}, "DATABASE_MAX_TX_RETRY_COUNT": {"title": "Database Max Tx Retry Count", "type": "integer", "default": 3, "env_names": ["database_max_tx_retry_count"]}, "RATE_LIMIT": {"title": "Rate Limit", "type": "boolean", "default": true, "env_names": ["rate_limit"]}, "MESSAGE_SIZE_LIMIT": {"title": "Message Size Limit", "type": "integer", "default": 2000, "env_names": ["message_size_limit"]}, "REDIS_HOST": {"title": "Redis Host", "type": "string", "default": "localhost", "env_names": ["redis_host"]}, "REDIS_PORT": {"title": "Redis Port", "type": "string", "default": "6379", "env_names": ["redis_port"]}, "DEBUG_USE_SEED_DATA": {"title": "Debug Use Seed Data", "type": "boolean", "default": false, "env_names": ["debug_use_seed_data"]}, "DEBUG_USE_SEED_DATA_PATH": {"title": "Debug Use Seed Data Path", "type": "string", "format": "file-path", "default": "/workspaces/Open-Assistant/backend/test_data/realistic/realistic_seed_data.json", "env_names": ["debug_use_seed_data_path"]}, "DEBUG_ALLOW_SELF_LABELING": {"title": "Debug Allow Self Labeling", "type": "boolean", "default": false, "env_names": ["debug_allow_self_labeling"]}, "DEBUG_ALLOW_SELF_RANKING": {"title": "Debug Allow Self Ranking", "type": "boolean", "default": false, "env_names": ["debug_allow_self_ranking"]}, "DEBUG_ALLOW_DUPLICATE_TASKS": {"title": "Debug Allow Duplicate Tasks", "type": "boolean", "default": false, "env_names": ["debug_allow_duplicate_tasks"]}, "DEBUG_SKIP_EMBEDDING_COMPUTATION": {"title": "Debug Skip Embedding Computation", "type": "boolean", "default": false, "env_names": ["debug_skip_embedding_computation"]}, "DEBUG_SKIP_TOXICITY_CALCULATION": {"title": "Debug Skip Toxicity Calculation", "type": "boolean", "default": false, "env_names": ["debug_skip_toxicity_calculation"]}, "DEBUG_DATABASE_ECHO": {"title": "Debug Database Echo", "type": "boolean", "default": false, "env_names": ["debug_database_echo"]}, "DEBUG_IGNORE_TOS_ACCEPTANCE": {"title": "Debug Ignore Tos Acceptance", "type": "boolean", "default": true, "env_names": ["debug_ignore_tos_acceptance"]}, "DUPLICATE_MESSAGE_FILTER_WINDOW_MINUTES": {"title": "Duplicate Message Filter Window Minutes", "type": "integer", "default": 120, "env_names": ["duplicate_message_filter_window_minutes"]}, "HUGGING_FACE_API_KEY": {"title": "Hugging Face Api Key", "type": "string", "default": "", "env_names": ["hugging_face_api_key"]}, "ROOT_TOKENS": {"title": "Root Tokens", "type": "array", "items": {"type": "string"}, "default": ["1234"], "env_names": ["root_tokens"]}, "ENABLE_PROM_METRICS": {"title": "Enable Prom Metrics", "type": "boolean", "default": true, "env_names": ["enable_prom_metrics"]}, "BACKEND_CORS_ORIGINS_CSV": {"title": "Backend Cors Origins Csv", "type": "string", "env_names": ["backend_cors_origins_csv"]}, "BACKEND_CORS_ORIGINS": {"title": "Backend Cors Origins", "type": "array", "items": {"maxLength": 65536, "minLength": 1, "type": "string", "format": "uri"}, "default": [], "env_names": ["backend_cors_origins"]}, "UPDATE_ALEMBIC": {"title": "Update Alembic", "type": "boolean", "default": true, "env_names": ["update_alembic"]}, "tree_manager": {"title": "Tree Manager", "allOf": [{"$ref": "#/components/schemas/TreeManagerConfiguration"}], "default": {"max_active_trees": 10, "max_initial_prompt_review": 100, "max_tree_depth": 3, "max_children_count": 3, "num_prompter_replies": 1, "goal_tree_size": 12, "random_goal_tree_size": false, "min_goal_tree_size": 5, "num_reviews_initial_prompt": 3, "num_reviews_reply": 3, "auto_mod_enabled": true, "auto_mod_max_skip_reply": 25, "auto_mod_red_flags": 4, "p_full_labeling_review_prompt": 1.0, "p_full_labeling_review_reply_assistant": 1.0, "p_full_labeling_review_reply_prompter": 0.25, "acceptance_threshold_initial_prompt": 0.6, "acceptance_threshold_reply": 0.6, "num_required_rankings": 3, "p_activate_backlog_tree": 0.1, "min_active_rankings_per_lang": 0, "labels_initial_prompt": ["spam", "lang_mismatch", "quality", "creativity", "humor", "toxicity", "violence", "not_appropriate", "pii", "hate_speech", "sexual_content"], "labels_assistant_reply": ["spam", "lang_mismatch", "fails_task", "quality", "helpfulness", "creativity", "humor", "toxicity", "violence", "not_appropriate", "pii", "hate_speech", "sexual_content"], "labels_prompter_reply": ["spam", "lang_mismatch", "quality", "creativity", "humor", "toxicity", "violence", "not_appropriate", "pii", "hate_speech", "sexual_content"], "mandatory_labels_initial_prompt": ["spam"], "mandatory_labels_assistant_reply": ["spam"], "mandatory_labels_prompter_reply": ["spam"], "rank_prompter_replies": false, "lonely_children_count": 2, "p_lonely_child_extension": 0.75, "recent_tasks_span_sec": 300, "max_pending_tasks_per_user": 8, "max_prompt_lottery_waiting": 250, "init_prompt_disabled_langs": ""}, "env_names": ["tree_manager"]}, "USER_STATS_INTERVAL_DAY": {"title": "User Stats Interval Day", "type": "integer", "default": 5, "env_names": ["user_stats_interval_day"]}, "USER_STATS_INTERVAL_WEEK": {"title": "User Stats Interval Week", "type": "integer", "default": 15, "env_names": ["user_stats_interval_week"]}, "USER_STATS_INTERVAL_MONTH": {"title": "User Stats Interval Month", "type": "integer", "default": 60, "env_names": ["user_stats_interval_month"]}, "USER_STATS_INTERVAL_TOTAL": {"title": "User Stats Interval Total", "type": "integer", "default": 240, "env_names": ["user_stats_interval_total"]}, "USER_STREAK_UPDATE_INTERVAL": {"title": "User Streak Update Interval", "type": "integer", "default": 4, "env_names": ["user_streak_update_interval"]}, "CACHED_STATS_UPDATE_INTERVAL": {"title": "Cached Stats Update Interval", "type": "integer", "default": 60, "env_names": ["cached_stats_update_interval"]}, "RATE_LIMIT_TASK_USER_TIMES": {"title": "Rate Limit Task User Times", "type": "integer", "default": 30, "env_names": ["rate_limit_task_user_times"]}, "RATE_LIMIT_TASK_USER_MINUTES": {"title": "Rate Limit Task User Minutes", "type": "integer", "default": 4, "env_names": ["rate_limit_task_user_minutes"]}, "RATE_LIMIT_TASK_API_TIMES": {"title": "Rate Limit Task Api Times", "type": "integer", "default": 10000, "env_names": ["rate_limit_task_api_times"]}, "RATE_LIMIT_TASK_API_MINUTES": {"title": "Rate Limit Task Api Minutes", "type": "integer", "default": 1, "env_names": ["rate_limit_task_api_minutes"]}, "RATE_LIMIT_ASSISTANT_USER_TIMES": {"title": "Rate Limit Assistant User Times", "type": "integer", "default": 4, "env_names": ["rate_limit_assistant_user_times"]}, "RATE_LIMIT_ASSISTANT_USER_MINUTES": {"title": "Rate Limit Assistant User Minutes", "type": "integer", "default": 2, "env_names": ["rate_limit_assistant_user_minutes"]}, "RATE_LIMIT_PROMPTER_USER_TIMES": {"title": "Rate Limit Prompter User Times", "type": "integer", "default": 8, "env_names": ["rate_limit_prompter_user_times"]}, "RATE_LIMIT_PROMPTER_USER_MINUTES": {"title": "Rate Limit Prompter User Minutes", "type": "integer", "default": 2, "env_names": ["rate_limit_prompter_user_minutes"]}, "TASK_VALIDITY_MINUTES": {"title": "Task Validity Minutes", "type": "integer", "default": 2880, "env_names": ["task_validity_minutes"]}, "DATABASE_POOL_SIZE": {"title": "Database Pool Size", "type": "integer", "default": 75, "env_names": ["database_pool_size"]}, "DATABASE_MAX_OVERFLOW": {"title": "Database Max Overflow", "type": "integer", "default": 20, "env_names": ["database_max_overflow"]}}, "additionalProperties": false, "description": "Base class for settings, allowing values to be overridden by environment variables.\n\nThis is useful in production for secrets you do not wish to save in code, it plays nicely with docker(-compose),\n<PERSON><PERSON> and any 12 factor app design."}, "State": {"title": "State", "enum": ["initial_prompt_review", "growing", "ranking", "ready_for_scoring", "ready_for_export", "scoring_failed", "aborted_low_grade", "halted_by_moderator", "backlog_ranking", "prompt_lottery_waiting"], "type": "string", "description": "States of the Open-Assistant message tree state machine."}, "SummarizeStoryTask": {"title": "SummarizeStoryTask", "required": ["story"], "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["summarize_story"], "type": "string", "default": "summarize_story"}, "story": {"title": "Story", "type": "string"}}, "description": "A task to summarize a story."}, "SystemStats": {"title": "SystemStats", "type": "object", "properties": {"all": {"title": "All", "type": "integer", "default": 0}, "active": {"title": "Active", "type": "integer", "default": 0}, "active_by_lang": {"title": "Active By <PERSON>", "type": "object", "additionalProperties": {"type": "integer"}, "default": {}}, "deleted": {"title": "Deleted", "type": "integer", "default": 0}, "message_trees": {"title": "Message Trees", "type": "integer", "default": 0}}}, "TaskAck": {"title": "TaskAck", "required": ["message_id"], "type": "object", "properties": {"message_id": {"title": "Message Id", "type": "string"}}, "description": "The frontend acknowledges that it has received a task and created a message."}, "TaskClose": {"title": "TaskClose", "required": ["message_id"], "type": "object", "properties": {"message_id": {"title": "Message Id", "type": "string"}}, "description": "The frontend asks to mark task as done"}, "TaskDone": {"title": "TaskDone", "type": "object", "properties": {"id": {"title": "Id", "type": "string", "format": "uuid"}, "type": {"title": "Type", "enum": ["task_done"], "type": "string", "default": "task_done"}}, "description": "Signals to the frontend that the task is done."}, "TaskNAck": {"title": "TaskNAck", "type": "object", "properties": {"reason": {"title": "Reason", "type": "string", "nullable": true}}, "description": "The frontend acknowledges that it has received a task but cannot create a message."}, "TaskRequest": {"title": "TaskRequest", "type": "object", "properties": {"type": {"allOf": [{"$ref": "#/components/schemas/TaskRequestType"}], "default": "random"}, "user": {"title": "User", "allOf": [{"$ref": "#/components/schemas/User"}], "nullable": true}, "collective": {"title": "Collective", "type": "boolean", "default": false}, "lang": {"title": "<PERSON>", "type": "string", "nullable": true}}, "description": "The frontend asks the backend for a task."}, "TaskRequestType": {"title": "TaskRequestType", "enum": ["random", "summarize_story", "rate_summary", "initial_prompt", "prompter_reply", "assistant_reply", "rank_initial_prompts", "rank_prompter_replies", "rank_assistant_replies", "label_initial_prompt", "label_assistant_reply", "label_prompter_reply"], "type": "string", "description": "An enumeration."}, "TextLabel": {"title": "TextLabel", "enum": ["spam", "fails_task", "lang_mismatch", "pii", "not_appropriate", "hate_speech", "sexual_content", "moral_judgement", "political_content", "quality", "toxicity", "humor", "helpfulness", "creativity", "violence"], "type": "string", "description": "A label for a piece of text."}, "TextLabels": {"title": "TextLabels", "required": ["user", "text", "labels", "message_id"], "type": "object", "properties": {"type": {"title": "Type", "enum": ["text_labels"], "type": "string", "default": "text_labels"}, "user": {"$ref": "#/components/schemas/User"}, "text": {"title": "Text", "type": "string"}, "labels": {"title": "Labels", "type": "object", "additionalProperties": {"type": "number"}}, "message_id": {"title": "Message Id", "type": "string", "format": "uuid"}, "task_id": {"title": "Task Id", "type": "string", "format": "uuid"}, "is_report": {"title": "Is Report", "type": "boolean"}}, "description": "A set of labels for a piece of text."}, "TextReplyToMessage": {"title": "TextReplyToMessage", "required": ["user", "message_id", "user_message_id", "text"], "type": "object", "properties": {"type": {"title": "Type", "enum": ["text_reply_to_message"], "type": "string", "default": "text_reply_to_message"}, "user": {"$ref": "#/components/schemas/User"}, "message_id": {"title": "Message Id", "type": "string"}, "user_message_id": {"title": "User Message Id", "type": "string"}, "text": {"title": "Text", "minLength": 1, "type": "string"}, "lang": {"title": "<PERSON>", "type": "string"}}, "description": "A user has replied to a message with text."}, "TreeManagerConfiguration": {"title": "TreeManagerConfiguration", "type": "object", "properties": {"max_active_trees": {"title": "Max Active Trees", "type": "integer", "default": 10}, "max_initial_prompt_review": {"title": "Max Initial Prompt Review", "type": "integer", "default": 100}, "max_tree_depth": {"title": "Max Tree Depth", "type": "integer", "default": 3}, "max_children_count": {"title": "Max Children Count", "type": "integer", "default": 3}, "num_prompter_replies": {"title": "Num Prompter Replies", "type": "integer", "default": 1}, "goal_tree_size": {"title": "Goal Tree Size", "type": "integer", "default": 12}, "random_goal_tree_size": {"title": "Random Goal Tree Size", "type": "boolean", "default": false}, "min_goal_tree_size": {"title": "<PERSON> Goal Tree Size", "type": "integer", "default": 5}, "num_reviews_initial_prompt": {"title": "Num Reviews Initial Prompt", "type": "integer", "default": 3}, "num_reviews_reply": {"title": "Num Reviews Reply", "type": "integer", "default": 3}, "auto_mod_enabled": {"title": "Auto Mod Enabled", "type": "boolean", "default": true}, "auto_mod_max_skip_reply": {"title": "Auto Mod Max Skip Reply", "type": "integer", "default": 25}, "auto_mod_red_flags": {"title": "Auto Mod Red Flags", "type": "integer", "default": 4}, "p_full_labeling_review_prompt": {"title": "P Full Labeling Review Prompt", "type": "number", "default": 1.0}, "p_full_labeling_review_reply_assistant": {"title": "P Full Labeling Review Reply Assistant", "type": "number", "default": 1.0}, "p_full_labeling_review_reply_prompter": {"title": "P Full Labeling Review Reply Prompter", "type": "number", "default": 0.25}, "acceptance_threshold_initial_prompt": {"title": "Acceptance Threshold Initial Prompt", "type": "number", "default": 0.6}, "acceptance_threshold_reply": {"title": "Acceptance Threshold Reply", "type": "number", "default": 0.6}, "num_required_rankings": {"title": "Num Required Rankings", "type": "integer", "default": 3}, "p_activate_backlog_tree": {"title": "P Activate Backlog Tree", "type": "number", "default": 0.1}, "min_active_rankings_per_lang": {"title": "Min Active Rankings Per Lang", "type": "integer", "default": 0}, "labels_initial_prompt": {"type": "array", "items": {"$ref": "#/components/schemas/TextLabel"}, "default": ["spam", "lang_mismatch", "quality", "creativity", "humor", "toxicity", "violence", "not_appropriate", "pii", "hate_speech", "sexual_content"]}, "labels_assistant_reply": {"type": "array", "items": {"$ref": "#/components/schemas/TextLabel"}, "default": ["spam", "lang_mismatch", "fails_task", "quality", "helpfulness", "creativity", "humor", "toxicity", "violence", "not_appropriate", "pii", "hate_speech", "sexual_content"]}, "labels_prompter_reply": {"type": "array", "items": {"$ref": "#/components/schemas/TextLabel"}, "default": ["spam", "lang_mismatch", "quality", "creativity", "humor", "toxicity", "violence", "not_appropriate", "pii", "hate_speech", "sexual_content"]}, "mandatory_labels_initial_prompt": {"type": "array", "items": {"$ref": "#/components/schemas/TextLabel"}, "default": ["spam"]}, "mandatory_labels_assistant_reply": {"type": "array", "items": {"$ref": "#/components/schemas/TextLabel"}, "default": ["spam"]}, "mandatory_labels_prompter_reply": {"type": "array", "items": {"$ref": "#/components/schemas/TextLabel"}, "default": ["spam"]}, "rank_prompter_replies": {"title": "Rank Prompter Replies", "type": "boolean", "default": false}, "lonely_children_count": {"title": "Lonely Children Count", "type": "integer", "default": 2}, "p_lonely_child_extension": {"title": "P Lonely Child Extension", "type": "number", "default": 0.75}, "recent_tasks_span_sec": {"title": "Recent Tasks Span Sec", "type": "integer", "default": 300}, "max_pending_tasks_per_user": {"title": "Max Pending Tasks Per User", "type": "integer", "default": 8}, "max_prompt_lottery_waiting": {"title": "Max Prompt Lottery Waiting", "type": "integer", "default": 250}, "init_prompt_disabled_langs": {"title": "Init Prompt Disabled <PERSON><PERSON>", "type": "string", "default": ""}}, "description": "TreeManager configuration settings"}, "TreeManagerStats": {"title": "TreeManagerStats", "required": ["state_counts", "message_counts"], "type": "object", "properties": {"state_counts": {"title": "State Counts", "type": "object", "additionalProperties": {"type": "integer"}}, "message_counts": {"title": "Message Counts", "type": "array", "items": {"$ref": "#/components/schemas/TreeMessageCountStats"}}}}, "TreeMessageCountStats": {"title": "TreeMessageCountStats", "required": ["message_tree_id", "state", "depth", "oldest", "youngest", "count", "goal_tree_size"], "type": "object", "properties": {"message_tree_id": {"title": "Message Tree Id", "type": "string", "format": "uuid"}, "state": {"title": "State", "type": "string"}, "depth": {"title": "De<PERSON><PERSON>", "type": "integer"}, "oldest": {"title": "Oldest", "type": "string", "format": "date-time"}, "youngest": {"title": "Youngest", "type": "string", "format": "date-time"}, "count": {"title": "Count", "type": "integer"}, "goal_tree_size": {"title": "Goal Tree Size", "type": "integer"}}}, "TrollScore": {"title": "TrollScore", "required": ["user_id", "username", "auth_method", "display_name", "enabled", "deleted", "show_on_leaderboard"], "type": "object", "properties": {"rank": {"title": "Rank", "type": "integer"}, "user_id": {"title": "User Id", "type": "string", "format": "uuid"}, "highlighted": {"title": "Highlighted", "type": "boolean", "default": false}, "username": {"title": "Username", "type": "string"}, "auth_method": {"title": "Auth Method", "type": "string"}, "display_name": {"title": "Display Name", "type": "string"}, "last_activity_date": {"title": "Last Activity Date", "type": "string", "format": "date-time"}, "enabled": {"title": "Enabled", "type": "boolean"}, "deleted": {"title": "Deleted", "type": "boolean"}, "show_on_leaderboard": {"title": "Show On Leaderboard", "type": "boolean"}, "troll_score": {"title": "Troll Score", "type": "integer", "default": 0}, "base_date": {"title": "Base Date", "type": "string", "format": "date-time"}, "modified_date": {"title": "Modified Date", "type": "string", "format": "date-time"}, "red_flags": {"title": "Red Flags", "type": "integer", "default": 0}, "upvotes": {"title": "Upvotes", "type": "integer", "default": 0}, "downvotes": {"title": "Downvotes", "type": "integer", "default": 0}, "spam_prompts": {"title": "Spam Prompts", "type": "integer", "default": 0}, "quality": {"title": "Quality", "type": "number"}, "humor": {"title": "<PERSON><PERSON>", "type": "number"}, "toxicity": {"title": "Toxicity", "type": "number"}, "violence": {"title": "Violence", "type": "number"}, "helpfulness": {"title": "Helpfulness", "type": "number"}, "spam": {"title": "Spam", "type": "integer", "default": 0}, "lang_mismach": {"title": "<PERSON>", "type": "integer", "default": 0}, "not_appropriate": {"title": "Not Appropriate", "type": "integer", "default": 0}, "pii": {"title": "<PERSON><PERSON>", "type": "integer", "default": 0}, "hate_speech": {"title": "Hate Speech", "type": "integer", "default": 0}, "sexual_content": {"title": "Sexual Content", "type": "integer", "default": 0}, "political_content": {"title": "Political Content", "type": "integer", "default": 0}}}, "TrollboardStats": {"title": "TrollboardStats", "required": ["time_frame", "last_updated", "trollboard"], "type": "object", "properties": {"time_frame": {"title": "Time Frame", "type": "string"}, "last_updated": {"title": "Last Updated", "type": "string", "format": "date-time"}, "trollboard": {"title": "Trollboard", "type": "array", "items": {"$ref": "#/components/schemas/TrollScore"}}}}, "User": {"title": "User", "required": ["id", "display_name", "auth_method"], "type": "object", "properties": {"id": {"title": "Id", "type": "string"}, "display_name": {"title": "Display Name", "type": "string"}, "auth_method": {"title": "Auth Method", "enum": ["discord", "google", "local", "system"], "type": "string"}}}, "UserScore": {"title": "UserScore", "required": ["user_id", "username", "auth_method", "display_name"], "type": "object", "properties": {"rank": {"title": "Rank", "type": "integer"}, "user_id": {"title": "User Id", "type": "string", "format": "uuid"}, "highlighted": {"title": "Highlighted", "type": "boolean", "default": false}, "username": {"title": "Username", "type": "string"}, "auth_method": {"title": "Auth Method", "type": "string"}, "display_name": {"title": "Display Name", "type": "string"}, "leader_score": {"title": "Leader Score", "type": "integer", "default": 0}, "level": {"title": "Level", "type": "integer", "default": 0}, "base_date": {"title": "Base Date", "type": "string", "format": "date-time"}, "modified_date": {"title": "Modified Date", "type": "string", "format": "date-time"}, "prompts": {"title": "Prompts", "type": "integer", "default": 0}, "replies_assistant": {"title": "Replies Assistant", "type": "integer", "default": 0}, "replies_prompter": {"title": "Replies Prompter", "type": "integer", "default": 0}, "labels_simple": {"title": "Labels Simple", "type": "integer", "default": 0}, "labels_full": {"title": "Labels Full", "type": "integer", "default": 0}, "rankings_total": {"title": "Rankings Total", "type": "integer", "default": 0}, "rankings_good": {"title": "Rankings Good", "type": "integer", "default": 0}, "accepted_prompts": {"title": "Accepted Prompts", "type": "integer", "default": 0}, "accepted_replies_assistant": {"title": "Accepted Replies Assistant", "type": "integer", "default": 0}, "accepted_replies_prompter": {"title": "Accepted Replies Prompter", "type": "integer", "default": 0}, "reply_ranked_1": {"title": "Reply Ranked 1", "type": "integer", "default": 0}, "reply_ranked_2": {"title": "Reply Ranked 2", "type": "integer", "default": 0}, "reply_ranked_3": {"title": "Reply Ranked 3", "type": "integer", "default": 0}, "streak_last_day_date": {"title": "Streak Last Day Date", "type": "string", "format": "date-time"}, "streak_days": {"title": "Streak Days", "type": "integer"}, "last_activity_date": {"title": "Last Activity Date", "type": "string", "format": "date-time"}}}, "UserStatsTimeFrame": {"title": "UserStatsTimeFrame", "enum": ["day", "week", "month", "total"], "type": "string", "description": "An enumeration."}, "ValidationError": {"title": "ValidationError", "required": ["loc", "msg", "type"], "type": "object", "properties": {"loc": {"title": "Location", "type": "array", "items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}}, "msg": {"title": "Message", "type": "string"}, "type": {"title": "Error Type", "type": "string"}}}}, "securitySchemes": {"api-key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key"}, "oasst-user": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-oasst-user"}, "HTTPBearer": {"type": "http", "scheme": "bearer"}, "APIKeyCookie": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "next-auth.session-token"}}}}