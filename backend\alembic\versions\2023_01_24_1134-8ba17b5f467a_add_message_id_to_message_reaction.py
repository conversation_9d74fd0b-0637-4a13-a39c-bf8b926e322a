"""add message_id to message_reaction

Revision ID: 8ba17b5f467a
Revises: 160ac010efcc
Create Date: 2023-01-24 11:34:42.167575

"""
import sqlalchemy as sa
import sqlmodel
from alembic import op

# revision identifiers, used by Alembic.
revision = "8ba17b5f467a"
down_revision = "160ac010efcc"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("message_reaction", sa.Column("message_id", sqlmodel.sql.sqltypes.GUID(), nullable=True))
    op.create_index(op.f("ix_message_reaction_message_id"), "message_reaction", ["message_id"], unique=False)
    op.add_column("text_labels", sa.Column("task_id", sqlmodel.sql.sqltypes.GUID(), nullable=True))
    op.create_index(op.f("ix_text_labels_task_id"), "text_labels", ["task_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_text_labels_task_id"), table_name="text_labels")
    op.drop_column("text_labels", "task_id")
    op.drop_index(op.f("ix_message_reaction_message_id"), table_name="message_reaction")
    op.drop_column("message_reaction", "message_id")
    # ### end Alembic commands ###
