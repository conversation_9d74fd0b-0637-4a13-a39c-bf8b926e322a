{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": false}, {"name": "Python: Debug Tests", "type": "python", "request": "launch", "program": "${file}", "purpose": ["debug-test"], "console": "integratedTerminal", "justMyCode": true}, {"name": "Debug SFT Training", "type": "python", "request": "launch", "cwd": "${workspaceFolder}/model/model_training", "program": "trainer_sft.py", "args": ["--configs", "defaults", "oasst_export_eu", "gpt-neox"], "console": "integratedTerminal", "justMyCode": true, "env": {"TOKENIZERS_PARALLELISM": "false"}}, {"name": "Debug RM Training", "type": "python", "request": "launch", "cwd": "${workspaceFolder}/model/model_training", "program": "trainer_rm.py", "args": ["--configs", "defaults_rm", "oasst_export_eu", "debug"], "console": "integratedTerminal", "justMyCode": true, "env": {"TOKENIZERS_PARALLELISM": "false", "CUDA_VISIBLE_DEVICES": "1,2,3,4,5,6,7"}}, {"name": "Debug RM Training", "type": "python", "request": "launch", "cwd": "${workspaceFolder}/model/model_training", "program": "trainer_rm.py", "args": ["--configs", "defaults_rm", "oasst_export_eu", "pythia-1B", "--cache_dir", "/home/<USER>/data_cache", "--use_flash_attention", "true"], "console": "integratedTerminal", "justMyCode": true, "env": {"TOKENIZERS_PARALLELISM": "false", "CUDA_VISIBLE_DEVICES": "1"}}, {"name": "Debug Patching", "type": "python", "request": "launch", "cwd": "${workspaceFolder}/model/model_training", "program": "models/test_patched_gpt_neox.py", "console": "integratedTerminal", "justMyCode": true, "env": {"CUDA_VISIBLE_DEVICES": "0"}}, {"name": "Debug RLHF", "type": "python", "request": "launch", "cwd": "${workspaceFolder}/model/model_training", "module": "accelerate.commands.launch", "args": ["--main_process_port", "29506", "--config_file", "configs/accelerate_config.yaml", "--num_processes", "5", "trainer_rl.py", "--configs", "defaults", "defaults_rlhf", "pythia_rlhf", "oasst_export_latin_cyrillic_rlhf"], "console": "integratedTerminal", "justMyCode": false, "env": {"CUDA_VISIBLE_DEVICES": "1,2,3,4,5", "OMP_NUM_THREADS": "1"}}]}