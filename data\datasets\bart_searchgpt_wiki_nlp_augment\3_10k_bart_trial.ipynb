{"cells": [{"cell_type": "code", "execution_count": 1, "outputs": [], "source": ["import os\n", "import pyarrow.parquet as pq\n", "import pandas as pd\n", "import tqdm\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "folder_path = r\"D:\\github\\oa_test_gpu\\wiki_queries\""], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 2, "outputs": [], "source": ["# parquet_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith(\".parquet\")]\n", "# parquet_files"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 3, "outputs": [], "source": ["# dfs = [pq.read_table(f).to_pandas() for f in parquet_files]\n", "# concatenated_df = pd.concat(dfs)\n", "# concatenated_df.to_parquet(os.path.join(folder_path, \"wiki_queries_950k.parquet\"))"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 4, "outputs": [{"data": {"text/plain": "              id                                                url  \\\n1000000  5106811  https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash   \n1000001  5106829   https://en.wikipedia.org/wiki/Aurat%20Foundation   \n1000002  5106839          https://en.wikipedia.org/wiki/BloomingOUT   \n1000003  5106855  https://en.wikipedia.org/wiki/The%20Moment%20o...   \n1000004  5106875      https://en.wikipedia.org/wiki/Daily%20Emerald   \n...          ...                                                ...   \n89995     156641          https://en.wikipedia.org/wiki/Stalag%2017   \n89996     156644  https://en.wikipedia.org/wiki/Die%20Another%20Day   \n89997     156653             https://en.wikipedia.org/wiki/District   \n89998     156656  https://en.wikipedia.org/wiki/Oath%20of%20Alle...   \n89999     156658           https://en.wikipedia.org/wiki/Wiktionary   \n\n                                                      text  \\\n1000000  <PERSON><PERSON>j <PERSON>  was a women's right advocat...   \n1000001  Aurat Foundation, founded in 1986, is a women'...   \n1000002  bloomingOUT is an LGBT+ radio show broadcast o...   \n1000003  The Moment of the Magician  is a fantasy novel...   \n1000004  The Daily Emerald is the independent, student-...   \n...                                                    ...   \n89995    Stalag 17 is a 1953 American drama war film wh...   \n89996    Die Another Day is a 2002 spy film and the twe...   \n89997    A district is a type of administrative divisio...   \n89998    The Irish Oath of Allegiance  was a controvers...   \n89999    Wiktionary is a multilingual, web-based projec...   \n\n                                title  word_num  \\\n1000000             Shahtaj Qizilbash       139   \n1000001              Aurat Foundation        86   \n1000002                   BloomingOUT       153   \n1000003    The Moment of the Magician        49   \n1000004                 Daily Emerald        52   \n...                               ...       ...   \n89995                       Stalag 17       157   \n89996                 Die Another Day       211   \n89997                        District        43   \n89998    Oath of Allegiance (Ireland)        90   \n89999                      Wiktionary       167   \n\n                                                   queries  \n1000000  [who was shahtaj qizilbash, who is shahtaj qiz...  \n1000001  [where is aurat?, aurat foundation, what is au...  \n1000002  [where is blooming out, blooming out indiana, ...  \n1000003  [moment of the magician book, who wrote moment...  \n1000004  [where is the emerald newspaper in oregon, wha...  \n...                                                    ...  \n89995    [when was stalag 17 movie filmed, who was the ...  \n89996    [who acted in die another day movie, what movi...  \n89997    [what is the difference between district and c...  \n89998    [who swore the irish oath of allegiance?, when...  \n89999    [where is wikikidnary, definition of wikipedia...  \n\n[950000 rows x 6 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>url</th>\n      <th>text</th>\n      <th>title</th>\n      <th>word_num</th>\n      <th>queries</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>1000000</th>\n      <td>5106811</td>\n      <td>https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash</td>\n      <td><PERSON><PERSON><PERSON>  was a women's right advocat...</td>\n      <td><PERSON><PERSON><PERSON></td>\n      <td>139</td>\n      <td>[who was shahtaj q<PERSON><PERSON><PERSON><PERSON>, who is shahtaj qiz...</td>\n    </tr>\n    <tr>\n      <th>1000001</th>\n      <td>5106829</td>\n      <td>https://en.wikipedia.org/wiki/Aurat%20Foundation</td>\n      <td>Aurat Foundation, founded in 1986, is a women'...</td>\n      <td>Aurat Foundation</td>\n      <td>86</td>\n      <td>[where is aurat?, aurat foundation, what is au...</td>\n    </tr>\n    <tr>\n      <th>1000002</th>\n      <td>5106839</td>\n      <td>https://en.wikipedia.org/wiki/BloomingOUT</td>\n      <td>bloomingOUT is an LGBT+ radio show broadcast o...</td>\n      <td>BloomingOUT</td>\n      <td>153</td>\n      <td>[where is blooming out, blooming out indiana, ...</td>\n    </tr>\n    <tr>\n      <th>1000003</th>\n      <td>5106855</td>\n      <td>https://en.wikipedia.org/wiki/The%20Moment%20o...</td>\n      <td>The Moment of the Magician  is a fantasy novel...</td>\n      <td>The Moment of the Magician</td>\n      <td>49</td>\n      <td>[moment of the magician book, who wrote moment...</td>\n    </tr>\n    <tr>\n      <th>1000004</th>\n      <td>5106875</td>\n      <td>https://en.wikipedia.org/wiki/Daily%20Emerald</td>\n      <td>The Daily Emerald is the independent, student-...</td>\n      <td>Daily Emerald</td>\n      <td>52</td>\n      <td>[where is the emerald newspaper in oregon, wha...</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>89995</th>\n      <td>156641</td>\n      <td>https://en.wikipedia.org/wiki/Stalag%2017</td>\n      <td>Stalag 17 is a 1953 American drama war film wh...</td>\n      <td>Stalag 17</td>\n      <td>157</td>\n      <td>[when was stalag 17 movie filmed, who was the ...</td>\n    </tr>\n    <tr>\n      <th>89996</th>\n      <td>156644</td>\n      <td>https://en.wikipedia.org/wiki/Die%20Another%20Day</td>\n      <td>Die Another Day is a 2002 spy film and the twe...</td>\n      <td>Die Another Day</td>\n      <td>211</td>\n      <td>[who acted in die another day movie, what movi...</td>\n    </tr>\n    <tr>\n      <th>89997</th>\n      <td>156653</td>\n      <td>https://en.wikipedia.org/wiki/District</td>\n      <td>A district is a type of administrative divisio...</td>\n      <td>District</td>\n      <td>43</td>\n      <td>[what is the difference between district and c...</td>\n    </tr>\n    <tr>\n      <th>89998</th>\n      <td>156656</td>\n      <td>https://en.wikipedia.org/wiki/Oath%20of%20Alle...</td>\n      <td>The Irish Oath of Allegiance  was a controvers...</td>\n      <td>Oath of Allegiance (Ireland)</td>\n      <td>90</td>\n      <td>[who swore the irish oath of allegiance?, when...</td>\n    </tr>\n    <tr>\n      <th>89999</th>\n      <td>156658</td>\n      <td>https://en.wikipedia.org/wiki/Wiktionary</td>\n      <td>Wiktionary is a multilingual, web-based projec...</td>\n      <td>Wiktionary</td>\n      <td>167</td>\n      <td>[where is wikikidnary, definition of wikipedia...</td>\n    </tr>\n  </tbody>\n</table>\n<p>950000 rows × 6 columns</p>\n</div>"}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["concatenated_df = pd.read_parquet(os.path.join(folder_path, \"wiki_queries_950k.parquet\"))\n", "concatenated_df"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 5, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 950000/950000 [01:16<00:00, 12381.89it/s]\n"]}, {"data": {"text/plain": "              id                                                url  \\\n0        5106811  https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash   \n1        5106811  https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash   \n2        5106811  https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash   \n3        5106829   https://en.wikipedia.org/wiki/Aurat%20Foundation   \n4        5106829   https://en.wikipedia.org/wiki/Aurat%20Foundation   \n...          ...                                                ...   \n2849995   156656  https://en.wikipedia.org/wiki/Oath%20of%20Alle...   \n2849996   156656  https://en.wikipedia.org/wiki/Oath%20of%20Alle...   \n2849997   156658           https://en.wikipedia.org/wiki/Wiktionary   \n2849998   156658           https://en.wikipedia.org/wiki/Wiktionary   \n2849999   156658           https://en.wikipedia.org/wiki/Wiktionary   \n\n                                                      text  \\\n0        <PERSON><PERSON><PERSON>  was a women's right advocat...   \n1        <PERSON><PERSON><PERSON>  was a women's right advocat...   \n2        <PERSON><PERSON><PERSON>  was a women's right advocat...   \n3        Aurat Foundation, founded in 1986, is a women'...   \n4        Aurat Foundation, founded in 1986, is a women'...   \n...                                                    ...   \n2849995  The Irish Oath of Allegiance  was a controvers...   \n2849996  The Irish Oath of Allegiance  was a controvers...   \n2849997  Wiktionary is a multilingual, web-based projec...   \n2849998  Wiktionary is a multilingual, web-based projec...   \n2849999  Wiktionary is a multilingual, web-based projec...   \n\n                                title  word_num  \\\n0                   Shahtaj Qizilbash       139   \n1                   Shahtaj Qizilbash       139   \n2                   Shahtaj Qizilbash       139   \n3                    Aurat Foundation        86   \n4                    Aurat Foundation        86   \n...                               ...       ...   \n2849995  Oath of Allegiance (Ireland)        90   \n2849996  Oath of Allegiance (Ireland)        90   \n2849997                    Wiktionary       167   \n2849998                    Wiktionary       167   \n2849999                    Wiktionary       167   \n\n                                                    query  \n0                               who was shahtaj qizilbash  \n1                               who is shahtaj qizilbash?  \n2                                who is shahtaj qizilbash  \n3                                         where is aurat?  \n4                                        aurat foundation  \n...                                                   ...  \n2849995  when did the irish oath of allegiance take place  \n2849996             what was the irish oath of allegiance  \n2849997                              where is wikikidnary  \n2849998                           definition of wikipedia  \n2849999                 what is the meaning of wiktionary  \n\n[2850000 rows x 6 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>url</th>\n      <th>text</th>\n      <th>title</th>\n      <th>word_num</th>\n      <th>query</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>5106811</td>\n      <td>https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash</td>\n      <td><PERSON><PERSON><PERSON>  was a women's right advocat...</td>\n      <td><PERSON><PERSON><PERSON></td>\n      <td>139</td>\n      <td>who was shahtaj qizil<PERSON>h</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>5106811</td>\n      <td>https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash</td>\n      <td><PERSON><PERSON><PERSON> Qizilbash  was a women's right advocat...</td>\n      <td>Shahtaj Qizilbash</td>\n      <td>139</td>\n      <td>who is shahtaj qizilbash?</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>5106811</td>\n      <td>https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash</td>\n      <td>Shahtaj Qizilbash  was a women's right advocat...</td>\n      <td>Shahtaj Qizilbash</td>\n      <td>139</td>\n      <td>who is shahtaj qizilbash</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>5106829</td>\n      <td>https://en.wikipedia.org/wiki/Aurat%20Foundation</td>\n      <td>Aurat Foundation, founded in 1986, is a women'...</td>\n      <td>Aurat Foundation</td>\n      <td>86</td>\n      <td>where is aurat?</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5106829</td>\n      <td>https://en.wikipedia.org/wiki/Aurat%20Foundation</td>\n      <td>Aurat Foundation, founded in 1986, is a women'...</td>\n      <td>Aurat Foundation</td>\n      <td>86</td>\n      <td>aurat foundation</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2849995</th>\n      <td>156656</td>\n      <td>https://en.wikipedia.org/wiki/Oath%20of%20Alle...</td>\n      <td>The Irish Oath of Allegiance  was a controvers...</td>\n      <td>Oath of Allegiance (Ireland)</td>\n      <td>90</td>\n      <td>when did the irish oath of allegiance take place</td>\n    </tr>\n    <tr>\n      <th>2849996</th>\n      <td>156656</td>\n      <td>https://en.wikipedia.org/wiki/Oath%20of%20Alle...</td>\n      <td>The Irish Oath of Allegiance  was a controvers...</td>\n      <td>Oath of Allegiance (Ireland)</td>\n      <td>90</td>\n      <td>what was the irish oath of allegiance</td>\n    </tr>\n    <tr>\n      <th>2849997</th>\n      <td>156658</td>\n      <td>https://en.wikipedia.org/wiki/Wiktionary</td>\n      <td>Wiktionary is a multilingual, web-based projec...</td>\n      <td>Wiktionary</td>\n      <td>167</td>\n      <td>where is wikikidnary</td>\n    </tr>\n    <tr>\n      <th>2849998</th>\n      <td>156658</td>\n      <td>https://en.wikipedia.org/wiki/Wiktionary</td>\n      <td>Wiktionary is a multilingual, web-based projec...</td>\n      <td>Wiktionary</td>\n      <td>167</td>\n      <td>definition of wikipedia</td>\n    </tr>\n    <tr>\n      <th>2849999</th>\n      <td>156658</td>\n      <td>https://en.wikipedia.org/wiki/Wiktionary</td>\n      <td>Wiktionary is a multilingual, web-based projec...</td>\n      <td>Wiktionary</td>\n      <td>167</td>\n      <td>what is the meaning of wiktionary</td>\n    </tr>\n  </tbody>\n</table>\n<p>2850000 rows × 6 columns</p>\n</div>"}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# flatten the queries\n", "query_list, id_list, url_list, text_list, title_list, word_num_list = [], [], [], [], [], []\n", "for i, row in tqdm.tqdm(concatenated_df.iterrows(), total=len(concatenated_df)):\n", "    for query in row[\"queries\"]:\n", "        id_list.append(row[\"id\"])\n", "        url_list.append(row[\"url\"])\n", "        text_list.append(row[\"text\"])\n", "        title_list.append(row[\"title\"])\n", "        word_num_list.append(row[\"word_num\"])\n", "        query_list.append(query)\n", "query_df = pd.DataFrame(\n", "    {\n", "        \"id\": id_list,\n", "        \"url\": url_list,\n", "        \"text\": text_list,\n", "        \"title\": title_list,\n", "        \"word_num\": word_num_list,\n", "        \"query\": query_list,\n", "    }\n", ")\n", "query_df"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 6, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_33600\\2992388678.py:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  query_df['parent'] = query_df['query'].apply(get_parent_tag)\n"]}, {"data": {"text/plain": "              id                                                url  \\\n0        5106811  https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash   \n1        5106811  https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash   \n2        5106811  https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash   \n3        5106829   https://en.wikipedia.org/wiki/Aurat%20Foundation   \n4        5106829   https://en.wikipedia.org/wiki/Aurat%20Foundation   \n...          ...                                                ...   \n2849995   156656  https://en.wikipedia.org/wiki/Oath%20of%20Alle...   \n2849996   156656  https://en.wikipedia.org/wiki/Oath%20of%20Alle...   \n2849997   156658           https://en.wikipedia.org/wiki/Wiktionary   \n2849998   156658           https://en.wikipedia.org/wiki/Wiktionary   \n2849999   156658           https://en.wikipedia.org/wiki/Wiktionary   \n\n                                                      text  \\\n0        <PERSON><PERSON><PERSON>  was a women's right advocat...   \n1        <PERSON><PERSON><PERSON>  was a women's right advocat...   \n2        <PERSON><PERSON><PERSON>  was a women's right advocat...   \n3        Aurat Foundation, founded in 1986, is a women'...   \n4        Aurat Foundation, founded in 1986, is a women'...   \n...                                                    ...   \n2849995  The Irish Oath of Allegiance  was a controvers...   \n2849996  The Irish Oath of Allegiance  was a controvers...   \n2849997  Wiktionary is a multilingual, web-based projec...   \n2849998  Wiktionary is a multilingual, web-based projec...   \n2849999  Wiktionary is a multilingual, web-based projec...   \n\n                                title  word_num  \\\n0                   Shahtaj Qizilbash       139   \n1                   Shahtaj Qizilbash       139   \n2                   Shahtaj Qizilbash       139   \n3                    Aurat Foundation        86   \n4                    Aurat Foundation        86   \n...                               ...       ...   \n2849995  Oath of Allegiance (Ireland)        90   \n2849996  Oath of Allegiance (Ireland)        90   \n2849997                    Wiktionary       167   \n2849998                    Wiktionary       167   \n2849999                    Wiktionary       167   \n\n                                                    query   parent  \n0                               who was shahtaj qizilbash      who  \n1                               who is shahtaj qizilbash?      who  \n2                                who is shahtaj qizilbash      who  \n3                                         where is aurat?    where  \n4                                        aurat foundation  Unknown  \n...                                                   ...      ...  \n2849995  when did the irish oath of allegiance take place     when  \n2849996             what was the irish oath of allegiance     what  \n2849997                              where is wikikidnary    where  \n2849998                           definition of wikipedia   define  \n2849999                 what is the meaning of wiktionary     what  \n\n[2648487 rows x 7 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>url</th>\n      <th>text</th>\n      <th>title</th>\n      <th>word_num</th>\n      <th>query</th>\n      <th>parent</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>5106811</td>\n      <td>https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash</td>\n      <td><PERSON><PERSON><PERSON>  was a women's right advocat...</td>\n      <td><PERSON><PERSON>j <PERSON></td>\n      <td>139</td>\n      <td>who was shahtaj qizil<PERSON>h</td>\n      <td>who</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>5106811</td>\n      <td>https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash</td>\n      <td><PERSON>taj Qizilbash  was a women's right advocat...</td>\n      <td>Shahtaj Qizilbash</td>\n      <td>139</td>\n      <td>who is shahtaj qizilbash?</td>\n      <td>who</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>5106811</td>\n      <td>https://en.wikipedia.org/wiki/Shahtaj%20Qizilbash</td>\n      <td>Shahtaj Qizilbash  was a women's right advocat...</td>\n      <td>Shahtaj Qizilbash</td>\n      <td>139</td>\n      <td>who is shahtaj qizilbash</td>\n      <td>who</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>5106829</td>\n      <td>https://en.wikipedia.org/wiki/Aurat%20Foundation</td>\n      <td>Aurat Foundation, founded in 1986, is a women'...</td>\n      <td>Aurat Foundation</td>\n      <td>86</td>\n      <td>where is aurat?</td>\n      <td>where</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5106829</td>\n      <td>https://en.wikipedia.org/wiki/Aurat%20Foundation</td>\n      <td>Aurat Foundation, founded in 1986, is a women'...</td>\n      <td>Aurat Foundation</td>\n      <td>86</td>\n      <td>aurat foundation</td>\n      <td>Unknown</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2849995</th>\n      <td>156656</td>\n      <td>https://en.wikipedia.org/wiki/Oath%20of%20Alle...</td>\n      <td>The Irish Oath of Allegiance  was a controvers...</td>\n      <td>Oath of Allegiance (Ireland)</td>\n      <td>90</td>\n      <td>when did the irish oath of allegiance take place</td>\n      <td>when</td>\n    </tr>\n    <tr>\n      <th>2849996</th>\n      <td>156656</td>\n      <td>https://en.wikipedia.org/wiki/Oath%20of%20Alle...</td>\n      <td>The Irish Oath of Allegiance  was a controvers...</td>\n      <td>Oath of Allegiance (Ireland)</td>\n      <td>90</td>\n      <td>what was the irish oath of allegiance</td>\n      <td>what</td>\n    </tr>\n    <tr>\n      <th>2849997</th>\n      <td>156658</td>\n      <td>https://en.wikipedia.org/wiki/Wiktionary</td>\n      <td>Wiktionary is a multilingual, web-based projec...</td>\n      <td>Wiktionary</td>\n      <td>167</td>\n      <td>where is wikikidnary</td>\n      <td>where</td>\n    </tr>\n    <tr>\n      <th>2849998</th>\n      <td>156658</td>\n      <td>https://en.wikipedia.org/wiki/Wiktionary</td>\n      <td>Wiktionary is a multilingual, web-based projec...</td>\n      <td>Wiktionary</td>\n      <td>167</td>\n      <td>definition of wikipedia</td>\n      <td>define</td>\n    </tr>\n    <tr>\n      <th>2849999</th>\n      <td>156658</td>\n      <td>https://en.wikipedia.org/wiki/Wiktionary</td>\n      <td>Wiktionary is a multilingual, web-based projec...</td>\n      <td>Wiktionary</td>\n      <td>167</td>\n      <td>what is the meaning of wiktionary</td>\n      <td>what</td>\n    </tr>\n  </tbody>\n</table>\n<p>2648487 rows × 7 columns</p>\n</div>"}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["query_df = query_df.drop_duplicates()\n", "\n", "\n", "def get_parent_tag(query):\n", "    question_tag = [\"who\", \"when\", \"what\", \"which\", \"where\", \"how\", \"why\"]\n", "    for tag in question_tag:\n", "        if tag in query:\n", "            return tag\n", "    if \"is\" == query[:2] or \"are\" == query[:3] or \"was\" == query[:3] or \"were\" == query[:4]:\n", "        return \"is/are/was/were\"\n", "    if \"define\" in query or \"definition\" in query:\n", "        return \"define\"\n", "    return \"Unknown\"\n", "\n", "\n", "query_df[\"parent\"] = query_df[\"query\"].apply(get_parent_tag)\n", "query_df"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 7, "outputs": [{"data": {"text/plain": "([<matplotlib.patches.Wedge at 0x2038f10add0>,\n  <matplotlib.patches.Wedge at 0x2038f10ace0>,\n  <matplotlib.patches.Wedge at 0x2038f10bac0>,\n  <matplotlib.patches.Wedge at 0x2038f148190>,\n  <matplotlib.patches.Wedge at 0x2038f148820>,\n  <matplotlib.patches.Wedge at 0x2038f148eb0>,\n  <matplotlib.patches.Wedge at 0x2038f149540>,\n  <matplotlib.patches.Wedge at 0x2038f149bd0>,\n  <matplotlib.patches.Wedge at 0x2038f14a260>,\n  <matplotlib.patches.Wedge at 0x2038f14a8f0>],\n [Text(0.6639542683200764, 0.8770203701052515, 'what'),\n  Text(-1.0210867085051922, 0.4091233722412259, 'who'),\n  Text(-0.30258321384542464, -1.057564843732513, 'where'),\n  Text(0.7146903846452939, -0.8361923547220234, 'when'),\n  Text(0.939944335886077, -0.5714058500187776, 'Unknown'),\n  Text(1.0180705476485639, -0.41657215462696645, 'which'),\n  Text(1.0629847816910225, -0.2829546852294363, 'define'),\n  Text(1.0867731997921213, -0.170070609493805, 'how'),\n  Text(1.0972074519613497, -0.07833139447553998, 'why'),\n  Text(1.0998588401189247, -0.017621912843214328, 'is/are/was/were')],\n [Text(0.36215687362913257, 0.47837474733013713, '777952'),\n  Text(-0.5569563864573776, 0.22315820304066866, '771311'),\n  Text(-0.16504538937023158, -0.5768535511268251, '639276'),\n  Text(0.389831118897433, -0.4561049207574672, '191862'),\n  Text(0.5126969104833147, -0.31167591819206053, '75688'),\n  Text(0.5553112078083076, -0.22722117525107258, '57365'),\n  Text(0.5798098809223758, -0.15433891921605614, '50744'),\n  Text(0.5927853817047933, -0.09276578699662091, '37711'),\n  Text(0.598476791978918, -0.04272621516847635, '33072'),\n  Text(0.5999230037012316, -0.009611952459935088, '13506')])"}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": "<Figure size 1000x1000 with 1 Axes>", "image/png": "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*********************************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\n"}, "metadata": {}, "output_type": "display_data"}], "source": ["# create pie chart for query_df\n", "# text should be like 28.8% (n=1234)\n", "plt.figure(figsize=(10, 10))\n", "values = query_df[\"parent\"].value_counts()\n", "plt.pie(\n", "    values, labels=query_df[\"parent\"].value_counts().index, autopct=lambda x: \"{:.0f}\".format(x * values.sum() / 100)\n", ")"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 8, "outputs": [{"data": {"text/plain": "              id                                                url  \\\n4        5106829   https://en.wikipedia.org/wiki/Aurat%20Foundation   \n7        5106839          https://en.wikipedia.org/wiki/BloomingOUT   \n9        5106855  https://en.wikipedia.org/wiki/The%20Moment%20o...   \n124      5107172        https://en.wikipedia.org/wiki/Aubin%20Codex   \n169      5107366  https://en.wikipedia.org/wiki/Speed%20Demon%20...   \n...          ...                                                ...   \n2849905   156587        https://en.wikipedia.org/wiki/Death%20Eater   \n2849928   156598     https://en.wikipedia.org/wiki/Great%20Northern   \n2849967   156628  https://en.wikipedia.org/wiki/The%20Bad%20and%...   \n2849968   156628  https://en.wikipedia.org/wiki/The%20Bad%20and%...   \n2849976   156635         https://en.wikipedia.org/wiki/Glossophobia   \n\n                                                      text  \\\n4        Aurat Foundation, founded in 1986, is a women'...   \n7        bloomingOUT is an LGBT+ radio show broadcast o...   \n9        The Moment of the Magician  is a fantasy novel...   \n124      The Aubin Codex is an 81-leaf Aztec codex writ...   \n169      Speed Demon is a 2003 Canadian-American horror...   \n...                                                    ...   \n2849905  The Death Eaters are characters featured in th...   \n2849928                       Great Northern may refer to:   \n2849967  The Bad and the Beautiful is a 1952 American m...   \n2849968  The Bad and the Beautiful is a 1952 American m...   \n2849976  Glossophobia or speech anxiety is the fear of ...   \n\n                              title  word_num  \\\n4                  Aurat Foundation        86   \n7                       BloomingOUT       153   \n9        The Moment of the Magician        49   \n124                     Aubin Codex        48   \n169         Speed Demon (2003 film)        23   \n...                             ...       ...   \n2849905                 Death Eater       204   \n2849928              Great Northern         5   \n2849967   The Bad and the Beautiful       270   \n2849968   The Bad and the Beautiful       270   \n2849976                Glossophobia       175   \n\n                                                     query   parent  \n4                                         aurat foundation  Unknown  \n7                                     blooming out indiana  Unknown  \n9                              moment of the magician book  Unknown  \n124                                        the aubin codex  Unknown  \n169                              id speed demon movie cast  Unknown  \n...                                                    ...      ...  \n2849905                    harry potter death eaters names  Unknown  \n2849928                       great northern slang meaning  Unknown  \n2849967  the song for the bad and the beautiful theme o...  Unknown  \n2849968                      cast of the bad and beautiful  Unknown  \n2849976                                glossophobia causes  Unknown  \n\n[75688 rows x 7 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>url</th>\n      <th>text</th>\n      <th>title</th>\n      <th>word_num</th>\n      <th>query</th>\n      <th>parent</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>4</th>\n      <td>5106829</td>\n      <td>https://en.wikipedia.org/wiki/Aurat%20Foundation</td>\n      <td>Aurat Foundation, founded in 1986, is a women'...</td>\n      <td>Aurat Foundation</td>\n      <td>86</td>\n      <td>aurat foundation</td>\n      <td>Unknown</td>\n    </tr>\n    <tr>\n      <th>7</th>\n      <td>5106839</td>\n      <td>https://en.wikipedia.org/wiki/BloomingOUT</td>\n      <td>bloomingOUT is an LGBT+ radio show broadcast o...</td>\n      <td>BloomingOUT</td>\n      <td>153</td>\n      <td>blooming out indiana</td>\n      <td>Unknown</td>\n    </tr>\n    <tr>\n      <th>9</th>\n      <td>5106855</td>\n      <td>https://en.wikipedia.org/wiki/The%20Moment%20o...</td>\n      <td>The Moment of the Magician  is a fantasy novel...</td>\n      <td>The Moment of the Magician</td>\n      <td>49</td>\n      <td>moment of the magician book</td>\n      <td>Unknown</td>\n    </tr>\n    <tr>\n      <th>124</th>\n      <td>5107172</td>\n      <td>https://en.wikipedia.org/wiki/Aubin%20Codex</td>\n      <td>The Aubin Codex is an 81-leaf Aztec codex writ...</td>\n      <td>Aubin Codex</td>\n      <td>48</td>\n      <td>the aubin codex</td>\n      <td>Unknown</td>\n    </tr>\n    <tr>\n      <th>169</th>\n      <td>5107366</td>\n      <td>https://en.wikipedia.org/wiki/Speed%20Demon%20...</td>\n      <td>Speed Demon is a 2003 Canadian-American horror...</td>\n      <td>Speed Demon (2003 film)</td>\n      <td>23</td>\n      <td>id speed demon movie cast</td>\n      <td>Unknown</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2849905</th>\n      <td>156587</td>\n      <td>https://en.wikipedia.org/wiki/Death%20Eater</td>\n      <td>The Death Eaters are characters featured in th...</td>\n      <td>Death Eater</td>\n      <td>204</td>\n      <td>harry potter death eaters names</td>\n      <td>Unknown</td>\n    </tr>\n    <tr>\n      <th>2849928</th>\n      <td>156598</td>\n      <td>https://en.wikipedia.org/wiki/Great%20Northern</td>\n      <td>Great Northern may refer to:</td>\n      <td>Great Northern</td>\n      <td>5</td>\n      <td>great northern slang meaning</td>\n      <td>Unknown</td>\n    </tr>\n    <tr>\n      <th>2849967</th>\n      <td>156628</td>\n      <td>https://en.wikipedia.org/wiki/The%20Bad%20and%...</td>\n      <td>The Bad and the Beautiful is a 1952 American m...</td>\n      <td>The Bad and the Beautiful</td>\n      <td>270</td>\n      <td>the song for the bad and the beautiful theme o...</td>\n      <td>Unknown</td>\n    </tr>\n    <tr>\n      <th>2849968</th>\n      <td>156628</td>\n      <td>https://en.wikipedia.org/wiki/The%20Bad%20and%...</td>\n      <td>The Bad and the Beautiful is a 1952 American m...</td>\n      <td>The Bad and the Beautiful</td>\n      <td>270</td>\n      <td>cast of the bad and beautiful</td>\n      <td>Unknown</td>\n    </tr>\n    <tr>\n      <th>2849976</th>\n      <td>156635</td>\n      <td>https://en.wikipedia.org/wiki/Glossophobia</td>\n      <td>Glossophobia or speech anxiety is the fear of ...</td>\n      <td>Glossophobia</td>\n      <td>175</td>\n      <td>glossophobia causes</td>\n      <td>Unknown</td>\n    </tr>\n  </tbody>\n</table>\n<p>75688 rows × 7 columns</p>\n</div>"}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["query_df[(query_df[\"parent\"] == \"Unknown\")]\n", "# query_df[(query_df['parent'] == 'Unknown') & (query_df['query'].str.contains('define'))]"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 56, "outputs": [{"data": {"text/plain": "               id                                                url  \\\n2430719   2300253  https://en.wikipedia.org/wiki/Passenger%20to%2...   \n1009479    744577  https://en.wikipedia.org/wiki/Indoor%20Footbal...   \n1848132  50950148       https://en.wikipedia.org/wiki/Katie%20Umback   \n176027     262411  https://en.wikipedia.org/wiki/Anton%20Alberts%...   \n289371     354408      https://en.wikipedia.org/wiki/Arshile%20Gorky   \n...           ...                                                ...   \n2582496   2582061       https://en.wikipedia.org/wiki/A.%20M.%20Naik   \n2338613   2194726   https://en.wikipedia.org/wiki/Jesse%20E.%20James   \n1904500  51160949  https://en.wikipedia.org/wiki/Amanda%20Benoit-...   \n2328121   2177821  https://en.wikipedia.org/wiki/Will%20to%20Powe...   \n1700683   1768814   https://en.wikipedia.org/wiki/Antoine%20Duquesne   \n\n                                                      text  \\\n2430719  Passenger to Frankfurt: An Extravaganza is a s...   \n1009479  The Indoor Football League  began in 1999 as a...   \n1848132  Katie-Marie Umback  is an Australian para-eque...   \n176027   <PERSON>  <PERSON>s  was a Dutch architect best kno...   \n289371   Arshile Gorky  was an Armenian-American painte...   \n...                                                    ...   \n2582496  Anil Manibhai Naik  is an Indian industrialist...   \n2338613  Jesse Edwards \"Tim\" James  was the only surviv...   \n1904500  Amanda Benoit-Wark  is a Canadian ice hockey p...   \n2328121  Will to Power is an American dance-pop group t...   \n1700683  Antoine Duquesne  was a Belgian politician and...   \n\n                                      title  word_num  \\\n2430719              Passenger to Frankfurt       272   \n1009479  Indoor Football League (1999–2000)       209   \n1848132                        Katie Umback        18   \n176027            Anton Alberts (architect)       185   \n289371                        Arshile Gorky        87   \n...                                     ...       ...   \n2582496                          A. M. Naik        82   \n2338613                      Jesse E. James        48   \n1904500                  Amanda Benoit-Wark        41   \n2328121                Will to Power (band)       117   \n1700683                    Antoine Duquesne        42   \n\n                                               query           parent  \n2430719             who wrote passenger to frankfurt              who  \n1009479                             who owns the ifl              who  \n1848132                   who is the para equestrian              who  \n176027            who is architect alexander alberts              who  \n289371                                  who is gorky              who  \n...                                              ...              ...  \n2582496               is anil manibhai naik an india  is/are/was/were  \n2338613            was jesse james born in tennessee  is/are/was/were  \n1904500           is amanda benoit ark hockey player  is/are/was/were  \n2328121  is will to power in the united states music  is/are/was/were  \n1700683                 is anthony duquesne a french  is/are/was/were  \n\n[10000 rows x 7 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>url</th>\n      <th>text</th>\n      <th>title</th>\n      <th>word_num</th>\n      <th>query</th>\n      <th>parent</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>2430719</th>\n      <td>2300253</td>\n      <td>https://en.wikipedia.org/wiki/Passenger%20to%2...</td>\n      <td>Passenger to Frankfurt: An Extravaganza is a s...</td>\n      <td>Passenger to Frankfurt</td>\n      <td>272</td>\n      <td>who wrote passenger to frankfurt</td>\n      <td>who</td>\n    </tr>\n    <tr>\n      <th>1009479</th>\n      <td>744577</td>\n      <td>https://en.wikipedia.org/wiki/Indoor%20Footbal...</td>\n      <td>The Indoor Football League  began in 1999 as a...</td>\n      <td>Indoor Football League (1999–2000)</td>\n      <td>209</td>\n      <td>who owns the ifl</td>\n      <td>who</td>\n    </tr>\n    <tr>\n      <th>1848132</th>\n      <td>50950148</td>\n      <td>https://en.wikipedia.org/wiki/Katie%20Umback</td>\n      <td>Katie-Marie Umback  is an Australian para-eque...</td>\n      <td>Katie Umback</td>\n      <td>18</td>\n      <td>who is the para equestrian</td>\n      <td>who</td>\n    </tr>\n    <tr>\n      <th>176027</th>\n      <td>262411</td>\n      <td>https://en.wikipedia.org/wiki/Anton%20Alberts%...</td>\n      <td>Anton  Alberts  was a Dutch architect best kno...</td>\n      <td>Anton Alberts (architect)</td>\n      <td>185</td>\n      <td>who is architect alexander alberts</td>\n      <td>who</td>\n    </tr>\n    <tr>\n      <th>289371</th>\n      <td>354408</td>\n      <td>https://en.wikipedia.org/wiki/Arshile%20Gorky</td>\n      <td>Arshile Gorky  was an Armenian-American painte...</td>\n      <td>Arshile Gorky</td>\n      <td>87</td>\n      <td>who is gorky</td>\n      <td>who</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>2582496</th>\n      <td>2582061</td>\n      <td>https://en.wikipedia.org/wiki/A.%20M.%20Naik</td>\n      <td>Anil Manibhai Naik  is an Indian industrialist...</td>\n      <td>A. M. Naik</td>\n      <td>82</td>\n      <td>is anil manibhai naik an india</td>\n      <td>is/are/was/were</td>\n    </tr>\n    <tr>\n      <th>2338613</th>\n      <td>2194726</td>\n      <td>https://en.wikipedia.org/wiki/Jesse%20E.%20James</td>\n      <td>Jesse Edwards \"Tim\" James  was the only surviv...</td>\n      <td>Jesse E. James</td>\n      <td>48</td>\n      <td>was jesse james born in tennessee</td>\n      <td>is/are/was/were</td>\n    </tr>\n    <tr>\n      <th>1904500</th>\n      <td>51160949</td>\n      <td>https://en.wikipedia.org/wiki/Amanda%20Benoit-...</td>\n      <td>Amanda Benoit-Wark  is a Canadian ice hockey p...</td>\n      <td>Amanda Benoit-Wark</td>\n      <td>41</td>\n      <td>is amanda benoit ark hockey player</td>\n      <td>is/are/was/were</td>\n    </tr>\n    <tr>\n      <th>2328121</th>\n      <td>2177821</td>\n      <td>https://en.wikipedia.org/wiki/Will%20to%20Powe...</td>\n      <td>Will to Power is an American dance-pop group t...</td>\n      <td>Will to Power (band)</td>\n      <td>117</td>\n      <td>is will to power in the united states music</td>\n      <td>is/are/was/were</td>\n    </tr>\n    <tr>\n      <th>1700683</th>\n      <td>1768814</td>\n      <td>https://en.wikipedia.org/wiki/Antoine%20Duquesne</td>\n      <td>Antoine Duquesne  was a Belgian politician and...</td>\n      <td>Antoine Duquesne</td>\n      <td>42</td>\n      <td>is anthony duquesne a french</td>\n      <td>is/are/was/were</td>\n    </tr>\n  </tbody>\n</table>\n<p>10000 rows × 7 columns</p>\n</div>"}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["parent_tags = query_df[\"parent\"].unique()\n", "sampled_df_list = []\n", "for parent_tag in parent_tags:\n", "    df = query_df[query_df[\"parent\"] == parent_tag]\n", "    # randomly get 100 rows with seed\n", "    df = df.sample(n=1000, random_state=1)\n", "    sampled_df_list.append(df)\n", "sampled_df = pd.concat(sampled_df_list)\n", "sampled_df"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 57, "outputs": [], "source": ["sampled_df.to_csv(\"wiki_qa_bart_10000row_input.csv\", index=False)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 10, "outputs": [], "source": ["# plt.figure(figsize=(10,10))\n", "# values = sampled_df['parent'].value_counts()\n", "# plt.pie(values, labels=sampled_df['parent'].value_counts().index,\n", "#         autopct= lambda x: '{:.0f}'.format(x*values.sum()/100)\n", "#         )"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 50, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cuda\n"]}], "source": ["import torch\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(device)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 51, "outputs": [], "source": ["from transformers import pipeline\n", "import tiktoken\n", "\n", "\n", "def num_tokens_from_string(string: str) -> int:\n", "    encoding = tiktoken.encoding_for_model(\"gpt-3.5-turbo\")\n", "    num_tokens = len(encoding.encode(string))\n", "    return num_tokens\n", "\n", "\n", "row = sampled_df.iloc[0]\n", "summarizer = pipeline(\"summarization\", model=\"facebook/bart-large-cnn\")"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 52, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Your max_length is set to 353, but you input_length is only 346. You might consider decreasing max_length manually, e.g. summarizer('...', max_length=173)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["who wrote passenger to frankfurt\n", "text (token: 353)\n", "Passenger to Frankfurt: An Extravaganza is a spy novel by <PERSON> first published in the United Kingdom by the Collins Crime Club in September 1970 and in the United States by Dodd, Mead and Company later in the same year. The UK edition retailed at twenty-five shillings. In preparation for decimalisation on 15 February 1971, it was concurrently priced on the dustjacket at £1.25. The US edition retailed at $5.95.\n", "It was published to mark <PERSON>'s eightieth birthday and, by counting up both UK and US short-story collections to reach the desired total, was also advertised as her eightieth book. It is the last of her spy novels. At the beginning of the book there is a quote by <PERSON>, \"Leadership, besides being a great creative force, can be diabolical ...\"\n", "Sir <PERSON>, a middle-aged diplomat, steps into the world of spies, double agents, and secret groups to effect a change in international power centres. He meets a woman who has selected him to aid her at a crucial point, when a weather delay changes where her and his aeroplane flight lands before proceeding to England. There is much commentary on the changes in the world, especially college age youth in Europe, the United States, and South America, in the late 1960s.\n", "The novel received mixed reviews at publication and in 1990. In 2017, it was assessed favourably in an essay about speculative spy thriller novels by women. It is one of only four Christie novels not to have received an adaptation of any kind, the others being <PERSON> Comes as the End, Destination Unknown and <PERSON><PERSON> of Fate.\n", "output\n", "[{'summary_text': \"The Passenger to Frankfurt: An Extravaganza is a spy novel by <PERSON>. It was published to mark <PERSON>'s eightieth birthday and was advertised as her eightieth book. It is one of only four of her novels not to have received an adaptation.\"}]\n"]}], "source": ["# one sample test\n", "print(row[\"query\"])\n", "num_tokens = num_tokens_from_string(row[\"text\"])\n", "print(f\"text (token: {num_tokens})\")\n", "print(row[\"text\"])\n", "print(\"output\")\n", "print(summarizer(row[\"text\"], max_length=num_tokens, min_length=40, do_sample=False))"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 27, "outputs": [{"data": {"text/plain": "\"The Passenger to Frankfurt: An Extravaganza is a spy novel by <PERSON>. It was published to mark <PERSON>'s eightieth birthday and was advertised as her eightieth book. It is one of only four of her novels not to have received an adaptation.\""}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["response = summarizer(row[\"text\"], max_length=int(num_tokens * 0.7), min_length=40, do_sample=False)[0][\"summary_text\"]\n", "response"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 58, "outputs": [{"data": {"text/plain": "            id                                                url  \\\n0      2300253  https://en.wikipedia.org/wiki/Passenger%20to%2...   \n1       744577  https://en.wikipedia.org/wiki/Indoor%20Footbal...   \n2     50950148       https://en.wikipedia.org/wiki/Katie%20Umback   \n3       262411  https://en.wikipedia.org/wiki/Anton%20Alberts%...   \n4       354408      https://en.wikipedia.org/wiki/Arshile%20Gorky   \n...        ...                                                ...   \n9995   2582061       https://en.wikipedia.org/wiki/A.%20M.%20Naik   \n9996   2194726   https://en.wikipedia.org/wiki/Jesse%20E.%20James   \n9997  51160949  https://en.wikipedia.org/wiki/Amanda%20Benoit-...   \n9998   2177821  https://en.wikipedia.org/wiki/Will%20to%20Powe...   \n9999   1768814   https://en.wikipedia.org/wiki/<PERSON>%20Duquesne   \n\n                                                   text  \\\n0     Passenger to Frankfurt: An Extravaganza is a s...   \n1     The Indoor Football League  began in 1999 as a...   \n2     <PERSON><PERSON><PERSON>  is an Australian para-eque...   \n3     <PERSON>  was a Dutch architect best kno...   \n4     Arshile Gorky  was an Armenian-American painte...   \n...                                                 ...   \n9995  Anil Manibhai Naik  is an Indian industrialist...   \n9996  Jesse Edwards \"Tim\" James  was the only surviv...   \n9997  Amanda Benoit-Wark  is a Canadian ice hockey p...   \n9998  Will to Power is an American dance-pop group t...   \n9999  Antoine Duquesne  was a Belgian politician and...   \n\n                                   title  word_num  \\\n0                 Passenger to Frankfurt       272   \n1     Indoor Football League (1999–2000)       209   \n2                           Katie Umback        18   \n3              Anton Alberts (architect)       185   \n4                          Arshile Gorky        87   \n...                                  ...       ...   \n9995                          A. M. Naik        82   \n9996                      Jesse E. James        48   \n9997                  Amanda Benoit-Wark        41   \n9998                Will to Power (band)       117   \n9999                    Antoine Duquesne        42   \n\n                                            query           parent  \\\n0                who wrote passenger to frankfurt              who   \n1                                who owns the ifl              who   \n2                      who is the para equestrian              who   \n3              who is architect alexander alberts              who   \n4                                    who is gorky              who   \n...                                           ...              ...   \n9995               is anil manibhai naik an india  is/are/was/were   \n9996            was jesse james born in tennessee  is/are/was/were   \n9997           is amanda benoit ark hockey player  is/are/was/were   \n9998  is will to power in the united states music  is/are/was/were   \n9999                 is anthony duquesne a french  is/are/was/were   \n\n                                               response  \n0     The Passenger to Frankfurt: An Extravaganza is...  \n1     The Indoor Football League was founded in 1999...  \n2     Katie-Marie Umback is an Australian para-eques...  \n3     Anton Alberts was a Dutch architect best known...  \n4     Arshile Gorky was an Armenian-American painter...  \n...                                                 ...  \n9995  Anil Manibhai Naik is an Indian industrialist,...  \n9996  Jesse Edwards \"Tim\" James was the only survivi...  \n9997  Amanda Benoit-Wark played for the Canada women...  \n9998  Will to Power is an American dance-pop group t...  \n9999  Antoine Duquesne was a Belgian politician.Memb...  \n\n[10000 rows x 8 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>url</th>\n      <th>text</th>\n      <th>title</th>\n      <th>word_num</th>\n      <th>query</th>\n      <th>parent</th>\n      <th>response</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>2300253</td>\n      <td>https://en.wikipedia.org/wiki/Passenger%20to%2...</td>\n      <td>Passenger to Frankfurt: An Extravaganza is a s...</td>\n      <td>Passenger to Frankfurt</td>\n      <td>272</td>\n      <td>who wrote passenger to frankfurt</td>\n      <td>who</td>\n      <td>The Passenger to Frankfurt: An Extravaganza is...</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>744577</td>\n      <td>https://en.wikipedia.org/wiki/Indoor%20Footbal...</td>\n      <td>The Indoor Football League  began in 1999 as a...</td>\n      <td>Indoor Football League (1999–2000)</td>\n      <td>209</td>\n      <td>who owns the ifl</td>\n      <td>who</td>\n      <td>The Indoor Football League was founded in 1999...</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>50950148</td>\n      <td>https://en.wikipedia.org/wiki/Katie%20Umback</td>\n      <td>Katie-Marie Umback  is an Australian para-eque...</td>\n      <td>Katie Umback</td>\n      <td>18</td>\n      <td>who is the para equestrian</td>\n      <td>who</td>\n      <td>Katie-Marie Umback is an Australian para-eques...</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>262411</td>\n      <td>https://en.wikipedia.org/wiki/Anton%20Alberts%...</td>\n      <td>Anton  Alberts  was a Dutch architect best kno...</td>\n      <td>Anton Alberts (architect)</td>\n      <td>185</td>\n      <td>who is architect alexander alberts</td>\n      <td>who</td>\n      <td>Anton Alberts was a Dutch architect best known...</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>354408</td>\n      <td>https://en.wikipedia.org/wiki/Arshile%20Gorky</td>\n      <td>Arshile Gorky  was an Armenian-American painte...</td>\n      <td>Arshile Gorky</td>\n      <td>87</td>\n      <td>who is gorky</td>\n      <td>who</td>\n      <td>Arshile Gorky was an Armenian-American painter...</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>9995</th>\n      <td>2582061</td>\n      <td>https://en.wikipedia.org/wiki/A.%20M.%20Naik</td>\n      <td>Anil Manibhai Naik  is an Indian industrialist...</td>\n      <td>A. M. Naik</td>\n      <td>82</td>\n      <td>is anil manibhai naik an india</td>\n      <td>is/are/was/were</td>\n      <td>Anil Manibhai Naik is an Indian industrialist,...</td>\n    </tr>\n    <tr>\n      <th>9996</th>\n      <td>2194726</td>\n      <td>https://en.wikipedia.org/wiki/Jesse%20E.%20James</td>\n      <td>Jesse Edwards \"Tim\" James  was the only surviv...</td>\n      <td>Jesse E. James</td>\n      <td>48</td>\n      <td>was jesse james born in tennessee</td>\n      <td>is/are/was/were</td>\n      <td>Jesse Edwards \"Tim\" James was the only survivi...</td>\n    </tr>\n    <tr>\n      <th>9997</th>\n      <td>51160949</td>\n      <td>https://en.wikipedia.org/wiki/Amanda%20Benoit-...</td>\n      <td>Amanda Benoit-Wark  is a Canadian ice hockey p...</td>\n      <td>Amanda Benoit-Wark</td>\n      <td>41</td>\n      <td>is amanda benoit ark hockey player</td>\n      <td>is/are/was/were</td>\n      <td>Amanda Benoit-Wark played for the Canada women...</td>\n    </tr>\n    <tr>\n      <th>9998</th>\n      <td>2177821</td>\n      <td>https://en.wikipedia.org/wiki/Will%20to%20Powe...</td>\n      <td>Will to Power is an American dance-pop group t...</td>\n      <td>Will to Power (band)</td>\n      <td>117</td>\n      <td>is will to power in the united states music</td>\n      <td>is/are/was/were</td>\n      <td>Will to Power is an American dance-pop group t...</td>\n    </tr>\n    <tr>\n      <th>9999</th>\n      <td>1768814</td>\n      <td>https://en.wikipedia.org/wiki/Antoine%20Duquesne</td>\n      <td>Antoine Duquesne  was a Belgian politician and...</td>\n      <td>Antoine Duquesne</td>\n      <td>42</td>\n      <td>is anthony duquesne a french</td>\n      <td>is/are/was/were</td>\n      <td>Antoine Duquesne was a Belgian politician.Memb...</td>\n    </tr>\n  </tbody>\n</table>\n<p>10000 rows × 8 columns</p>\n</div>"}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["# multi sample output\n", "sampled_output_df = pd.read_csv(\"wiki_qa_bart_10000row.csv\")\n", "sampled_output_df\n", "# for index, row in sampled_output_df[:50].iterrows():\n", "#     print(index)\n", "#     print(row['query'])\n", "#     print(row['response'])"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 59, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 who\n", "who wrote passenger to frankfurt\n", "The Passenger to Frankfurt: An Extravaganza is a spy novel by <PERSON>. It was published to mark <PERSON>'s eightieth birthday and was advertised as her eightieth book. It is one of only four of her novels not to have received an adaptation.\n", "1 who\n", "who owns the ifl\n", "The Indoor Football League was founded in 1999. It was an offshoot of the troubled PIFL. The league was successful enough for a major expansion in 2000. After the season, the league was purchased by the Arena Football League's Orlando Predators.\n", "2 who\n", "who is the para equestrian\n", "<PERSON><PERSON><PERSON> is an Australian para-equestrian. She represented Australia at the 2016 Rio Paralympics. <PERSON><PERSON> won a silver medal in the women's individual\n", "3 who\n", "who is architect <PERSON><PERSON><PERSON> <PERSON><PERSON>\n", "<PERSON> was a Dutch architect best known for the ING Bank in Amsterdam and the Gas Corporation headquarters in Groningen. He was involved with Situationist International before being expelled in 1960. In 1963 he founded the firm Albert<PERSON> and <PERSON> with <PERSON>.\n", "4 who\n", "who is gorky\n", "<PERSON><PERSON><PERSON><PERSON> was an Armenian-American painter who had a seminal influence on Abstract Expressionism. Along with <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> has been hailed as one of the most powerful American painters of the 20th century.\n", "1000 where\n", "where is harcourt house\n", "Harcourt House Artist Run Centre is an artist-run, charitable organization that promotes contemporary visual art. The Centre includes two public gallery spaces; and as the single largest community of visual artists in Edmonton, it offers 42 low-rent studios.\n", "1001 where\n", "where is choye commune\n", "Choye is a commune in the Haute-Saône department in the region of Bourgogne-Franche-Comté in eastern France.It is\n", "1002 where\n", "where was kea bouman born\n", "<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON> was a female tennis player from the Netherlands. She won the singles title at the 1927 French Championships. She is the only Dutch woman to have won a Grand Slam singles title.\n", "1003 where\n", "where is perseus arm\n", "Perseus Arm is one of two major spiral arms of the Milky Way galaxy. Previously thought to be 13,000 light-years away, it is now thought to lie 6,400 light years from the Solar System.\n", "1004 where\n", "where is st-livrade france\n", "Sainte-<PERSON>rade is a commune in the Haute-Garonne department in southwestern France.It is located in the south-west of the province of Ha\n", "2000 Unknown\n", "ningal meaning\n", "<PERSON><PERSON><PERSON> was a Mesopotamian goddess of Sumerian origin regarded as the wife of the moon god, <PERSON><PERSON>/<PERSON>. She was particularly closely associated with his main cult centers, Ur and Harran, but they were also worshiped together in other cities of Mesopotamia.\n", "2001 Unknown\n", "vers sur meuge france\n", "Vers-sur-Méouge is a commune in the Drôme department in southeastern France.It is located in the south-east of the department, near the border\n", "2002 Unknown\n", "carl barron comedian\n", "<PERSON> is an Australian theatre and television comedian. His style is based on observational humour. He is best known for his work on the ABC's The Biggest Loser. He has also\n", "2003 Unknown\n", "paradoris caerulea\n", "<PERSON>dor<PERSON> caerulea is a species of sea slug.It is a dorid nudibranch, a shell-less marine opisthobranch gastropod m\n", "2004 Unknown\n", "cast of london town\n", "The film stars <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>. It had its world premiere at the Los Angeles Film Festival on June 3, 2016. The film was released in a limited release and through video on demand on October 7, 2016, by\n", "3000 what\n", "what was longmont potion castle\n", "Longmont Potion Castle is the pseudonym of an anonymous prank caller active in Colorado and the Los Angeles areas since 1986. Details about his personal life are scarce, and his real name is unknown to the public. Over the years\n", "3001 what\n", "what year did the jungle show premiere\n", "\"The Jungle\" is episode 77 of the American television anthology series The Twilight Zone. It first aired on December 1, 1961. It is the 77th episode of the series and the\n", "3002 what\n", "what is bae systems largest export agreement\n", "The first sales occurred in September 1985 and the most recent contract for 72 Eurofighter Typhoon multirole fighters was signed in August 2006. It is Britain's largest ever export agreement, and employs at least 5,000 people in Saudi Arabia.\n", "3003 what\n", "what type of university is quenzhou normal\n", "Quanzhou Normal University is a public university in Quanzhou, Fujian province, People's Republic of China.It is located in the city centre of the city of Quanz\n", "3004 what\n", "what is israel astronomical association\n", "The Israeli Astronomical Association  is an Israeli nonprofit organization. Its purpose is to deepen and distribute the awareness for the field of astronomy among the Israeli public. The organization is based in Tel Aviv\n", "4000 define\n", "defence hockey definition\n", "Defence or defense is a player whose primary responsibility is to prevent the opposing team from scoring. They are often referred to as defencemen, D, D-men or blueliners. In regular play, two defencemen complement three forwards and a goaltender.\n", "4001 define\n", "wein's law definition\n", "Wien approximation is an equation used to describe the short-wavelength  spectrum of thermal radiation. Wien displacement law describes the relationship between the temperature of an object and the peak wavelength or\n", "4002 define\n", "lovingkindness definition\n", "Chesed is an English translation of Chesed, a term found in the Hebrew Bible. Loving-kindness is a term used in Buddhism to refer to love and kindness. Chesed\n", "4003 define\n", "definition of fernandina\n", "<PERSON><PERSON><PERSON><PERSON> is a Spanish name for a woman.It may refer to any of a number of women.It is also known as \"<PERSON>\" or \"<PERSON>rna<PERSON>\"\n", "4004 define\n", "definition of pti\n", " PTI may refer to: PTI, PTi, PTI or PTI-I.It may also refer to the following people: \"PTI\" or \"PTi\n", "5000 when\n", "when was south africa founded\n", "The following lists events that happened during 1891 in South Africa.It was the first year of the Second Boer War, and the first of the Third Boer era in South African history\n", "5001 when\n", "when is the cleveland hustles season\n", "Cleveland Hustles was an hour-long American reality TV series created by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. In it, aspiring entrepreneurs competed to open one of four physical stores in the Gordon Square Arts District under the mentorship of a Cleveland\n", "5002 when\n", "when was 50 cent is the future released\n", "50 Cent Is the Future is the second mixtape by American rapper 50 Cent and first one by his rap group G-Unit. It was released on June 1, 2002 via Street Dance/Thurd World Muzic. The lone guest appearance is provided by UTP, which marks the first collaboration between the group and future member <PERSON>.\n", "5003 when\n", "when is 343\n", "Year 343 was a common year starting on Saturday of the Julian calendar. At the time, it was known as the Year of the Consulship of <PERSON><PERSON><PERSON> and R<PERSON>ulus. The denomination 343 for this year has been used since the early medieval\n", "5004 when\n", "when was lonely places by august der<PERSON><PERSON> released\n", "Lonesome Places is a collection of fantasy and horror short stories by American author <PERSON>. It was released in 1962 by Arkham House in an edition of 2,201 copies.\n", "6000 how\n", "victoria's parliament has how many members\n", "The Parliament of Victoria is the bicameral legislature of the Australian state of Victoria. It consists of the Queen, the Legislative Assembly and the Legislative Council. The current Parliament was elected on 24 November 2018, sworn in on 19 December 2018.\n", "6001 how\n", "how many scottish mps in burgh constituency\n", "New constituency boundaries were first used in the 1918 general election. In Scotland the legislation defined 32 burgh constituencies, 38 county constituencies and one university constituency. For the 1950 general election, new boundaries were introduced under the House of Commons  Act 1949.\n", "6002 how\n", "how many years ago did christopher knowles start painting\n", "<PERSON> is an American poet and painter. In 1976, his poetry was used by <PERSON> for the avant-garde minimalist Philip Glass opera, Einstein on the Beach. <PERSON> has received a diagnosis of possible brain damage. He is often referred to as autistic.\n", "6003 how\n", "how many universities in turkey\n", "There are 207 universities and academies in Turkey. There are 129 state universities, 74 private foundation universities, four two-year granting institutions. This is a list of universities in Turkey, sorted\n", "6004 how\n", "how many federated states\n", "The following table lists Mexico's 32 federated entities, ranked by total continental surface. The table is ordered by the size of the country's continental surface, with the highest ranked nation at the\n", "7000 which\n", "which region is tettnang\n", "Tettnang is a town in the Bodensee district in southern Baden-Württemberg in a region of Germany known as Swabia. It lies 7 kilometres from Lake Constance. The region\n", "7001 which\n", "which family is the antbirds\n", "The Grallariidae are a family of small passerine birds of Central and South America. They lay 1 to 6 eggs in a nest in a tree, both sexes incubating. The long, powerful legs and an essentially vestigial tail aid this lifestyle.\n", "7002 which\n", "which ncaa division is eastern michigan\n", "The 1981 Eastern Michigan Hurons football team represented Eastern Michigan University in the 1981 NCAA Division I-A football season. The Hurons finished in last place in the Mid-American Conference. The team's statistical leaders included <PERSON><PERSON><PERSON><PERSON> with 1,391 passing yards, <PERSON> with 971 rushing yards, and <PERSON> with 440 receiving yards.\n", "7003 which\n", "which nobleman was george gordon\n", "<PERSON>, 1st Marquess of Huntly was a Scottish nobleman. He took a leading role in the political and military life of Scotland in the late 16th century, and around\n", "7004 which\n", "which champions are defending champions?\n", "<PERSON> was the defending champion but chose not to participate.<PERSON><PERSON> won the title after defeating <PERSON><PERSON> 1-6, 6-1,\n", "8000 why\n", "why did grant surrender fort henry\n", "The Battle of Fort Henry was fought on February 6, 1862, in Donelson, Stewart County, Tennessee. It was the first important victory for the Union and Brig. Gen. <PERSON> in the Western Theater.\n", "8001 why\n", "why was the gal and pal invented\n", "The Generic Array Logic was an innovation of the PAL. It was able to take the place of many PAL device types. A similar device called a PEEL was introduced by the International CMOS Technology  corporation.\n", "8002 why\n", "why did the last us space shuttle leave earth after the biosatellite mission\n", "Biosatellite 3 was the last unmanned U.S. Biosatellite program for biological research. The intent had been to fly a 6 kg male Southern pig-tailed macaque in Earth-orbit for 30 days. However, after only 8.8 days in orbit, the mission was terminated because of the subject's deteriorating health.\n", "8003 why\n", "why is austria a good tourist destination\n", "Austria has one guest bed for every six inhabitants. In 2007, Austria ranked 9th worldwide in international tourism receipts, with 18.9 billion US$. In international tourist arrivals, Austriaranked 12th with 30.8 million tourists.\n", "8004 why\n", "why was compass a failure\n", "Operation Compass was the first large British military operation of the Western Desert Campaign during the Second World War. British Empire forces attacked Italian forces of the 10th Army  in western Egypt and Cyrenaica, the eastern province of Libya, from December 1940 to February 1941.\n", "9000 is/are/was/were\n", "washington state route 97\n", "Wagon roads along the west side of the Columbia River were plotted in the 1880s by American settlers. The wagon road was designated as State Road 10 and was completely paved by the late 1930s. US 97 was created in 1926 and followed the highway, which was relocated in the 1950s due to the construction of the Rocky Reach Dam.\n", "9001 is/are/was/were\n", "is red mountain pass colorado\n", "Red Mountain Pass is a mountain pass in the San Juan Mountains of western Colorado. Red Mountain Pass elevation  is 1,071 feet. Red mountain pass is in the western San Juan\n", "9002 is/are/was/were\n", "was clayton waagner a bank robber\n", "<PERSON> is a convicted bank robber and anti-abortion terrorist. He was the FBI's 467th fugitive to be placed on the Ten Most Wanted list on September 21, 2001. He is currently in prison.\n", "9003 is/are/was/were\n", "is myrrh a flower?\n", "Myrrh is a gum-resin extracted from a number of small, thorny tree species of the genus Commiphora. Myrrh resin has been used throughout history as a perfume, incense and medicine.\n", "9004 is/are/was/were\n", "are there first nations in saskatchewan canada\n", "First Nations ethnicities in the province include the Cree, Assiniboine, Saulteaux, Lakota, Dene and Dakota. Historically, the Atsina and Blackfoot could also be found at various times.\n"]}], "source": ["parents = sampled_output_df[\"parent\"].unique()\n", "for parent in parents:\n", "    for index, row in sampled_output_df[sampled_output_df[\"parent\"] == parent].head(5).iterrows():\n", "        print(index, parent)\n", "        print(row[\"query\"])\n", "        print(row[\"response\"])"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 64, "outputs": [{"data": {"text/plain": "                                                 query  \\\n0                     who wrote passenger to frankfurt   \n1                                     who owns the ifl   \n2                           who is the para equestrian   \n3                   who is architect ale<PERSON><PERSON> <PERSON>   \n4                                         who is gorky   \n..                                                 ...   \n995                    who is the director of cornered   \n996                            who was mi<PERSON> kassa<PERSON>   \n997  who was the first leader of the third hellenic...   \n998                                 who was is<PERSON><PERSON><PERSON>?   \n999                                   who is ethridge?   \n\n                                              response  \n0    The Passenger to Frankfurt: An Extravaganza is...  \n1    The Indoor Football League was founded in 1999...  \n2    <PERSON><PERSON><PERSON> is an Australian para-eques...  \n3    <PERSON> was a Dutch architect best known...  \n4    <PERSON><PERSON><PERSON><PERSON> was an Armenian-American painter...  \n..                                                 ...  \n995  Cornered is a 1945 film noir starring <PERSON>...  \n996  <PERSON><PERSON><PERSON><PERSON> was an Armenian-Americ...  \n997  <PERSON><PERSON><PERSON> was a Greek jurist and p...  \n998  <PERSON><PERSON><PERSON> was a Jewish Holocaust survi...  \n999  Ethridge is a British surname.It may refer to ...  \n\n[1000 rows x 2 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>query</th>\n      <th>response</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>who wrote passenger to frankfurt</td>\n      <td>The Passenger to Frankfurt: An Extravaganza is...</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>who owns the ifl</td>\n      <td>The Indoor Football League was founded in 1999...</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>who is the para equestrian</td>\n      <td>Katie-<PERSON> is an Australian para-eques...</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>who is architect ale<PERSON><PERSON> al<PERSON><PERSON></td>\n      <td><PERSON> was a Dutch architect best known...</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>who is gorky</td>\n      <td>Arshile Gorky was an Armenian-American painter...</td>\n    </tr>\n    <tr>\n      <th>...</th>\n      <td>...</td>\n      <td>...</td>\n    </tr>\n    <tr>\n      <th>995</th>\n      <td>who is the director of cornered</td>\n      <td>Cornered is a 1945 film noir starring Dick Pow...</td>\n    </tr>\n    <tr>\n      <th>996</th>\n      <td>who was mikra kassabian</td>\n      <td>Mihran Krikor Kassabian was an Armenian-Americ...</td>\n    </tr>\n    <tr>\n      <th>997</th>\n      <td>who was the first leader of the third hellenic...</td>\n      <td>Michail Stasinopoulos was a Greek jurist and p...</td>\n    </tr>\n    <tr>\n      <th>998</th>\n      <td>who was isacovici?</td>\n      <td>Salomon Isacovici was a Jewish Holocaust survi...</td>\n    </tr>\n    <tr>\n      <th>999</th>\n      <td>who is ethridge?</td>\n      <td>Ethridge is a British surname.It may refer to ...</td>\n    </tr>\n  </tbody>\n</table>\n<p>1000 rows × 2 columns</p>\n</div>"}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["sampled_output_df[sampled_output_df[\"parent\"] == \"who\"][[\"query\", \"response\"]]"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 44, "outputs": [{"data": {"text/plain": "id                                                    1704412\nurl         https://en.wikipedia.org/wiki/1891%20in%20Sout...\ntext        The following lists events that happened durin...\ntitle                                    1891 in South Africa\nword_num                                                   11\nquery                           when was south africa founded\nparent                                                   when\nresponse    The following lists events that happened durin...\nName: 500, dtype: object"}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["sampled_output_df.iloc[500]"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}], "metadata": {"kernelspec": {"name": "searchgpt_oa_bart", "language": "python", "display_name": "searchgpt_oa_bart"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}