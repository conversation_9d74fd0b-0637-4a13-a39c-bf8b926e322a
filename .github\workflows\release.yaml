name: Release

on:
  push:
    branches:
      - main
  release:
    types:
      - released

jobs:
  pre-commit:
    uses: ./.github/workflows/pre-commit.yaml
  build-backend:
    uses: ./.github/workflows/docker-build.yaml
    needs: pre-commit
    with:
      image-name: oasst-backend
      context: .
      dockerfile: docker/Dockerfile.backend
      build-args: ""
  build-backend-worker:
    uses: ./.github/workflows/docker-build.yaml
    needs: pre-commit
    with:
      image-name: oasst-backend-worker
      context: .
      dockerfile: docker/Dockerfile.backend-worker
      build-args: ""
  build-web:
    uses: ./.github/workflows/docker-build.yaml
    needs: pre-commit
    with:
      image-name: oasst-web
      context: .
      dockerfile: docker/Dockerfile.website
      build-args: ""
  build-bot:
    uses: ./.github/workflows/docker-build.yaml
    needs: pre-commit
    with:
      image-name: oasst-discord-bot
      context: .
      dockerfile: docker/Dockerfile.discord-bot
      build-args: ""
  build-inference-server:
    uses: ./.github/workflows/docker-build.yaml
    needs: pre-commit
    with:
      image-name: oasst-inference-server
      context: .
      dockerfile: docker/inference/Dockerfile.server
      build-args: ""
  build-inference-worker-full:
    uses: ./.github/workflows/docker-build.yaml
    needs: pre-commit
    with:
      image-name: oasst-inference-worker-full
      context: .
      dockerfile: docker/inference/Dockerfile.worker-full
      build-args: ""
  build-inference-worker-hf:
    uses: ./.github/workflows/docker-build.yaml
    needs: pre-commit
    with:
      image-name: oasst-inference-worker-hf
      context: .
      dockerfile: docker/inference/Dockerfile.worker-hf
      build-args: ""
  build-inference-worker-standalone:
    uses: ./.github/workflows/docker-build.yaml
    needs: pre-commit
    with:
      image-name: oasst-inference-worker-standalone
      context: .
      dockerfile: docker/inference/Dockerfile.worker-standalone
      build-args: ""
  build-inference-safety:
    uses: ./.github/workflows/docker-build.yaml
    needs: pre-commit
    with:
      image-name: oasst-inference-safety
      context: .
      dockerfile: docker/inference/Dockerfile.safety
      build-args: ""
  deploy-to-node:
    needs:
      - build-backend
      - build-backend-worker
      - build-web
      - build-bot
      - build-inference-server
    uses: ./.github/workflows/deploy-to-node.yaml
    secrets: inherit
    with:
      stack-name: ${{ github.event_name == 'release' && 'staging' || 'dev' }}
      image-tag:
        ${{ github.event_name == 'release' && github.event.release.tag_name ||
        'latest' }}
      backend-port: ${{ github.event_name == 'release' && '8180' || '8080' }}
      website-port: ${{ github.event_name == 'release' && '3100' || '3000' }}
      inference-server-port:
        ${{ github.event_name == 'release' && '8185' || '8085' }}
