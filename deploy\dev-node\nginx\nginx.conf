events {}
http {
	server {
		listen 80;
		listen [::]:80;

		server_name *.open-assistant.io;
		server_tokens off;

		location /.well-known/acme-challenge/ {
			root /var/www/certbot;
		}

		location / {
			return 301 https://$host$request_uri;
		}
	}

	server {
		listen 443 ssl http2;

		server_name web.dev.open-assistant.io;

		ssl_certificate /etc/nginx/ssl/live/web.dev.open-assistant.io/fullchain.pem;
		ssl_certificate_key /etc/nginx/ssl/live/web.dev.open-assistant.io/privkey.pem;

		location / {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://127.0.0.1:3000;

			proxy_set_header Connection '';
			proxy_http_version 1.1;
			chunked_transfer_encoding off;
			proxy_buffering off;
			proxy_cache off;
		}
	}

	server {
		listen 443 ssl http2;

		server_name backend.dev.open-assistant.io;

		ssl_certificate /etc/nginx/ssl/live/backend.dev.open-assistant.io/fullchain.pem;
		ssl_certificate_key /etc/nginx/ssl/live/backend.dev.open-assistant.io/privkey.pem;

		location / {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://127.0.0.1:8080;
		}
	}

	map $http_upgrade $connection_upgrade {
		default upgrade;
		''      close;
	}

	server {
		listen 443 ssl http2;

		server_name inference.dev.open-assistant.io;

		ssl_certificate /etc/nginx/ssl/live/inference.dev.open-assistant.io/fullchain.pem;
		ssl_certificate_key /etc/nginx/ssl/live/inference.dev.open-assistant.io/privkey.pem;

		location / {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;

			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Forwarded-Proto $scheme;
			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection $connection_upgrade;
			proxy_redirect off;
			proxy_buffering off;

			proxy_pass http://127.0.0.1:8085;
		}
	}




	server {
		listen 443 ssl http2;

		server_name web.staging.open-assistant.io;

		ssl_certificate /etc/nginx/ssl/live/web.staging.open-assistant.io/fullchain.pem;
		ssl_certificate_key /etc/nginx/ssl/live/web.staging.open-assistant.io/privkey.pem;

		location / {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://127.0.0.1:3100;

			proxy_set_header Connection '';
			proxy_http_version 1.1;
			chunked_transfer_encoding off;
			proxy_buffering off;
			proxy_cache off;
		}
	}

	server {
		listen 443 ssl http2;

		server_name backend.staging.open-assistant.io;

		ssl_certificate /etc/nginx/ssl/live/backend.staging.open-assistant.io/fullchain.pem;
		ssl_certificate_key /etc/nginx/ssl/live/backend.staging.open-assistant.io/privkey.pem;

		location / {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://127.0.0.1:8180;
		}
	}

	server {
		listen 443 ssl http2;

		server_name inference.staging.open-assistant.io;

		ssl_certificate /etc/nginx/ssl/live/inference.staging.open-assistant.io/fullchain.pem;
		ssl_certificate_key /etc/nginx/ssl/live/inference.staging.open-assistant.io/privkey.pem;

		location / {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;

			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Forwarded-Proto $scheme;
			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection $connection_upgrade;
			proxy_redirect off;
			proxy_buffering off;

			proxy_pass http://127.0.0.1:8185;
		}
	}


}
