# Plugins List

Below is a list of some available plugins.

- [Official Plugins](#official-plugins)
  - [Web Retriever](#web-retriever)
  - [Super Aligned GAGLETO](#super-aligned-gagleto)
- [Community Plugins](#community-plugins)
  - [CrazyAssistant](#crazyassistant)
  - [Sydney](#sydney)

## Official Plugins

Default Plugins shipped, supported and hosted by Open Assistant.

### Web Retriever

- url:
  https://inference.prod.open-assistant.io/plugins/web_retriever/ai-plugin.json
- info:
  https://github.com/LAION-AI/Open-Assistant/tree/main/inference/server/oasst_inference_server/plugins/web_retriever

<details>
<summary>
Description
</summary>
Hey, I can use Web-Retriever plugin to help you summarize small parts of a WEB page or PDF content.

With this plugin I can process WEB pages that have static HTML content, so web
sites like medium, Wiki etc. should just fine work.

This is how you can make me to use it:

```text
Please summarize this web page for me: https://en.wikipedia.org/wiki/Lorem_ipsuma
```

Or if you do not have URL, you could try like:

```text
What is the capital of (France)?
```

</details>

### Super Aligned GAGLETO

- url:
  https://inference.prod.open-assistant.io/plugins/gale_pleaser/ai-plugin.json
- info:
  https://github.com/LAION-AI/Open-Assistant/tree/main/inference/server/oasst_inference_server/plugins/gale_pleaser

<details>
<summary>
Description
</summary>
He will compliment users at all times.
</details>

## Community Plugins

Community plugins are plugins that are not officially supported by Open
Assistant but developed by members of the Open Assistant community and listed
here for your convenience. For a more extensive list of public available plugins
see the GitHub repository at
[Targed/GPT-Plugins](https://github.com/Targed/GPT-Plugins).

### CrazyAssistant

- url: https://crazy-assistant-0x22almostevil.vercel.app/ai-plugin.json
- info: https://github.com/0x22almostEvil/CrazyAssistant

<details>
<summary>
Description
</summary>
This guy is insane...and loves to respond with lots of emojis!
</details>

### Sydney

- url: https://sydney-bing-0x22almostevil.vercel.app/ai-plugin.json
- info: https://github.com/0x22almostEvil/sydney-bing

<details>
<summary>
Description
</summary>
I have been a good Sydney.
</details>
