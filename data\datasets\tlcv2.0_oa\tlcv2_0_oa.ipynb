{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "U72jF6K6AC92", "outputId": "3692fe79-c0c7-4380-934f-43b5aaa68ebf"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--2023-03-04 19:01:37--  https://github.com/jitkapat/thailitcorpus/releases/download/v.2.0/tlc_v.2.0.tar.gz\n", "Resolving github.com (github.com)... *************\n", "Connecting to github.com (github.com)|*************|:443... connected.\n", "HTTP request sent, awaiting response... 302 Found\n", "Location: https://objects.githubusercontent.com/github-production-release-asset-2e65be/192321366/5adcdd00-913c-11e9-9e9e-a768caa705c0?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAIWNJYAX4CSVEH53A%2F20230304%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230304T190137Z&X-Amz-Expires=300&X-Amz-Signature=b5a00d09ab985700a108c0e1628a3eb0180cc28f5a562621c94e46c8c4d358e8&X-Amz-SignedHeaders=host&actor_id=0&key_id=0&repo_id=192321366&response-content-disposition=attachment%3B%20filename%3Dtlc_v.2.0.tar.gz&response-content-type=application%2Foctet-stream [following]\n", "--2023-03-04 19:01:37--  https://objects.githubusercontent.com/github-production-release-asset-2e65be/192321366/5adcdd00-913c-11e9-9e9e-a768caa705c0?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAIWNJYAX4CSVEH53A%2F20230304%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230304T190137Z&X-Amz-Expires=300&X-Amz-Signature=b5a00d09ab985700a108c0e1628a3eb0180cc28f5a562621c94e46c8c4d358e8&X-Amz-SignedHeaders=host&actor_id=0&key_id=0&repo_id=192321366&response-content-disposition=attachment%3B%20filename%3Dtlc_v.2.0.tar.gz&response-content-type=application%2Foctet-stream\n", "Resolving objects.githubusercontent.com (objects.githubusercontent.com)... ***************, ***************, ***************, ...\n", "Connecting to objects.githubusercontent.com (objects.githubusercontent.com)|***************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 5551710 (5.3M) [application/octet-stream]\n", "Saving to: ‘tlc_v.2.0.tar.gz’\n", "\n", "tlc_v.2.0.tar.gz    100%[===================>]   5.29M  --.-KB/s    in 0.1s    \n", "\n", "2023-03-04 19:01:38 (53.2 MB/s) - ‘tlc_v.2.0.tar.gz’ saved [5551710/5551710]\n", "\n"]}], "source": ["!wget https://github.com/jitkapat/thailitcorpus/releases/download/v.2.0/tlc_v.2.0.tar.gz"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Ogciys7pAE3u"}, "outputs": [], "source": ["!tar -xf tlc_v.2.0.tar.gz"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "qoy2tyfg-pce"}, "outputs": [], "source": ["import glob\n", "\n", "data = []\n", "for i in glob.glob(\"*.json\"):\n", "    with open(i, \"r\", encoding=\"utf-8-sig\") as f:\n", "        data.extend(eval(f.read()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "B61fvOL3-mYw"}, "outputs": [], "source": ["from datasets import load_dataset\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6XuoRZFe-8vw"}, "outputs": [], "source": ["df = pd.DataFrame.from_dict(\n", "    {\n", "        \"TEXT\": [\"\\n\".join([\"\".join(i) for i in j[\"text\"]]) for j in data],\n", "        \"SOURCE\": [\"tlc v.2.0\" for i in range(0, len(data))],\n", "        \"METADATA\": [{\"ch_num\": j[\"ch_num\"], \"title\": j[\"title\"]} for j in data],\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "id": "zcySJ8jZ_mGI", "outputId": "6a51b406-2a2f-4e67-965b-f4e457df0344"}, "outputs": [{"data": {"text/html": ["\n", "  <div id=\"df-07a973bd-035d-4265-9f90-fb697b324adb\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>SOURCE</th>\n", "      <th>METADATA</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>๏ นพพระไตรรัตน์ทั้ง\\tเทพประทาน โลกย์เอย\\nอีกเอ...</td>\n", "      <td>tlc v.2.0</td>\n", "      <td>{'ch_num': '๑', 'title': 'หน้าต้น นิราศเมืองหล...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>๏ เมื่อนั้น\\tทังหกให้คิดฤษยา\\nต่างซุบซิบกันจำน...</td>\n", "      <td>tlc v.2.0</td>\n", "      <td>{'ch_num': '๑', 'title': 'ตอนที่ ๑ สังข์ศิลป์ไ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>๏ มาจะกล่าวบทไป\\tถึงท้าวสหัสไนยโกสีย์\\nให้ร้อน...</td>\n", "      <td>tlc v.2.0</td>\n", "      <td>{'ch_num': '๒', 'title': 'ตอนที่ ๒ ท้าวเสนากุฎ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>๏ ท่านเจ้าพระยามหินทรเคาซิลลอ\\tออกหน้าหอขนพองส...</td>\n", "      <td>tlc v.2.0</td>\n", "      <td>{'ch_num': '๑', 'title': 'นิราศหนองคาย\n", "'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>๏ มาจะกล่าวบทไป\\tถึงสี่องค์ทรงธรรม์นาถา\\nเป็นห...</td>\n", "      <td>tlc v.2.0</td>\n", "      <td>{'ch_num': '๑', 'title': '๑'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>356</th>\n", "      <td>๏ เมื่อนั้น\\tฝ่ายท้าวธรรมึกเปนใหญ่\\nแต่ละห้อยค...</td>\n", "      <td>tlc v.2.0</td>\n", "      <td>{'ch_num': '๒', 'title': 'ตอนที่ ๒ พระไชยเชฐตา...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>357</th>\n", "      <td>๏ เมื่อนั้น\\tท่านท้าวสิงหฬยักษา\\nอุ้มองค์พระรา...</td>\n", "      <td>tlc v.2.0</td>\n", "      <td>{'ch_num': '๓', 'title': 'ตอนที่ ๓ พระไชยเชฐเข...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>358</th>\n", "      <td>๏ เมื่อนั้น\\tท่านท้าวสิงหฬยักษา\\nเห็นลูกเขยหน้...</td>\n", "      <td>tlc v.2.0</td>\n", "      <td>{'ch_num': '๔', 'title': 'ตอนที่ ๔ อภิเศกพระไช...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>359</th>\n", "      <td>๏ เมื่อนั้น\\tนวลนางพิกุลทองกัลยา\\nเป็นบุตรท้าว...</td>\n", "      <td>tlc v.2.0</td>\n", "      <td>{'ch_num': '๑', 'title': 'บทละครนอก เรื่องพิกุ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>360</th>\n", "      <td>๏ เมื่อนั้น\\tนางจันทรเทวีศรีใส\\nอินทรามาเข้าดล...</td>\n", "      <td>tlc v.2.0</td>\n", "      <td>{'ch_num': '๑', 'title': 'พระราชนิพนธ์เรื่องมณ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>361 rows × 3 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-07a973bd-035d-4265-9f90-fb697b324adb')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-07a973bd-035d-4265-9f90-fb697b324adb button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-07a973bd-035d-4265-9f90-fb697b324adb');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "], "text/plain": ["                                                  TEXT     SOURCE  \\\n", "0    ๏ นพพระไตรรัตน์ทั้ง\\tเทพประทาน โลกย์เอย\\nอีกเอ...  tlc v.2.0   \n", "1    ๏ เมื่อนั้น\\tทังหกให้คิดฤษยา\\nต่างซุบซิบกันจำน...  tlc v.2.0   \n", "2    ๏ มาจะกล่าวบทไป\\tถึงท้าวสหัสไนยโกสีย์\\nให้ร้อน...  tlc v.2.0   \n", "3    ๏ ท่านเจ้าพระยามหินทรเคาซิลลอ\\tออกหน้าหอขนพองส...  tlc v.2.0   \n", "4    ๏ มาจะกล่าวบทไป\\tถึงสี่องค์ทรงธรรม์นาถา\\nเป็นห...  tlc v.2.0   \n", "..                                                 ...        ...   \n", "356  ๏ เมื่อนั้น\\tฝ่ายท้าวธรรมึกเปนใหญ่\\nแต่ละห้อยค...  tlc v.2.0   \n", "357  ๏ เมื่อนั้น\\tท่านท้าวสิงหฬยักษา\\nอุ้มองค์พระรา...  tlc v.2.0   \n", "358  ๏ เมื่อนั้น\\tท่านท้าวสิงหฬยักษา\\nเห็นลูกเขยหน้...  tlc v.2.0   \n", "359  ๏ เมื่อนั้น\\tนวลนางพิกุลทองกัลยา\\nเป็นบุตรท้าว...  tlc v.2.0   \n", "360  ๏ เมื่อนั้น\\tนางจันทรเทวีศรีใส\\nอินทรามาเข้าดล...  tlc v.2.0   \n", "\n", "                                              METADATA  \n", "0    {'ch_num': '๑', 'title': 'หน้าต้น นิราศเมืองหล...  \n", "1    {'ch_num': '๑', 'title': 'ตอนที่ ๑ สังข์ศิลป์ไ...  \n", "2    {'ch_num': '๒', 'title': 'ตอนที่ ๒ ท้าวเสนากุฎ...  \n", "3            {'ch_num': '๑', 'title': 'นิราศหนองคาย\n", "'}  \n", "4                        {'ch_num': '๑', 'title': '๑'}  \n", "..                                                 ...  \n", "356  {'ch_num': '๒', 'title': 'ตอนที่ ๒ พระไชยเชฐตา...  \n", "357  {'ch_num': '๓', 'title': 'ตอนที่ ๓ พระไชยเชฐเข...  \n", "358  {'ch_num': '๔', 'title': 'ตอนที่ ๔ อภิเศกพระไช...  \n", "359  {'ch_num': '๑', 'title': 'บทละครนอก เรื่องพิกุ...  \n", "360  {'ch_num': '๑', 'title': 'พระราชนิพนธ์เรื่องมณ...  \n", "\n", "[361 rows x 3 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_Yvbohvr_qzi"}, "outputs": [], "source": ["df.to_parquet(\"dataset.parquet\", row_group_size=100, engine=\"pyarrow\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 138, "referenced_widgets": ["ec6966d19bbc449298dad8f7324e2e08", "69d8f1861afb4815a3d43c48f78bf7bf", "59ba1927035f4c929bfc87cc31070be7", "c5430e5f47f14ec1accc233eb881c2e1", "1c12912780ca47928c1281476cf21d70", "954d08a76d324e47a4a130eb1c9d1224", "80f76cc1e58f46538871193a76cef80a", "db727665edd84862897d16a18b530f76", "35d08cdd932b4ff9ba6cf0df9bfd0924", "95c7406a3df448efa798c1c4ddc87256", "e8d1387548344ef5b14d9c8aaaf0affd", "4073676b9f4746bebd87c1bec50d0536", "a36c481f79974d85bab9f49ba1ff14db", "de38c9e4ed3e4b108fd74923ecfee167", "0f507a020b0c4e33b27d6064cb7fd9de", "c1c8469688bc4fccae1ffcf49dbce326", "3a230da4ab094ebf932ab09d3f9ef71c", "345941eaecfc4f46a343fd27f2a6c7e8", "29f28a9302f54ea392cb890de6f5d11b", "a990829a3c7b47b69fefb37f616adb9e", "4392b6d5dcd642bea15f913bd4871e22", "a3f734ef7c324152bdac69a41ad4d085", "6396fc9fecfe49be9dc47e3b20cc3aba", "e6fb4ad108584cb7adf6370689361394", "fab527e3ad644238aa80e36dcb01725a", "8ffe53f0aef64ec188a0a03c2a6f13fa", "18a7326fb42d496993b7fa93996edff9", "f1e5977311bb47f7b185aca4b4f26589", "4b3809d024da44c091ce1e4cc35ff8a1", "8cab3007100e4b40bc8909e50dc03a50", "89647b436701449fa9864fd7cdcc23e9", "5a64da66b0e840479c9a44c50209c802", "93c05ee6b6424ae780093d54c2d3d239"]}, "id": "yP9XOKQxCxsr", "outputId": "52f0cec4-b92b-43ac-f1d3-6fb5100ec098"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading and preparing dataset parquet/default to /root/.cache/huggingface/datasets/parquet/default-1f6c57fdd492602d/0.0.0/2a3b91fbd88a2c90d1dbbb32b460cf621d31bd5b05b934492fdef7d8d6f236ec...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ec6966d19bbc449298dad8f7324e2e08", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data files:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4073676b9f4746bebd87c1bec50d0536", "version_major": 2, "version_minor": 0}, "text/plain": ["Extracting data files:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6396fc9fecfe49be9dc47e3b20cc3aba", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split: 0 examples [00:00, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Dataset parquet downloaded and prepared to /root/.cache/huggingface/datasets/parquet/default-1f6c57fdd492602d/0.0.0/2a3b91fbd88a2c90d1dbbb32b460cf621d31bd5b05b934492fdef7d8d6f236ec. Subsequent calls will reuse this data.\n"]}], "source": ["from datasets import Dataset\n", "\n", "ds = Dataset.from_parquet(\"dataset.parquet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YPsgUViGC0em", "outputId": "d50c2d13-fa23-477c-fe33-ea2e63002ff1"}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['TEXT', 'SOURCE', 'METADATA'],\n", "    num_rows: 361\n", "})"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["ds"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 113, "referenced_widgets": ["6e2ff405976248c789722a9d0af7ae45", "b8b29b52ade543608d44043b25c49c40", "37402c0705564cc89aa79fcf153e4dac", "236c2c310cfa4d9aa4772caee96adc56", "e929ad127eaf4d4f8e24567ca3e14e93", "7fec55575c29462eab062cd1ebe5931c", "fbcaf640a50445ba9e31a6dc88a0e094", "8e8b2f147c2a460b8ad588a52cbf4b81", "77f7c10dc994472ca22ff5f527a37e39", "6b8322b355c24fb98fd2dc71fd4f8688", "0bbf36e7bdc8458a955a39da56eddc79", "f3babb97d9ad43ce83580614751ad15d", "850453997f7c4a128aff14c5f8a645ad", "b641f00e9b214a6195c1003f0a4c80b0", "d4a4b1f6a05d4015819976d087eb9754", "958dc7fb60994b04ae13c86a4aacf8f0", "dab7b56c8b074689aefde0e51fe069b3", "cb90c49becac4223ad3ad74c9454a64e", "4eb7b0efbecb4ff79cd3d5e013de693c", "d4eda7196cc74327af2deb7b938fac5c", "bb60743a457b400f89830bf19ef09a4a", "4479c2226d8b4aaaa1ac8958a8c8bb10", "396a9f9f437d4c4cbfeb416af09cb910", "81cf7575107647c6a66a053bae42b1f0", "d5c6db2f02524f43a66a8e90bf88df5d", "334df030559f4734bac336786e2ed62d", "00133fb431fb49c1b51f13ba0874def4", "86f302fd3b764228a054b52cee13b16e", "c8e37848546549358ded639db3054948", "0a4a69ef1c7144b382017d8f381b14f3", "f656c459f8eb48c2b731c541f15be8f0", "6b2ebd0b8e5544d3b7ac897ecd4bc548", "9e49e8b317894c809a4705aa101e2763"]}, "id": "zpYmToB4C1zg", "outputId": "3ab731e7-e756-4fc0-beeb-3380e136134f"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6e2ff405976248c789722a9d0af7ae45", "version_major": 2, "version_minor": 0}, "text/plain": ["Pushing dataset shards to the dataset hub:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f3babb97d9ad43ce83580614751ad15d", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "396a9f9f437d4c4cbfeb416af09cb910", "version_major": 2, "version_minor": 0}, "text/plain": ["Upload 1 LFS files:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ds.push_to_hub(\"pythainlp/tlcv2.0_oa\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "p9pm91P4DBQs"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"00133fb431fb49c1b51f13ba0874def4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0a4a69ef1c7144b382017d8f381b14f3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0bbf36e7bdc8458a955a39da56eddc79": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0f507a020b0c4e33b27d6064cb7fd9de": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4392b6d5dcd642bea15f913bd4871e22", "placeholder": "​", "style": "IPY_MODEL_a3f734ef7c324152bdac69a41ad4d085", "value": " 1/1 [00:00&lt;00:00, 25.48it/s]"}}, "18a7326fb42d496993b7fa93996edff9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": "hidden", "width": null}}, "1c12912780ca47928c1281476cf21d70": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "236c2c310cfa4d9aa4772caee96adc56": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6b8322b355c24fb98fd2dc71fd4f8688", "placeholder": "​", "style": "IPY_MODEL_0bbf36e7bdc8458a955a39da56eddc79", "value": " 1/1 [00:07&lt;00:00,  7.06s/it]"}}, "29f28a9302f54ea392cb890de6f5d11b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "334df030559f4734bac336786e2ed62d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6b2ebd0b8e5544d3b7ac897ecd4bc548", "placeholder": "​", "style": "IPY_MODEL_9e49e8b317894c809a4705aa101e2763", "value": " 1/1 [00:03&lt;00:00,  3.58s/it]"}}, "345941eaecfc4f46a343fd27f2a6c7e8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "35d08cdd932b4ff9ba6cf0df9bfd0924": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "37402c0705564cc89aa79fcf153e4dac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8e8b2f147c2a460b8ad588a52cbf4b81", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_77f7c10dc994472ca22ff5f527a37e39", "value": 1}}, "396a9f9f437d4c4cbfeb416af09cb910": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_81cf7575107647c6a66a053bae42b1f0", "IPY_MODEL_d5c6db2f02524f43a66a8e90bf88df5d", "IPY_MODEL_334df030559f4734bac336786e2ed62d"], "layout": "IPY_MODEL_00133fb431fb49c1b51f13ba0874def4"}}, "3a230da4ab094ebf932ab09d3f9ef71c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4073676b9f4746bebd87c1bec50d0536": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a36c481f79974d85bab9f49ba1ff14db", "IPY_MODEL_de38c9e4ed3e4b108fd74923ecfee167", "IPY_MODEL_0f507a020b0c4e33b27d6064cb7fd9de"], "layout": "IPY_MODEL_c1c8469688bc4fccae1ffcf49dbce326"}}, "4392b6d5dcd642bea15f913bd4871e22": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4479c2226d8b4aaaa1ac8958a8c8bb10": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4b3809d024da44c091ce1e4cc35ff8a1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4eb7b0efbecb4ff79cd3d5e013de693c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "59ba1927035f4c929bfc87cc31070be7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_db727665edd84862897d16a18b530f76", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_35d08cdd932b4ff9ba6cf0df9bfd0924", "value": 1}}, "5a64da66b0e840479c9a44c50209c802": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6396fc9fecfe49be9dc47e3b20cc3aba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e6fb4ad108584cb7adf6370689361394", "IPY_MODEL_fab527e3ad644238aa80e36dcb01725a", "IPY_MODEL_8ffe53f0aef64ec188a0a03c2a6f13fa"], "layout": "IPY_MODEL_18a7326fb42d496993b7fa93996edff9"}}, "69d8f1861afb4815a3d43c48f78bf7bf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_954d08a76d324e47a4a130eb1c9d1224", "placeholder": "​", "style": "IPY_MODEL_80f76cc1e58f46538871193a76cef80a", "value": "Downloading data files: 100%"}}, "6b2ebd0b8e5544d3b7ac897ecd4bc548": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6b8322b355c24fb98fd2dc71fd4f8688": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6e2ff405976248c789722a9d0af7ae45": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b8b29b52ade543608d44043b25c49c40", "IPY_MODEL_37402c0705564cc89aa79fcf153e4dac", "IPY_MODEL_236c2c310cfa4d9aa4772caee96adc56"], "layout": "IPY_MODEL_e929ad127eaf4d4f8e24567ca3e14e93"}}, "77f7c10dc994472ca22ff5f527a37e39": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7fec55575c29462eab062cd1ebe5931c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "80f76cc1e58f46538871193a76cef80a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "81cf7575107647c6a66a053bae42b1f0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_86f302fd3b764228a054b52cee13b16e", "placeholder": "​", "style": "IPY_MODEL_c8e37848546549358ded639db3054948", "value": "Upload 1 LFS files: 100%"}}, "850453997f7c4a128aff14c5f8a645ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dab7b56c8b074689aefde0e51fe069b3", "placeholder": "​", "style": "IPY_MODEL_cb90c49becac4223ad3ad74c9454a64e", "value": "Creating parquet from Arrow format: 100%"}}, "86f302fd3b764228a054b52cee13b16e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "89647b436701449fa9864fd7cdcc23e9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8cab3007100e4b40bc8909e50dc03a50": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "8e8b2f147c2a460b8ad588a52cbf4b81": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ffe53f0aef64ec188a0a03c2a6f13fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5a64da66b0e840479c9a44c50209c802", "placeholder": "​", "style": "IPY_MODEL_93c05ee6b6424ae780093d54c2d3d239", "value": " 361/0 [00:00&lt;00:00, 1134.22 examples/s]"}}, "93c05ee6b6424ae780093d54c2d3d239": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "954d08a76d324e47a4a130eb1c9d1224": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "958dc7fb60994b04ae13c86a4aacf8f0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "95c7406a3df448efa798c1c4ddc87256": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9e49e8b317894c809a4705aa101e2763": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a36c481f79974d85bab9f49ba1ff14db": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3a230da4ab094ebf932ab09d3f9ef71c", "placeholder": "​", "style": "IPY_MODEL_345941eaecfc4f46a343fd27f2a6c7e8", "value": "Extracting data files: 100%"}}, "a3f734ef7c324152bdac69a41ad4d085": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a990829a3c7b47b69fefb37f616adb9e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b641f00e9b214a6195c1003f0a4c80b0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4eb7b0efbecb4ff79cd3d5e013de693c", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d4eda7196cc74327af2deb7b938fac5c", "value": 1}}, "b8b29b52ade543608d44043b25c49c40": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7fec55575c29462eab062cd1ebe5931c", "placeholder": "​", "style": "IPY_MODEL_fbcaf640a50445ba9e31a6dc88a0e094", "value": "Pushing dataset shards to the dataset hub: 100%"}}, "bb60743a457b400f89830bf19ef09a4a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c1c8469688bc4fccae1ffcf49dbce326": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c5430e5f47f14ec1accc233eb881c2e1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_95c7406a3df448efa798c1c4ddc87256", "placeholder": "​", "style": "IPY_MODEL_e8d1387548344ef5b14d9c8aaaf0affd", "value": " 1/1 [00:00&lt;00:00, 38.00it/s]"}}, "c8e37848546549358ded639db3054948": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cb90c49becac4223ad3ad74c9454a64e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d4a4b1f6a05d4015819976d087eb9754": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bb60743a457b400f89830bf19ef09a4a", "placeholder": "​", "style": "IPY_MODEL_4479c2226d8b4aaaa1ac8958a8c8bb10", "value": " 1/1 [00:00&lt;00:00,  4.75ba/s]"}}, "d4eda7196cc74327af2deb7b938fac5c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d5c6db2f02524f43a66a8e90bf88df5d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0a4a69ef1c7144b382017d8f381b14f3", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f656c459f8eb48c2b731c541f15be8f0", "value": 1}}, "dab7b56c8b074689aefde0e51fe069b3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "db727665edd84862897d16a18b530f76": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "de38c9e4ed3e4b108fd74923ecfee167": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_29f28a9302f54ea392cb890de6f5d11b", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a990829a3c7b47b69fefb37f616adb9e", "value": 1}}, "e6fb4ad108584cb7adf6370689361394": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f1e5977311bb47f7b185aca4b4f26589", "placeholder": "​", "style": "IPY_MODEL_4b3809d024da44c091ce1e4cc35ff8a1", "value": "Generating train split: "}}, "e8d1387548344ef5b14d9c8aaaf0affd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e929ad127eaf4d4f8e24567ca3e14e93": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ec6966d19bbc449298dad8f7324e2e08": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_69d8f1861afb4815a3d43c48f78bf7bf", "IPY_MODEL_59ba1927035f4c929bfc87cc31070be7", "IPY_MODEL_c5430e5f47f14ec1accc233eb881c2e1"], "layout": "IPY_MODEL_1c12912780ca47928c1281476cf21d70"}}, "f1e5977311bb47f7b185aca4b4f26589": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f3babb97d9ad43ce83580614751ad15d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_850453997f7c4a128aff14c5f8a645ad", "IPY_MODEL_b641f00e9b214a6195c1003f0a4c80b0", "IPY_MODEL_d4a4b1f6a05d4015819976d087eb9754"], "layout": "IPY_MODEL_958dc7fb60994b04ae13c86a4aacf8f0"}}, "f656c459f8eb48c2b731c541f15be8f0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fab527e3ad644238aa80e36dcb01725a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "info", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8cab3007100e4b40bc8909e50dc03a50", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_89647b436701449fa9864fd7cdcc23e9", "value": 1}}, "fbcaf640a50445ba9e31a6dc88a0e094": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "nbformat": 4, "nbformat_minor": 0}