events {}
http {
	server {
		listen 443 ssl http2;

		server_name web.prod2.open-assistant.io;

		ssl_certificate /etc/nginx/ssl/live/web.prod2.open-assistant.io/fullchain.pem;
		ssl_certificate_key /etc/nginx/ssl/live/web.prod2.open-assistant.io/privkey.pem;

		location / {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://127.0.0.1:3200;

			proxy_set_header Connection '';
			proxy_http_version 1.1;
			chunked_transfer_encoding off;
			proxy_buffering off;
			proxy_cache off;
		}
	}

	server {
		listen 443 ssl http2;

		server_name backend.prod2.open-assistant.io;

		ssl_certificate /etc/nginx/ssl/live/backend.prod2.open-assistant.io/fullchain.pem;
		ssl_certificate_key /etc/nginx/ssl/live/backend.prod2.open-assistant.io/privkey.pem;

		location / {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_pass http://127.0.0.1:8280;
		}
	}

	map $http_upgrade $connection_upgrade {
		default upgrade;
		''      close;
	}

	server {
		listen 443 ssl http2;

		server_name inference.prod2.open-assistant.io;

		ssl_certificate /etc/nginx/ssl/live/inference.prod2.open-assistant.io/fullchain.pem;
		ssl_certificate_key /etc/nginx/ssl/live/inference.prod2.open-assistant.io/privkey.pem;

		location / {
			proxy_set_header Host $host;
			proxy_set_header X-Real-IP $remote_addr;

			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header X-Forwarded-Proto $scheme;
			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection $connection_upgrade;
			proxy_redirect off;
			proxy_buffering off;

			proxy_pass http://127.0.0.1:8285;
		}
	}

}
