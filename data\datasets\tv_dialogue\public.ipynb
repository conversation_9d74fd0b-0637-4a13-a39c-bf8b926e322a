{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Download and convert publicly available transcripts to OA format\n", "- Friends dialogue (git repository)\n", "- The Office dialogue (Kaggle dataset)\n", "- Marvel Cinematic Universe dialogue (Kaggle dataset)\n", "- Doctor Who dialogue (Kaggle dataset)\n", "- Star Trek dialogue (git repository)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/LAION-AI/Open-Assistant/blob/data/datasets/tv_dialogue/public.ipynb)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# uncomment and run below lines to set up if running in colab\n", "# !git clone https://github.com/LAION-AI/Open-Assistant.git\n", "# %cd Open-Assistant/data/datasets/tv_dialogue\n", "# !pip install -r requirements.txt"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# download data, you can get your kaggle.json file from your account page https://www.kaggle.com/me/account\n", "import kaggle"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# import required packages\n", "import os\n", "import io\n", "import re\n", "import requests\n", "import json\n", "import time\n", "import warnings\n", "\n", "try:\n", "    from BeautifulSoup import BeautifulSoup\n", "except ImportError:\n", "    from bs4 import BeautifulSoup\n", "from tqdm import tqdm\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from typing import Tuple, Optional, Any"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Friends"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>season_id</th>\n", "      <th>episode_id</th>\n", "      <th>scene_id</th>\n", "      <th>utterance_id</th>\n", "      <th>speaker</th>\n", "      <th>tokens</th>\n", "      <th>transcript</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u001</td>\n", "      <td><PERSON></td>\n", "      <td>[['There', \"'s\", 'nothing', 'to', 'tell', '!']...</td>\n", "      <td>There's nothing to tell! He's just some guy I ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u002</td>\n", "      <td><PERSON></td>\n", "      <td>[[\"C'mon\", ',', 'you', \"'re\", 'going', 'out', ...</td>\n", "      <td>C'mon, you're going out with the guy! There's ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u003</td>\n", "      <td><PERSON></td>\n", "      <td>[['All', 'right', 'Joey', ',', 'be', 'nice', '...</td>\n", "      <td>All right <PERSON>, be nice. So does he have a hum...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u004</td>\n", "      <td><PERSON></td>\n", "      <td>[['Wait', ',', 'does', 'he', 'eat', 'chalk', '...</td>\n", "      <td>Wait, does he eat chalk?</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u005</td>\n", "      <td>unknown</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67368</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u017</td>\n", "      <td><PERSON></td>\n", "      <td>[['Oh', ',', 'it', \"'s\", 'gon', 'na', 'be', 'o...</td>\n", "      <td>Oh, it's gonna be okay.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67369</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u018</td>\n", "      <td><PERSON></td>\n", "      <td>[['Do', 'you', 'guys', 'have', 'to', 'go', 'to...</td>\n", "      <td>Do you guys have to go to the new house right ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67370</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u019</td>\n", "      <td><PERSON></td>\n", "      <td>[['We', 'got', 'some', 'time', '.']]</td>\n", "      <td>We got some time.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67371</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u020</td>\n", "      <td><PERSON></td>\n", "      <td>[['Okay', ',', 'should', 'we', 'get', 'some', ...</td>\n", "      <td>Okay, should we get some coffee?</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67372</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u021</td>\n", "      <td><PERSON></td>\n", "      <td>[['Sure', '.'], ['Where', '?']]</td>\n", "      <td>Sure. Where?</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>67373 rows × 7 columns</p>\n", "</div>"], "text/plain": ["      season_id episode_id scene_id utterance_id         speaker  \\\n", "0           s01        e01      c01         u001   Monica Geller   \n", "1           s01        e01      c01         u002  <PERSON>   \n", "2           s01        e01      c01         u003   Chandler Bing   \n", "3           s01        e01      c01         u004   <PERSON> Buffay   \n", "4           s01        e01      c01         u005         unknown   \n", "...         ...        ...      ...          ...             ...   \n", "67368       s10        e18      c11         u017   Chandler <PERSON>   \n", "67369       s10        e18      c11         u018    <PERSON>   \n", "67370       s10        e18      c11         u019   Monica Geller   \n", "67371       s10        e18      c11         u020    <PERSON>   \n", "67372       s10        e18      c11         u021   Chandler Bing   \n", "\n", "                                                  tokens  \\\n", "0      [['There', \"'s\", 'nothing', 'to', 'tell', '!']...   \n", "1      [[\"C'mon\", ',', 'you', \"'re\", 'going', 'out', ...   \n", "2      [['All', 'right', '<PERSON>', ',', 'be', 'nice', '...   \n", "3      [['Wait', ',', 'does', 'he', 'eat', 'chalk', '...   \n", "4                                                     []   \n", "...                                                  ...   \n", "67368  [['Oh', ',', 'it', \"'s\", 'gon', 'na', 'be', 'o...   \n", "67369  [['Do', 'you', 'guys', 'have', 'to', 'go', 'to...   \n", "67370               [['We', 'got', 'some', 'time', '.']]   \n", "67371  [['Okay', ',', 'should', 'we', 'get', 'some', ...   \n", "67372                    [['Sure', '.'], ['Where', '?']]   \n", "\n", "                                              transcript  \n", "0      There's nothing to tell! He's just some guy I ...  \n", "1      C'mon, you're going out with the guy! There's ...  \n", "2      All right <PERSON>, be nice. So does he have a hum...  \n", "3                               Wait, does he eat chalk?  \n", "4                                                    NaN  \n", "...                                                  ...  \n", "67368                            Oh, it's gonna be okay.  \n", "67369  Do you guys have to go to the new house right ...  \n", "67370                                  We got some time.  \n", "67371                   Okay, should we get some coffee?  \n", "67372                                       Sure. Where?  \n", "\n", "[67373 rows x 7 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# friends via https://github.com/emorynlp/character-mining\n", "friends = pd.read_csv(\n", "    \"https://raw.githubusercontent.com/emorynlp/character-mining/master/tsv/friends_transcripts.tsv\", sep=\"\\t\"\n", ")\n", "friends"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>season_id</th>\n", "      <th>episode_id</th>\n", "      <th>scene_id</th>\n", "      <th>utterance_id</th>\n", "      <th>speaker</th>\n", "      <th>tokens</th>\n", "      <th>transcript</th>\n", "      <th>group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u001</td>\n", "      <td><PERSON></td>\n", "      <td>[['There', \"'s\", 'nothing', 'to', 'tell', '!']...</td>\n", "      <td>There's nothing to tell! He's just some guy I ...</td>\n", "      <td>s01_e01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u002</td>\n", "      <td><PERSON></td>\n", "      <td>[[\"C'mon\", ',', 'you', \"'re\", 'going', 'out', ...</td>\n", "      <td>C'mon, you're going out with the guy! There's ...</td>\n", "      <td>s01_e01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u003</td>\n", "      <td><PERSON></td>\n", "      <td>[['All', 'right', 'Joey', ',', 'be', 'nice', '...</td>\n", "      <td>All right <PERSON>, be nice. So does he have a hum...</td>\n", "      <td>s01_e01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u004</td>\n", "      <td><PERSON></td>\n", "      <td>[['Wait', ',', 'does', 'he', 'eat', 'chalk', '...</td>\n", "      <td>Wait, does he eat chalk?</td>\n", "      <td>s01_e01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u005</td>\n", "      <td>unknown</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>s01_e01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67368</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u017</td>\n", "      <td><PERSON></td>\n", "      <td>[['Oh', ',', 'it', \"'s\", 'gon', 'na', 'be', 'o...</td>\n", "      <td>Oh, it's gonna be okay.</td>\n", "      <td>s10_e18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67369</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u018</td>\n", "      <td><PERSON></td>\n", "      <td>[['Do', 'you', 'guys', 'have', 'to', 'go', 'to...</td>\n", "      <td>Do you guys have to go to the new house right ...</td>\n", "      <td>s10_e18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67370</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u019</td>\n", "      <td><PERSON></td>\n", "      <td>[['We', 'got', 'some', 'time', '.']]</td>\n", "      <td>We got some time.</td>\n", "      <td>s10_e18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67371</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u020</td>\n", "      <td><PERSON></td>\n", "      <td>[['Okay', ',', 'should', 'we', 'get', 'some', ...</td>\n", "      <td>Okay, should we get some coffee?</td>\n", "      <td>s10_e18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67372</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u021</td>\n", "      <td><PERSON></td>\n", "      <td>[['Sure', '.'], ['Where', '?']]</td>\n", "      <td>Sure. Where?</td>\n", "      <td>s10_e18</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>67373 rows × 8 columns</p>\n", "</div>"], "text/plain": ["      season_id episode_id scene_id utterance_id         speaker  \\\n", "0           s01        e01      c01         u001   Monica Geller   \n", "1           s01        e01      c01         u002  <PERSON>   \n", "2           s01        e01      c01         u003   Chandler Bing   \n", "3           s01        e01      c01         u004   <PERSON> Buffay   \n", "4           s01        e01      c01         u005         unknown   \n", "...         ...        ...      ...          ...             ...   \n", "67368       s10        e18      c11         u017   Chandler <PERSON>   \n", "67369       s10        e18      c11         u018    <PERSON>   \n", "67370       s10        e18      c11         u019   Monica Geller   \n", "67371       s10        e18      c11         u020    <PERSON>   \n", "67372       s10        e18      c11         u021   Chandler Bing   \n", "\n", "                                                  tokens  \\\n", "0      [['There', \"'s\", 'nothing', 'to', 'tell', '!']...   \n", "1      [[\"C'mon\", ',', 'you', \"'re\", 'going', 'out', ...   \n", "2      [['All', 'right', '<PERSON>', ',', 'be', 'nice', '...   \n", "3      [['Wait', ',', 'does', 'he', 'eat', 'chalk', '...   \n", "4                                                     []   \n", "...                                                  ...   \n", "67368  [['Oh', ',', 'it', \"'s\", 'gon', 'na', 'be', 'o...   \n", "67369  [['Do', 'you', 'guys', 'have', 'to', 'go', 'to...   \n", "67370               [['We', 'got', 'some', 'time', '.']]   \n", "67371  [['Okay', ',', 'should', 'we', 'get', 'some', ...   \n", "67372                    [['Sure', '.'], ['Where', '?']]   \n", "\n", "                                              transcript    group  \n", "0      There's nothing to tell! He's just some guy I ...  s01_e01  \n", "1      C'mon, you're going out with the guy! There's ...  s01_e01  \n", "2      All right <PERSON>, be nice. So does he have a hum...  s01_e01  \n", "3                               Wait, does he eat chalk?  s01_e01  \n", "4                                                    NaN  s01_e01  \n", "...                                                  ...      ...  \n", "67368                            Oh, it's gonna be okay.  s10_e18  \n", "67369  Do you guys have to go to the new house right ...  s10_e18  \n", "67370                                  We got some time.  s10_e18  \n", "67371                   Okay, should we get some coffee?  s10_e18  \n", "67372                                       Sure. Where?  s10_e18  \n", "\n", "[67373 rows x 8 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["friends[\"group\"] = friends[[\"season_id\", \"episode_id\"]].apply(lambda x: f\"{x[0]}_{x[1]}\", axis=1)\n", "friends"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["episode_list = \"\"\"\n", "Season 1\n", "The Pilot\n", "The One With the Sonogram at the End\n", "The One With the Thumb\n", "The One With <PERSON>\n", "The One With the East German Laundry Detergent\n", "The One With the Butt\n", "The One With the Blackout\n", "The One Where Nana Dies Twice\n", "The One Where Underdog Gets Away\n", "The One With the Monkey\n", "The One With Mrs <PERSON>\n", "The One With the Dozen Lasagnas\n", "The One With the Boobies\n", "The One With the Candy Hearts\n", "The One With the Stoned Guy\n", "The One With Two Parts, Part 1\n", "The One With Two Parts, Part 2\n", "The One With All the Poker\n", "The One Where the Monkey Gets Away\n", "The One with the Evil Orthodontist\n", "The One with <PERSON><PERSON>\n", "The One with the Ick Factor\n", "The One with the Birth\n", "The One where <PERSON> Finds Out\n", "\n", "Season 2\n", "The One With <PERSON>' New Girlfriend\n", "The One With the Breast Milk\n", "The One Where Heckles Dies\n", "The One With Phoebe's Husband\n", "The One With Five Steaks and an Eggplant\n", "The One With the Baby on the Bus\n", "The One Where Ross Finds Out\n", "The One With the List\n", "The One With <PERSON>'s Dad\n", "The One With <PERSON>\n", "The One With The Lesbian Wedding\n", "The One After the Superbowl, Part 1\n", "The One After the Superbowl, Part 2\n", "The One With The Prom Video\n", "The One Where <PERSON> and <PERSON><PERSON><PERSON>\n", "The One Where Joey Moves Out\n", "The One Where Eddie Moves In\n", "The One Where Dr <PERSON> Dies\n", "The One Where Eddie Won't Go\n", "The One Where Old Yeller Dies\n", "The One With The Bullies\n", "The One With Two Parties\n", "The One With The Chicken Pox\n", "The One With Barry & Mindy's Wedding\n", "\n", "Season 3\n", "The One With The Princess <PERSON>\n", "The One Where No One's Ready\n", "The One With The Jam\n", "The One With The Metaphorical Tunnel\n", "The One With <PERSON>, Jr\n", "The One With The Flashback\n", "The One With The Racecar Bed\n", "The One With The Giant Poking Device\n", "The One With The Football\n", "The One Where <PERSON>\n", "The One Where Chandler Can't Remember Which Sister\n", "The One With All The Jealousy\n", "The One Where <PERSON> and <PERSON> Are Just Friends\n", "The One With <PERSON>'s Ex-Partner\n", "The One Where Ross And Rachel Take A Break\n", "The One The Morning After\n", "The One Without The Ski Trip\n", "The One With The Hypnosis Tape\n", "The One With The Tiny T-Shirt\n", "The One With The Dollhouse\n", "The One With a Chick And a Duck\n", "The One With The Screamer\n", "The One With <PERSON>'s Thing\n", "The One With The Ultimate Fighting Champion\n", "The One At The Beach\n", "\n", "Season 4\n", "The One With The Jellyfish\n", "The One With The Cat\n", "The One With The 'Cuffs\n", "The One With The Ballroom Dancing\n", "The One With <PERSON>'s New Girlfriend\n", "The One With The Dirty Girl\n", "The One Where Chandler Crosses The Line\n", "The One With Chandler In A Box\n", "The One Where They're Going To PARTY!\n", "The One With The Girl From Poughkeepsie\n", "The One With Phoebe's Uterus\n", "The One With The Embryos\n", "The One With <PERSON>'s Crush\n", "The One With Joey's Dirty Day\n", "The One With All The Rugby\n", "The One With The Fake Party\n", "The One With The Free Porn\n", "The One With Rachel's <PERSON> Dress\n", "The One With All The Haste\n", "The One With All The Wedding Dresses\n", "The One With The Invitation\n", "The One With The Worst Best Man Ever\n", "The One With <PERSON>'s Wedding, Part 1\n", "The One With <PERSON>'s Wedding, Part 2\n", "\n", "Season 5\n", "The One After Ross Says Rachel\n", "The One With All The Kissing\n", "The One With The Triplets\n", "The One Where Phoebe Hates PBS\n", "The One With The Kips\n", "The One With The Yeti\n", "The One Where Ross Moves In\n", "The One With All The Thanksgivings\n", "The One With Ross's Sandwich\n", "The One With The Inappropriate Sister\n", "The One With All The Resolutions\n", "The One With Chandler's Work Laugh\n", "The One With Joey's Bag\n", "The One Where Everybody Finds Out\n", "The One With The Girl Who Hits Joey\n", "The One With The Cop\n", "The One With <PERSON>'s Inadvertent Kiss\n", "The One Where Rachel Smokes\n", "The One Where Ross Can't Flirt\n", "The One With The Ride-Along\n", "The One With The Ball\n", "The One With Joey's Big Break\n", "The One In Vegas, Part 1\n", "The One In Vegas, Part 2\n", "\n", "Season 6\n", "The One After Vegas\n", "The One Where Ross Hugs Rachel\n", "The One With <PERSON>'s Denial\n", "The One Where <PERSON> Loses His Insurance\n", "The One With <PERSON>'s Porsche\n", "The One On The Last Night\n", "The One Where Phoebe Runs\n", "The One With Ross's <PERSON><PERSON>\n", "The One Where Ross Got High\n", "The One With The Routine\n", "The One With The Apothecary Table\n", "The One With The Joke\n", "The One With <PERSON>'s Sister\n", "The One Where Chandler Can't Cry\n", "The One That Could Have Been, Part 1\n", "The One That Could Have Been, Part 2\n", "The One With Unagi\n", "The One Where Ross Dates a Student\n", "The One With Joey's <PERSON><PERSON>\n", "The One With Mac & C.H.E.E.S.E.\n", "The One Where Ross Meets <PERSON>'s Dad\n", "The One Where Paul's The Man\n", "The One With The Ring\n", "The One With The Proposal, Part 1\n", "The One With The Proposal, Part 2\n", "\n", "Season 7\n", "The One With Monica's Thunder\n", "The One With <PERSON>'s Book\n", "The One With Phoebe's Cookies\n", "The One With <PERSON>'s Assistant\n", "The One With The Engagement Picture\n", "The One With The Nap Partners\n", "The One With Ross's Library Book\n", "The One Where Chandler Doesn't Like Dogs\n", "The One With All The Candy\n", "The One With the Holiday Armadillo\n", "The One With All The Cheesecakes\n", "The One Where They're Up All Night\n", "The One Where <PERSON><PERSON><PERSON>\n", "The One Where They All Turn Thirty\n", "The One With Joey's New Brain\n", "The One With The Truth About London\n", "The One With The Cheap Wedding Dress\n", "The One With Joey's Award\n", "The One With <PERSON> and <PERSON>'s Cousin\n", "The One With Rachel's Big Kiss\n", "The One With The Vows\n", "The One With <PERSON>'s Dad\n", "The One With <PERSON> and <PERSON>'s Wedding, Part 1\n", "The One With <PERSON> and <PERSON>'s Wedding, Part 2\n", "\n", "Season 8\n", "The One After \"I Do\"\n", "The One With The Red Sweater\n", "The One Where <PERSON> Tells\n", "The One With The Video Tape\n", "The One With <PERSON>'s Date\n", "The One With The Halloween Party\n", "The One With The Stain\n", "The One With The Stripper\n", "The One With The Rumor\n", "The One With Monica's Boots\n", "The One With Ross' Step Forward\n", "The One Where Joey Dates Rachel\n", "The One Where Chandler Takes a Bath\n", "The One With The Secret Closet\n", "The One With The Birthing Video\n", "The One Where Joey Tells Rachel\n", "The One With The Tea Leaves\n", "The One In Massapequa\n", "The One With <PERSON>'s Interview\n", "The One With The Baby Shower\n", "The One With The Cooking Class\n", "The One Where Rachel is Late\n", "The One Where Rachel Has a Baby, Part 1\n", "The One Where Rachel Has a Baby, Part 2\n", "\n", "Season 9\n", "The One Where No One Proposes\n", "The One Where <PERSON>\n", "The One With the Pediatrician\n", "The One With the Sharks\n", "The One With <PERSON>'s Birthday Dinner\n", "The One With the Male Nanny\n", "The One With <PERSON>'s <PERSON>ap<PERSON><PERSON>riate Song\n", "The One With <PERSON>'s Other Sister\n", "The One With <PERSON>'s Phone Number\n", "The One With Christmas in Tulsa\n", "The One Where <PERSON> Goes Back To Work\n", "The One With Phoebe's Rats\n", "The One Where <PERSON> Sings\n", "The One With The Blind Dates\n", "The One With The Mugging\n", "The One With The Boob Job\n", "The One With The Memorial Service\n", "The One With The Lottery\n", "The One With <PERSON>'s Dream\n", "The One With The Soap Opera Party\n", "The One With The Fertility Test\n", "The One With The Donor\n", "The One In Barbados, Part 1\n", "The One In Barbados, Part 2\n", "\n", "Season 10\n", "The One After <PERSON> and <PERSON>\n", "The One Where Ross is Fine\n", "The One With <PERSON>'s Tan\n", "The One With the Cake\n", "The One Where <PERSON>'s Sister Baby-sits\n", "The One With <PERSON>'s <PERSON>\n", "The One With the Home Study\n", "The One With the Late Thanksgiving\n", "The One With The Birth Mother\n", "The One Where Chandler Gets Caught\n", "The One Where The Stripper Cries\n", "The One With Phoebe's Wedding\n", "The One Where Joey Speaks French\n", "The One With Princess <PERSON><PERSON><PERSON>\n", "The One Where Estelle Dies\n", "The One With Rachel's Going Away Party\n", "The Last One, Part 1\n", "The Last One, Part 2\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'s01': {'e01': 'The Pilot',\n", "  'e02': 'The One With the Sonogram at the End',\n", "  'e03': 'The One With the Thumb',\n", "  'e04': 'The One With <PERSON>',\n", "  'e05': 'The One With the East German Laundry Detergent',\n", "  'e06': 'The One With the Butt',\n", "  'e07': 'The One With the Blackout',\n", "  'e08': 'The One Where Nana Dies Twice',\n", "  'e09': 'The One Where Underdog Gets Away',\n", "  'e10': 'The One With the Monkey',\n", "  'e11': 'The One With Mrs Bing',\n", "  'e12': 'The One With the Dozen Lasagnas',\n", "  'e13': 'The One With the Boobies',\n", "  'e14': 'The One With the Candy Hearts',\n", "  'e15': 'The One With the Stoned Guy',\n", "  'e16': 'The One With Two Parts, Part 1',\n", "  'e17': 'The One With Two Parts, Part 2',\n", "  'e18': 'The One With All the Poker',\n", "  'e19': 'The One Where the Monkey Gets Away',\n", "  'e20': 'The One with the Evil Orthodontist',\n", "  'e21': 'The One with <PERSON><PERSON>',\n", "  'e22': 'The One with the Ick Factor',\n", "  'e23': 'The One with the Birth',\n", "  'e24': 'The One where <PERSON> Finds Out'},\n", " 's02': {'e01': \"The One With Ross' New Girlfriend\",\n", "  'e02': 'The One With the Breast Milk',\n", "  'e03': 'The One Where Heckles Dies',\n", "  'e04': \"The One With Phoebe's Husband\",\n", "  'e05': 'The One With Five Steaks and an Eggplant',\n", "  'e06': 'The One With the Baby on the Bus',\n", "  'e07': 'The One Where Ross Finds Out',\n", "  'e08': 'The One With the List',\n", "  'e09': \"The One With Phoebe's Dad\",\n", "  'e10': 'The One With Russ',\n", "  'e11': 'The One With The Lesbian Wedding',\n", "  'e12': 'The One After the Superbowl, Part 1',\n", "  'e13': 'The One After the Superbowl, Part 2',\n", "  'e14': 'The One With The Prom Video',\n", "  'e15': 'The One Where <PERSON> and <PERSON><PERSON>',\n", "  'e16': 'The One Where Joey Moves Out',\n", "  'e17': 'The One Where Eddie Moves In',\n", "  'e18': 'The One Where Dr <PERSON> Dies',\n", "  'e19': \"The One Where Eddie Won't Go\",\n", "  'e20': 'The One Where Old Yeller Dies',\n", "  'e21': 'The One With The Bullies',\n", "  'e22': 'The One With Two Parties',\n", "  'e23': 'The One With The Chicken Pox',\n", "  'e24': \"The One With Barry & Mindy's Wedding\"},\n", " 's03': {'e01': 'The One With The Princess Le<PERSON>',\n", "  'e02': \"The One Where No One's Ready\",\n", "  'e03': 'The One With The Jam',\n", "  'e04': 'The One With The Metaphorical Tunnel',\n", "  'e05': 'The One With <PERSON>, Jr',\n", "  'e06': 'The One With The Flashback',\n", "  'e07': 'The One With The Racecar Bed',\n", "  'e08': 'The One With The Giant Poking Device',\n", "  'e09': 'The One With The Football',\n", "  'e10': 'The One Where <PERSON>',\n", "  'e11': \"The One Where Chandler Can't Remember Which Sister\",\n", "  'e12': 'The One With All The Jealousy',\n", "  'e13': 'The One Where <PERSON> and <PERSON> Are Just Friends',\n", "  'e14': \"The One With Phoebe's Ex-Partner\",\n", "  'e15': 'The One Where Ross And Rachel Take A Break',\n", "  'e16': 'The One The Morning After',\n", "  'e17': 'The One Without The Ski Trip',\n", "  'e18': 'The One With The Hypnosis Tape',\n", "  'e19': 'The One With The Tiny T-Shirt',\n", "  'e20': 'The One With The Dollhouse',\n", "  'e21': 'The One With a Chick And a Duck',\n", "  'e22': 'The One With The Screamer',\n", "  'e23': \"The One With Ross's Thing\",\n", "  'e24': 'The One With The Ultimate Fighting Champion',\n", "  'e25': 'The One At The Beach'},\n", " 's04': {'e01': 'The One With The Jellyfish',\n", "  'e02': 'The One With The Cat',\n", "  'e03': \"The One With The 'Cuffs\",\n", "  'e04': 'The One With The Ballroom Dancing',\n", "  'e05': \"The One With Joey's New Girlfriend\",\n", "  'e06': 'The One With The Dirty Girl',\n", "  'e07': 'The One Where Chandler Crosses The Line',\n", "  'e08': 'The One With Chandler In A Box',\n", "  'e09': \"The One Where They're Going To PARTY!\",\n", "  'e10': 'The One With The Girl From Poughkeepsie',\n", "  'e11': \"The One With Phoebe's Uterus\",\n", "  'e12': 'The One With The Embryos',\n", "  'e13': \"The One With Rachel's Crush\",\n", "  'e14': \"The One With Joey's Dirty Day\",\n", "  'e15': 'The One With All The Rugby',\n", "  'e16': 'The One With The Fake Party',\n", "  'e17': 'The One With The Free Porn',\n", "  'e18': \"The One With Rachel's New Dress\",\n", "  'e19': 'The One With All The Haste',\n", "  'e20': 'The One With All The Wedding Dresses',\n", "  'e21': 'The One With The Invitation',\n", "  'e22': 'The One With The Worst Best Man Ever',\n", "  'e23': \"The One With Ross's Wedding, Part 1\",\n", "  'e24': \"The One With Ross's Wedding, Part 2\"},\n", " 's05': {'e01': 'The One After Ross Says Rachel',\n", "  'e02': 'The One With All The Kissing',\n", "  'e03': 'The One With The Triplets',\n", "  'e04': 'The One Where Phoebe Hates PBS',\n", "  'e05': 'The One With The Kips',\n", "  'e06': 'The One With The Yeti',\n", "  'e07': 'The One Where Ross Moves In',\n", "  'e08': 'The One With All The Thanksgivings',\n", "  'e09': \"The One With Ross's Sandwich\",\n", "  'e10': 'The One With The Inappropriate Sister',\n", "  'e11': 'The One With All The Resolutions',\n", "  'e12': \"The One With Chandler's Work Laugh\",\n", "  'e13': \"The One With Joey's Bag\",\n", "  'e14': 'The One Where Everybody Finds Out',\n", "  'e15': 'The One With The Girl Who Hits Joey',\n", "  'e16': 'The One With The Cop',\n", "  'e17': \"The One With <PERSON>'s Inadvertent Kiss\",\n", "  'e18': 'The One Where Rachel Smokes',\n", "  'e19': \"The One Where Ross Can't Flirt\",\n", "  'e20': 'The One With The Ride-Along',\n", "  'e21': 'The One With The Ball',\n", "  'e22': \"The One With Joey's Big Break\",\n", "  'e23': 'The One In Vegas, Part 1',\n", "  'e24': 'The One In Vegas, Part 2'},\n", " 's06': {'e01': 'The One After Vegas',\n", "  'e02': 'The One Where Ross Hugs Rachel',\n", "  'e03': \"The One With Ross's Denial\",\n", "  'e04': 'The One Where Joey Loses His Insurance',\n", "  'e05': \"The One With Joey's Porsche\",\n", "  'e06': 'The One On The Last Night',\n", "  'e07': 'The One Where Phoebe Runs',\n", "  'e08': \"The One With Ross's Teeth\",\n", "  'e09': 'The One Where Ross Got High',\n", "  'e10': 'The One With The Routine',\n", "  'e11': 'The One With The Apothecary Table',\n", "  'e12': 'The One With The Joke',\n", "  'e13': \"The One With <PERSON>'s Sister\",\n", "  'e14': \"The One Where Chandler Can't Cry\",\n", "  'e15': 'The One That Could Have Been, Part 1',\n", "  'e16': 'The One That Could Have Been, Part 2',\n", "  'e17': 'The One With Unagi',\n", "  'e18': 'The One Where Ross Dates a Student',\n", "  'e19': \"The One With Joey's <PERSON><PERSON>\",\n", "  'e20': 'The One With Mac & C.H.E.E.S.E.',\n", "  'e21': \"The One Where Ross Meets <PERSON>'s Dad\",\n", "  'e22': \"The One Where Paul's The Man\",\n", "  'e23': 'The One With The Ring',\n", "  'e24': 'The One With The Proposal, Part 1',\n", "  'e25': 'The One With The Proposal, Part 2'},\n", " 's07': {'e01': \"The One With Monica's Thunder\",\n", "  'e02': \"The One With Rachel's Book\",\n", "  'e03': \"The One With Phoebe's Cookies\",\n", "  'e04': \"The One With <PERSON>'s Assistant\",\n", "  'e05': 'The One With The Engagement Picture',\n", "  'e06': 'The One With The Nap Partners',\n", "  'e07': \"The One With Ross's Library Book\",\n", "  'e08': \"The One Where Chandler Doesn't Like Dogs\",\n", "  'e09': 'The One With All The Candy',\n", "  'e10': 'The One With the Holiday Armadillo',\n", "  'e11': 'The One With All The Cheesecakes',\n", "  'e12': \"The One Where They're Up All Night\",\n", "  'e13': 'The One Where <PERSON><PERSON><PERSON>',\n", "  'e14': 'The One Where They All Turn Thirty',\n", "  'e15': \"The One With Joey's New Brain\",\n", "  'e16': 'The One With The Truth About London',\n", "  'e17': 'The One With The Cheap Wedding Dress',\n", "  'e18': \"The One With Joey's Award\",\n", "  'e19': \"The One With <PERSON> and <PERSON>'s Cousin\",\n", "  'e20': \"The One With <PERSON>'s Big Kiss\",\n", "  'e21': 'The One With The Vows',\n", "  'e22': \"The One With <PERSON>'s Dad\",\n", "  'e23': \"The One With <PERSON> and <PERSON>'s Wedding, Part 1\",\n", "  'e24': \"The One With <PERSON> and <PERSON>'s Wedding, Part 2\"},\n", " 's08': {'e01': 'The One After \"I Do\"',\n", "  'e02': 'The One With The Red Sweater',\n", "  'e03': 'The One Where Rachel Tells',\n", "  'e04': 'The One With The Video Tape',\n", "  'e05': \"The One With Rachel's Date\",\n", "  'e06': 'The One With The Halloween Party',\n", "  'e07': 'The One With The Stain',\n", "  'e08': 'The One With The Stripper',\n", "  'e09': 'The One With The Rumor',\n", "  'e10': \"The One With Monica's Boots\",\n", "  'e11': \"The One With Ross' Step Forward\",\n", "  'e12': 'The One Where Joey Dates Rachel',\n", "  'e13': 'The One Where Chandler Takes a Bath',\n", "  'e14': 'The One With The Secret Closet',\n", "  'e15': 'The One With The Birthing Video',\n", "  'e16': 'The One Where Joey Tells Rachel',\n", "  'e17': 'The One With The Tea Leaves',\n", "  'e18': 'The One In Massapequa',\n", "  'e19': \"The One With Joey's Interview\",\n", "  'e20': 'The One With The Baby Shower',\n", "  'e21': 'The One With The Cooking Class',\n", "  'e22': 'The One Where Rachel is Late',\n", "  'e23': 'The One Where Rachel Has a Baby, Part 1',\n", "  'e24': 'The One Where Rachel Has a Baby, Part 2'},\n", " 's09': {'e01': 'The One Where No One Proposes',\n", "  'e02': 'The One Where <PERSON>',\n", "  'e03': 'The One With the Pediatrician',\n", "  'e04': 'The One With the Sharks',\n", "  'e05': \"The One With Phoebe's Birthday Dinner\",\n", "  'e06': 'The One With the Male Nanny',\n", "  'e07': \"The One With <PERSON>'s Inappropriate Song\",\n", "  'e08': \"The One With <PERSON>'s Other Sister\",\n", "  'e09': \"The One With Rachel's Phone Number\",\n", "  'e10': 'The One With Christmas in Tulsa',\n", "  'e11': 'The One Where <PERSON> Goes Back To Work',\n", "  'e12': \"The One With Phoebe's Rats\",\n", "  'e13': 'The One Where Monica Sings',\n", "  'e14': 'The One With The Blind Dates',\n", "  'e15': 'The One With The Mugging',\n", "  'e16': 'The One With The Boob Job',\n", "  'e17': 'The One With The Memorial Service',\n", "  'e18': 'The One With The Lottery',\n", "  'e19': \"The One With Rachel's Dream\",\n", "  'e20': 'The One With The Soap Opera Party',\n", "  'e21': 'The One With The Fertility Test',\n", "  'e22': 'The One With The Donor',\n", "  'e23': 'The One In Barbados, Part 1',\n", "  'e24': 'The One In Barbados, Part 2'},\n", " 's10': {'e01': 'The One After <PERSON> and <PERSON>',\n", "  'e02': 'The One Where Ross is Fine',\n", "  'e03': \"The One With <PERSON>'s Tan\",\n", "  'e04': 'The One With the Cake',\n", "  'e05': \"The One Where <PERSON>'s Sister Baby-sits\",\n", "  'e06': \"The One With <PERSON>'s Grant\",\n", "  'e07': 'The One With the Home Study',\n", "  'e08': 'The One With the Late Thanksgiving',\n", "  'e09': 'The One With The Birth Mother',\n", "  'e10': 'The One Where Chandler Gets Caught',\n", "  'e11': 'The One Where The Stripper Cries',\n", "  'e12': \"The One With Phoebe's Wedding\",\n", "  'e13': 'The One Where Joey Speaks French',\n", "  'e14': 'The One With Princess <PERSON><PERSON>',\n", "  'e15': 'The One Where Estelle Dies',\n", "  'e16': \"The One With Rachel's Going Away Party\",\n", "  'e17': 'The Last One, Part 1',\n", "  'e18': 'The Last One, Part 2'}}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["episodes, season, cnt = {}, \"\", 0\n", "for line in episode_list.split(\"\\n\"):\n", "    if not line:\n", "        continue\n", "    if line.startswith(\"Season \"):\n", "        season = f\"s{line.split('Season ', 1)[1].z<PERSON>(2)}\"\n", "        cnt = 1\n", "        if season not in episodes:\n", "            episodes[season] = {}\n", "    else:\n", "        episodes[season][f\"e{str(cnt).zfill(2)}\"] = line.strip()\n", "        cnt += 1\n", "episodes"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>season_id</th>\n", "      <th>episode_id</th>\n", "      <th>scene_id</th>\n", "      <th>utterance_id</th>\n", "      <th>speaker</th>\n", "      <th>tokens</th>\n", "      <th>transcript</th>\n", "      <th>group</th>\n", "      <th>utterance</th>\n", "      <th>scene</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u001</td>\n", "      <td><PERSON></td>\n", "      <td>[['There', \"'s\", 'nothing', 'to', 'tell', '!']...</td>\n", "      <td>There's nothing to tell! He's just some guy I ...</td>\n", "      <td>s01_e01</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u002</td>\n", "      <td><PERSON></td>\n", "      <td>[[\"C'mon\", ',', 'you', \"'re\", 'going', 'out', ...</td>\n", "      <td>C'mon, you're going out with the guy! There's ...</td>\n", "      <td>s01_e01</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u003</td>\n", "      <td><PERSON></td>\n", "      <td>[['All', 'right', 'Joey', ',', 'be', 'nice', '...</td>\n", "      <td>All right <PERSON>, be nice. So does he have a hum...</td>\n", "      <td>s01_e01</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u004</td>\n", "      <td><PERSON></td>\n", "      <td>[['Wait', ',', 'does', 'he', 'eat', 'chalk', '...</td>\n", "      <td>Wait, does he eat chalk?</td>\n", "      <td>s01_e01</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>s01</td>\n", "      <td>e01</td>\n", "      <td>c01</td>\n", "      <td>u005</td>\n", "      <td>unknown</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>s01_e01</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67368</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u017</td>\n", "      <td><PERSON></td>\n", "      <td>[['Oh', ',', 'it', \"'s\", 'gon', 'na', 'be', 'o...</td>\n", "      <td>Oh, it's gonna be okay.</td>\n", "      <td>s10_e18</td>\n", "      <td>17</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67369</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u018</td>\n", "      <td><PERSON></td>\n", "      <td>[['Do', 'you', 'guys', 'have', 'to', 'go', 'to...</td>\n", "      <td>Do you guys have to go to the new house right ...</td>\n", "      <td>s10_e18</td>\n", "      <td>18</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67370</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u019</td>\n", "      <td><PERSON></td>\n", "      <td>[['We', 'got', 'some', 'time', '.']]</td>\n", "      <td>We got some time.</td>\n", "      <td>s10_e18</td>\n", "      <td>19</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67371</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u020</td>\n", "      <td><PERSON></td>\n", "      <td>[['Okay', ',', 'should', 'we', 'get', 'some', ...</td>\n", "      <td>Okay, should we get some coffee?</td>\n", "      <td>s10_e18</td>\n", "      <td>20</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67372</th>\n", "      <td>s10</td>\n", "      <td>e18</td>\n", "      <td>c11</td>\n", "      <td>u021</td>\n", "      <td><PERSON></td>\n", "      <td>[['Sure', '.'], ['Where', '?']]</td>\n", "      <td>Sure. Where?</td>\n", "      <td>s10_e18</td>\n", "      <td>21</td>\n", "      <td>11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>67373 rows × 10 columns</p>\n", "</div>"], "text/plain": ["      season_id episode_id scene_id utterance_id         speaker  \\\n", "0           s01        e01      c01         u001   Monica Geller   \n", "1           s01        e01      c01         u002  <PERSON>   \n", "2           s01        e01      c01         u003   Chandler Bing   \n", "3           s01        e01      c01         u004   <PERSON> Buffay   \n", "4           s01        e01      c01         u005         unknown   \n", "...         ...        ...      ...          ...             ...   \n", "67368       s10        e18      c11         u017   Chandler <PERSON>   \n", "67369       s10        e18      c11         u018    <PERSON>   \n", "67370       s10        e18      c11         u019   Monica Geller   \n", "67371       s10        e18      c11         u020    <PERSON>   \n", "67372       s10        e18      c11         u021   Chandler Bing   \n", "\n", "                                                  tokens  \\\n", "0      [['There', \"'s\", 'nothing', 'to', 'tell', '!']...   \n", "1      [[\"C'mon\", ',', 'you', \"'re\", 'going', 'out', ...   \n", "2      [['All', 'right', '<PERSON>', ',', 'be', 'nice', '...   \n", "3      [['Wait', ',', 'does', 'he', 'eat', 'chalk', '...   \n", "4                                                     []   \n", "...                                                  ...   \n", "67368  [['Oh', ',', 'it', \"'s\", 'gon', 'na', 'be', 'o...   \n", "67369  [['Do', 'you', 'guys', 'have', 'to', 'go', 'to...   \n", "67370               [['We', 'got', 'some', 'time', '.']]   \n", "67371  [['Okay', ',', 'should', 'we', 'get', 'some', ...   \n", "67372                    [['Sure', '.'], ['Where', '?']]   \n", "\n", "                                              transcript    group  utterance  \\\n", "0      There's nothing to tell! He's just some guy I ...  s01_e01          1   \n", "1      C'mon, you're going out with the guy! There's ...  s01_e01          2   \n", "2      All right <PERSON>, be nice. So does he have a hum...  s01_e01          3   \n", "3                               Wait, does he eat chalk?  s01_e01          4   \n", "4                                                    NaN  s01_e01          5   \n", "...                                                  ...      ...        ...   \n", "67368                            Oh, it's gonna be okay.  s10_e18         17   \n", "67369  Do you guys have to go to the new house right ...  s10_e18         18   \n", "67370                                  We got some time.  s10_e18         19   \n", "67371                   Okay, should we get some coffee?  s10_e18         20   \n", "67372                                       Sure. Where?  s10_e18         21   \n", "\n", "       scene  \n", "0          1  \n", "1          1  \n", "2          1  \n", "3          1  \n", "4          1  \n", "...      ...  \n", "67368     11  \n", "67369     11  \n", "67370     11  \n", "67371     11  \n", "67372     11  \n", "\n", "[67373 rows x 10 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["friends[\"utterance\"] = friends[\"utterance_id\"].apply(lambda x: int(x[1:]))\n", "friends[\"scene\"] = friends[\"scene_id\"].apply(lambda x: int(x[1:]))\n", "friends"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████| 236/236 [00:07<00:00, 32.37it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>METADATA</th>\n", "      <th>SOURCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Friends - The Pilot\\r\\n\\r\\n[<PERSON>] The...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Friends - The One With the Sonogram at the End...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Friends - The One With the Thumb\\r\\n\\r\\n[Phoeb...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Friends - The One With <PERSON>\\r...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Friends - The One With the East German Laundry...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>231</th>\n", "      <td>Friends - The One With Princess <PERSON><PERSON>\\r\\n\\r...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s10\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>232</th>\n", "      <td>Friends - The One Where <PERSON><PERSON><PERSON>\\r\\n\\r\\n[C...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s10\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>233</th>\n", "      <td>Friends - The One With Rachel's Going Away Par...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s10\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>234</th>\n", "      <td>Friends - The Last One, Part 1\\r\\n\\r\\n[Jennife...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s10\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>235</th>\n", "      <td>Friends - The Last One, Part 2\\r\\n\\r\\n[Gate At...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s10\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>236 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                                                  TEXT  \\\n", "0    Friends - The Pilot\\r\\n\\r\\n[<PERSON>] The...   \n", "1    Friends - The One With the Sonogram at the End...   \n", "2    Friends - The One With the Thumb\\r\\n\\r\\n[Phoeb...   \n", "3    Friends - The One With <PERSON>\\r...   \n", "4    Friends - The One With the East German Laundry...   \n", "..                                                 ...   \n", "231  Friends - The One With Princess <PERSON>\\r\\n\\r...   \n", "232  Friends - The One Where <PERSON><PERSON><PERSON>\\r\\n\\r\\n[C...   \n", "233  Friends - The One With Rachel's Going Away Par...   \n", "234  Friends - The Last One, Part 1\\r\\n\\r\\n[Jennife...   \n", "235  Friends - The Last One, Part 2\\r\\n\\r\\n[Gate At...   \n", "\n", "                                              METADATA            SOURCE  \n", "0    {\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...  friends/emorynlp  \n", "1    {\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...  friends/emorynlp  \n", "2    {\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...  friends/emorynlp  \n", "3    {\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...  friends/emorynlp  \n", "4    {\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...  friends/emorynlp  \n", "..                                                 ...               ...  \n", "231  {\"show\": \"Friends\", \"season\": \"s10\", \"episode\"...  friends/emorynlp  \n", "232  {\"show\": \"Friends\", \"season\": \"s10\", \"episode\"...  friends/emorynlp  \n", "233  {\"show\": \"Friends\", \"season\": \"s10\", \"episode\"...  friends/emorynlp  \n", "234  {\"show\": \"Friends\", \"season\": \"s10\", \"episode\"...  friends/emorynlp  \n", "235  {\"show\": \"Friends\", \"season\": \"s10\", \"episode\"...  friends/emorynlp  \n", "\n", "[236 rows x 3 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["data = {\"TEXT\": [], \"METADATA\": [], \"SOURCE\": []}\n", "for name, group in tqdm(friends.groupby(\"group\")):\n", "    metadata = {\n", "        \"show\": \"Friends\",\n", "        \"season\": group[\"season_id\"].values[0],\n", "        \"episode\": group[\"episode_id\"].values[0],\n", "        \"title\": episodes[group[\"season_id\"].values[0]][group[\"episode_id\"].values[0]],\n", "    }\n", "    text, last_scene = f\"Friends - {metadata['title']}\\r\\n\\r\\n\", None\n", "    group.sort_values(by=[\"scene_id\", \"utterance\"], ascending=True, inplace=True)\n", "    for index, row in group.iterrows():\n", "        if last_scene is None:\n", "            last_scene = row[\"scene_id\"]\n", "        elif last_scene != row[\"scene_id\"]:\n", "            last_scene = row[\"scene_id\"]\n", "            text += \"\\r\\n---------------------------------------\\r\\n\\r\\n\"\n", "        if row[\"speaker\"] == \"unknown\" or row[\"tokens\"] == \"[]\" or pd.isna(row[\"transcript\"]):\n", "            continue\n", "        text += f\"[{row['speaker'].strip()}] {row['transcript'].strip()}\\r\\n\"\n", "    data[\"TEXT\"].append(text)\n", "    data[\"METADATA\"].append(json.dumps(metadata))\n", "    data[\"SOURCE\"].append(\"friends/emorynlp\")\n", "data = pd.DataFrame(data)\n", "data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Friends - The Pilot\r\n", "\r\n", "[<PERSON>] There's nothing to tell! He's just some guy I work with!\r\n", "[<PERSON>] C'mon, you're going out with the guy! There's gotta be something wrong with him!\r\n", "[<PERSON>] All right <PERSON>, be nice. So does he have a hump? A hump and a hairpiece?\r\n", "[<PERSON>] Wait, does he eat chalk?\r\n", "[<PERSON>] Just, 'cause, I don't want her to go through what I went through with <PERSON>- oh!\r\n", "[<PERSON>] Okay, everybody relax. This is not even a date. It's just two people going out to dinner and- not having sex.\r\n", "[<PERSON>] Sounds like a date to me.\r\n", "[<PERSON>] Alright, so I'm back in high school, I'm standing in the middle of the cafeteria, and I realize I am totally naked.\r\n", "[#ALL#] Oh, yeah. Had that dream.\r\n", "[<PERSON>] Then I look down, and I realize there's a phone... there.\r\n", "[<PERSON>] Instead of...?\r\n", "[<PERSON>] That's right.\r\n", "[<PERSON>] Never had that dream.\r\n", "[<PERSON>] No.\r\n", "[<PERSON>] All of a sudden, the phone starts to ring. Now I don't know what to do, everybody starts looking at me.\r\n", "[<PERSON>] And they weren't looking at you before?!\r\n", "[<PERSON>] Finally, I figure I'd better answer it, and it turns out it's my mother, which is very-very weird, because- she never calls me!\r\n", "[<PERSON>] Hi.\r\n", "[<PERSON>] This guy says hello, I wanna kill myself.\r\n", "[<PERSON>] Are you okay, sweetie?\r\n", "[<PERSON>] I just feel like someone reached down my throat, grabbed my small intestine, pulled it out of my mouth and tied it around my neck...\r\n", "[<PERSON>] <PERSON>ie?\r\n", "[<PERSON>] <PERSON> moved her stuff out today.\r\n", "[<PERSON>] Ohh.\r\n", "[<PERSON>] Let me get you some coffee.\r\n", "[<PERSON>] Thanks.\r\n", "[<PERSON>] Ooh! Oh!\r\n", "[<PERSON>] No, no don't! Stop cleansing my aura! No, just leave my aura alone, okay?\r\n", "[<PERSON>] Fine! Be murky!\r\n", "[<PERSON>] I'll be fine, alright? Really, everyone. I hope she'll be very happy.\r\n", "[<PERSON>] No you don't.\r\n", "[<PERSON>] No I don't, to hell with her, she left me!\r\n", "[<PERSON>] And you never knew she was a lesbian...\r\n", "[<PERSON>] No!! Okay?! Why does everyone keep fixating on that? She didn't know, how should I know?\r\n", "[<PERSON>] Sometimes I wish I was a lesbian... Did I say that out loud?\r\n", "[<PERSON>] I told mom and dad last night, they seemed to take it pretty well.\r\n", "[<PERSON>] Oh really, so that hysterical phone call I got from a woman at sobbing 3:00 A.M., \"I'll never have grandchildren, I'll never have grandchildren.\" was what? A wrong number?\r\n", "[<PERSON>] Sorry.\r\n", "[<PERSON>] Alright <PERSON>, look. You're feeling a lot of pain right now. You're angry. You're hurting. Can I tell you what the answer is?\r\n", "[<PERSON>] Strip joint! C'mon, you're single! Have some hormones!\r\n", "[<PERSON>] I don't want to be single, okay? I just... I just- I just wanna be married again!\r\n", "[<PERSON>] And I just want a million dollars!\r\n", "[<PERSON>] Rachel?!\r\n", "[<PERSON>] Oh <PERSON> Monica hi! Thank God! I just went to your building and you weren't there and then this guy with a big hammer said you might be here and you are, you are!\r\n", "[Waitress] Can I get you some coffee?\r\n", "[<PERSON>] De-caff. Okay, everybody, this is <PERSON>, another Lincoln High survivor. This is everybody, this is <PERSON>, and <PERSON>, and <PERSON>, and- you remember my brother <PERSON>?\r\n", "[<PERSON>] Hi, sure!\r\n", "[<PERSON>] Hi.\r\n", "[<PERSON>] So you wanna tell us now, or are we waiting for four wet bridesmaids?\r\n", "[<PERSON>] Oh God... well, it started about a half hour before the wedding. I was in the room where we were keeping all the presents, and I was looking at this gravy boat. This really gorgeous Lamauge gravy boat. When all of a sudden- Sweet 'n' Lo?- I realized that I was more turned on by this gravy boat than by <PERSON>! And then I got really freaked out, and that's when it hit me: how much <PERSON> looks like Mr. Po<PERSON>o Head. Y'know, I mean, I always knew looked familiar, but... Anyway, I just had to get out of there, and I started wondering 'Why am I doing this, and who am I doing this for?'. So anyway I just didn't know where to go, and I know that you and I have kinda drifted apart, but you're the only person I knew who lived here in the city.\r\n", "[<PERSON>] Who wasn't invited to the wedding.\r\n", "[<PERSON>] Ooh, I was kinda hoping that wouldn't be an issue...\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Now I'm guessing that he bought her the big pipe organ, and she's really not happy about it.\r\n", "[<PERSON>] Tuna or egg salad? Decide!\r\n", "[<PERSON>] I'll have whatever <PERSON> is having.\r\n", "[<PERSON>] Daddy, I just... I can't marry him! I'm sorry. I just don't love him. Well, it matters to me!\r\n", "[<PERSON>] If I let go of my hair, my head will fall off.\r\n", "[<PERSON>] Ooh, she should not be wearing those pants.\r\n", "[<PERSON>] I say push her down the stairs.\r\n", "[<PERSON>] Push her down the stairs! Push her down the stairs! Push her down the stairs!\r\n", "[<PERSON>] C'mon <PERSON>, listen to me! It's like, it's like, all of my life, everyone has always told me, 'You're a shoe! You're a shoe, you're a shoe, you're a shoe!'. And today I just stopped and I said, 'What if I don't wanna be a shoe? What if I wanna be a- a purse, y'know? Or a- or a hat! No, I'm not saying I want you to buy me a hat, I'm saying I am a ha- It's a metaphor, Daddy!\r\n", "[<PERSON>] You can see where he'd have trouble.\r\n", "[<PERSON>] Look <PERSON>, it's my life. Well maybe I'll just stay here with <PERSON>.\r\n", "[<PERSON>] Well, I guess we've established who's staying here with <PERSON>...\r\n", "[<PERSON>] Well, maybe that's my decision. Well, maybe I don't need your money. Wait!! Wait, I said maybe!!\r\n", "[<PERSON>] Just breathe, breathe.. that's it. Just try to think of nice calm things...\r\n", "[<PERSON>] Raindrops on roses and rabbits and kittens, bluebells and sleighbells and- something with mittens... La la la la...something and noodles with string. These are a few...\r\n", "[<PERSON>] I'm all better now.\r\n", "[<PERSON>] I helped!\r\n", "[<PERSON>] Okay, look, this is probably for the best, y'know? Independence. Taking control of your life. The whole, 'hat' thing.\r\n", "[<PERSON>] And hey, you need anything, you can always come to <PERSON>. <PERSON> and <PERSON> live across the hall. And he's away a lot.\r\n", "[<PERSON>] Joey, stop hitting on her! It's her wedding day!\r\n", "[<PERSON>] What, like there's a rule or something?\r\n", "[<PERSON>] Please don't do that again, it's a horrible sound.\r\n", "[<PERSON> Wine Guy] It's, uh, it's <PERSON>.\r\n", "[<PERSON>] Oh God, is it 6:30? Buzz him in!\r\n", "[<PERSON>] Who's <PERSON>?\r\n", "[<PERSON>] <PERSON> the Wine Guy, <PERSON>?\r\n", "[<PERSON>] Maybe. <PERSON>: Wait. Your 'not a real date' tonight is with <PERSON> the <PERSON> Guy?\r\n", "[<PERSON>] He finally asked you out?\r\n", "[<PERSON>] Yes!\r\n", "[<PERSON>] Ooh, this is a Dear Diary moment.\r\n", "[<PERSON>] Rach, wait, I can cancel...\r\n", "[<PERSON>] Please, no, go, that'd be fine!\r\n", "[<PERSON>] Are, are you okay? I mean, do you want me to stay?\r\n", "[<PERSON>] That'd be good...\r\n", "[<PERSON>] Really?\r\n", "[<PERSON>] No, go on! It's <PERSON> the Wine Guy!\r\n", "[<PERSON>] What does that mean? Does he sell it, drink it, or just complain a lot?\r\n", "[<PERSON>] Hi, come in! <PERSON>, this is.. ... everybody, everybody, this is <PERSON>.\r\n", "[#ALL#] Hey! <PERSON>! Hi! The Wine Guy! Hey!\r\n", "[<PERSON>] I'm sorry, I didn't catch your name. <PERSON>, was it?\r\n", "[<PERSON>] Okay, umm-umm, I'll just--I'll be right back, I just gotta go ah, go ah...\r\n", "[<PERSON>] A wandering?\r\n", "[<PERSON>] Change! Okay, sit down. Two seconds.\r\n", "[<PERSON>] Ooh, I just pulled out four eyelashes. That can't be good.\r\n", "[<PERSON>] Hey, <PERSON>!\r\n", "[<PERSON> Guy] Yeah?\r\n", "[<PERSON>] Here's a little tip, she really likes it when you rub her neck in the same spot over and over and over again until it starts to get a little red.\r\n", "[<PERSON>] Shut up, <PERSON>!\r\n", "[<PERSON>] So <PERSON>, what're you, uh... what're you up to tonight?\r\n", "[<PERSON>] Well, I was kinda supposed to be headed for Aruba on my honeymoon, so nothing!\r\n", "[<PERSON>] Right, you're not even getting your honeymoon, <PERSON>.. No, no, although, Aruba, this time of year... talk about your- -big lizards... Anyway, if you don't feel like being alone tonight, <PERSON> and <PERSON> are coming over to help me put together my new furniture.\r\n", "[<PERSON>] Yes, and we're very excited about it.\r\n", "[<PERSON>] Well actually thanks, but I think I'm just gonna hang out here tonight. It's been kinda a long day.\r\n", "[<PERSON>] Okay, sure.\r\n", "[<PERSON>] Hey <PERSON><PERSON><PERSON>, you wanna help?\r\n", "[<PERSON>] Oh, I wish I could, but I don't want to.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Love is sweet as summer showers, love is a wondrous work of art, but your love oh your love, your love...is like a giant pigeon...crapping on my heart. La-la-la-la-la- Thank you. La-la-la-la...ohhh!\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] I'm supposed to attach a brackety thing to the side things, using a bunch of these little worm guys. I have no brackety thing, I see no whim guys whatsoever and- I cannot feel my legs.\r\n", "[<PERSON>] I'm thinking we've got a bookcase here.\r\n", "[<PERSON>] It's a beautiful thing.\r\n", "[<PERSON>] What's this?\r\n", "[<PERSON>] I would have to say that is an 'L'-shaped bracket.\r\n", "[<PERSON>] Which goes where?\r\n", "[<PERSON>] I have no idea.\r\n", "[<PERSON>] Done with the bookcase!\r\n", "[<PERSON>] All finished!\r\n", "[<PERSON>] This was <PERSON>'s favorite beer. She always drank it out of the can, I should have known.\r\n", "[<PERSON>] Hey-hey-hey-hey, if you're gonna start with that stuff we're outta here.\r\n", "[<PERSON>] Yes, please don't spoil all this fun.\r\n", "[<PERSON>] <PERSON>, let me ask you a question. She got the furniture, the stereo, the good TV- what did you get?\r\n", "[<PERSON>] You guys.\r\n", "[<PERSON>] Oh, <PERSON>.\r\n", "[<PERSON>] You got screwed.\r\n", "[<PERSON>] Oh my God!\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Oh my God!\r\n", "[<PERSON> the <PERSON> Guy] I know, I know, I'm such an idiot. I guess I should have caught on when she started going to the dentist four and five times a week. I mean, how clean can teeth get?\r\n", "[<PERSON>] My brother's going through that right now, he's such a mess. How did you get through it?\r\n", "[<PERSON> the <PERSON> Guy] Well, you might try accidentally breaking something valuable of hers, say her-\r\n", "[<PERSON>] -leg?\r\n", "[<PERSON> the <PERSON> Guy] That's one way! Me, I- I went for the watch.\r\n", "[<PERSON>] You actually broke her watch? Wow! The worst thing I ever did was, I-I shredded by boyfriend's favorite bath towel.\r\n", "[<PERSON> the Wine Guy] Ooh, steer clear of you.\r\n", "[<PERSON>] That's right.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] <PERSON>, I'm sorry... I am so sorry... I know you probably think that this is all about what I said the other day about you making love with your socks on, but it isn't... it isn't, it's about me, and I ju- Hi, machine cut me off again... anyway...look, look, I know that some girl is going to be incredibly lucky to become Mrs. <PERSON>, but it isn't me, it's not me. And not that I have any idea who me is right now, but you just have to give me a chance too...\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] I'm divorced! I'm only 26 and I'm divorced!\r\n", "[<PERSON>] Shut up!\r\n", "[<PERSON>] You must stop!\r\n", "[<PERSON>] That only took me an hour.\r\n", "[<PERSON>] Look, <PERSON>, you gotta understand, between us we haven't had a relationship that has lasted longer than a Mento. You, however have had the love of a woman for four years. Four years of closeness and sharing at the end of which she ripped your heart out, and that is why we don't do it! I don't think that was my point!\r\n", "[<PERSON>] You know what the scariest part is? What if there's only one woman for everybody, y'know? I mean what if you get one woman- and that's it? Unfortunately in my case, there was only one woman- for her...\r\n", "[<PERSON>] What are you talking about? 'One woman'? That's like saying there's only one flavor of ice cream for you. <PERSON><PERSON> tell you something, <PERSON>. There's lots of flavors out there. There's Rocky Road, and Cookie <PERSON>h, and Bing! Cherry Vanilla. You could get 'em with Jimmies, or nuts, or whipped cream! This is the best thing that ever happened to you! You got married, you were, like, what, eight? Welcome back to the world! Grab a spoon!\r\n", "[<PERSON>] I honestly don't know if I'm hungry or horny.\r\n", "[<PERSON>] Stay out of my freezer!\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON> the <PERSON> Guy] Ever since she walked out on me, I, uh...\r\n", "[<PERSON>] What?..... What, you wanna spell it out with noodles?\r\n", "[<PERSON> Guy] No, it's, it's more of a fifth date kinda revelation.\r\n", "[<PERSON>] Oh, so there is gonna be a fifth date?\r\n", "[<PERSON> the Wine Guy] Isn't there?\r\n", "[<PERSON>] Yeah... yeah, I think there is. -What were you gonna say?\r\n", "[<PERSON> the <PERSON> Guy] Well, ever-ev-... ever since she left me, um, I haven't been able to, uh, perform. ...Sexually.\r\n", "[<PERSON>] Oh God, oh <PERSON>, I am sorry... I am so sorry...\r\n", "[<PERSON> Wine Guy] It's okay...\r\n", "[<PERSON>] I know being spit on is probably not what you need right now. Um... how long?\r\n", "[<PERSON> the Wine Guy] Two years.\r\n", "[<PERSON>] Wow! I'm-I'm-I'm glad you smashed her watch!\r\n", "[<PERSON> Wine Guy] So you still think you, um... might want that fifth date?\r\n", "[<PERSON>] ...Yeah. Yeah, I do.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Priest On Tv] We are gathered here today to join <PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Arcola in the bound of holy matrimony.\r\n", "[<PERSON>] Oh...see... but <PERSON> loved <PERSON><PERSON>! That's the difference!\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Grab a spoon. Do you know how long it's been since I've grabbed a spoon? Do the words '<PERSON>, don't be a hero' mean anything to you?\r\n", "[<PERSON>] Great story! But, I uh, I gotta go, I got a date with <PERSON>--<PERSON>--<PERSON>... Oh man,\r\n", "[<PERSON>] <PERSON>'s the screamer, <PERSON> has cats.\r\n", "[<PERSON>] Right. Thanks. It's June. I'm outta here.\r\n", "[<PERSON>] Y'know, here's the thing. Even if I could get it together enough to- to ask a woman out,... who am I gonna ask?\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Isn't this amazing? I mean, I have never made coffee before in my entire life.\r\n", "[<PERSON>] That is amazing.\r\n", "[<PERSON>] Congratulations.\r\n", "[<PERSON>] Y'know, I figure if I can make coffee, there isn't anything I can't do.\r\n", "[<PERSON>] If can invade Poland, there isn't anything I can't do.\r\n", "[<PERSON>] Listen, while you're on a roll, if you feel like you gotta make like a Western omelet or something... Although actually I'm really not that hungry...\r\n", "[<PERSON>] Oh good, <PERSON> and <PERSON><PERSON><PERSON> are here.\r\n", "[#ALL#] Morning. Good morning.\r\n", "[<PERSON> the Wine Guy] Morning.\r\n", "[<PERSON>] Morning, <PERSON>.\r\n", "[<PERSON>] <PERSON>, <PERSON>.\r\n", "[<PERSON>] Hi, <PERSON>, is it?\r\n", "[<PERSON> the <PERSON> Guy] Thank you! Thank you so much!\r\n", "[<PERSON>] Stop!\r\n", "[<PERSON> the <PERSON>] No, I'm telling you last night was like umm, all my birthdays, both graduations, plus the barn raising scene in Witness.\r\n", "[<PERSON>] We'll talk later.\r\n", "[<PERSON> the <PERSON> Guy] Yeah. Thank you.\r\n", "[<PERSON>] That wasn't a real date?! What the hell do you do on a real date?\r\n", "[<PERSON>] Shut up, and put my table back.\r\n", "[#ALL#] Okayyy!\r\n", "[<PERSON>] All right, kids, I gotta get to work. If I don't input those numbers,... it doesn't make much of a difference...\r\n", "[<PERSON>] So, like, you guys all have jobs?\r\n", "[<PERSON>] Yeah, we all have jobs. See, that's how we buy stuff.\r\n", "[<PERSON>] Yeah, I'm an actor.\r\n", "[<PERSON>] Wow! Would I have seen you in anything?\r\n", "[<PERSON>] I doubt it. Mostly regional work.\r\n", "[<PERSON>] Oh wait, wait, unless you happened to catch the Reruns' production of <PERSON><PERSON><PERSON><PERSON>, at the little theater in the park.\r\n", "[<PERSON>] Look, it was a job all right?\r\n", "[<PERSON>] '<PERSON>, <PERSON><PERSON><PERSON><PERSON>, I'm a real live boy.'\r\n", "[<PERSON>] I will not take this abuse.\r\n", "[<PERSON>] You're right, I'm sorry. \"Once I was a wooden boy, a little wooden boy...\"\r\n", "[<PERSON>] You should both know, that he's a dead man. Oh, <PERSON>?\r\n", "[<PERSON>] So how you doing today? Did you sleep okay? Talk to <PERSON>? I can't stop smiling.\r\n", "[<PERSON>] I can see that. You look like you slept with a hanger in your mouth.\r\n", "[<PERSON>] I know, he's just so, so... Do you remember you and <PERSON>?\r\n", "[<PERSON>] Oh, yeah.\r\n", "[<PERSON>] Well, it's like that. With feelings.\r\n", "[<PERSON>] Oh wow. Are you in trouble.\r\n", "[<PERSON>] Big time!\r\n", "[<PERSON>] Want a wedding dress? Hardly used.\r\n", "[<PERSON>] I think we are getting a little ahead of selves here. Okay. Okay. I am just going to get up, go to work and not think about him all day. Or else I'm just gonna get up and go to work.\r\n", "[<PERSON>] Oh, look, wish me luck!\r\n", "[<PERSON>] What for?\r\n", "[<PERSON>] I'm gonna go get one of those job things.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON><PERSON><PERSON>] Hey, <PERSON>!\r\n", "[<PERSON>] Hey <PERSON><PERSON><PERSON>, welcome back! How was Florida?\r\n", "[<PERSON><PERSON><PERSON>] You had sex, didn't you?\r\n", "[<PERSON>] How do you do that?\r\n", "[<PERSON><PERSON><PERSON>] Oh, I hate you, I'm pushing my Aunt <PERSON><PERSON> through Parrot Jungle and you're having sex! So? Who?\r\n", "[<PERSON>] You know <PERSON>?\r\n", "[<PERSON><PERSON><PERSON>] <PERSON> the Wine Guy? Oh yeah, I know <PERSON>.\r\n", "[<PERSON>] You mean you know <PERSON> like I know <PERSON>?\r\n", "[<PERSON><PERSON><PERSON>] Are you kidding? I take credit for <PERSON>. Y'know before me, there was no snap in his turtle for two years.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Of course it was a line!\r\n", "[<PERSON>] Why?! Why? Why, why would anybody do something like that?\r\n", "[<PERSON>] I assume we're looking for an answer more sophisticated than 'to get you into bed'.\r\n", "[<PERSON>] I hate men! I hate men!\r\n", "[<PERSON>] Oh no, don't hate, you don't want to put that out into the universe.\r\n", "[<PERSON>] Is it me? Is it like I have some sort of beacon that only dogs and men with severe emotional problems can hear?\r\n", "[<PERSON>] All right, c'mere, gimme your feet.\r\n", "[<PERSON>] I just thought he was nice, y'know?\r\n", "[<PERSON>] I can't believe you didn't know it was a line!\r\n", "[<PERSON>] Guess what?\r\n", "[<PERSON>] You got a job?\r\n", "[<PERSON>] Are you kidding? I'm trained for nothing! I was laughed out of twelve interviews today.\r\n", "[<PERSON>] And yet you're surprisingly upbeat.\r\n", "[<PERSON>] You would be too if you found <PERSON> and <PERSON> boots on sale, fifty percent off!\r\n", "[<PERSON>] Oh, how well you know me...\r\n", "[<PERSON>] They're my new 'I don't need a job, I don't need my parents, I've got great boots' boots!\r\n", "[<PERSON>] How'd you pay for them?\r\n", "[<PERSON>] Uh, credit card.\r\n", "[<PERSON>] And who pays for that?\r\n", "[<PERSON>] Um... my... father.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Oh God, come on you guys, is this really necessary? I mean, I can stop charging anytime I want.\r\n", "[<PERSON>] C'mon, you can't live off your parents your whole life.\r\n", "[<PERSON>] I know that. That's why I was getting married.\r\n", "[<PERSON>] Give her a break, it's hard being on your own for the first time.\r\n", "[<PERSON>] Thank you.\r\n", "[<PERSON>] You're welcome. I remember when I first came to this city. I was fourteen. My mom had just killed herself and my step-dad was back in prison, and I got here, and I didn't know anybody. And I ended up living with this albino guy who was, like, cleaning windshields outside port authority, and then he killed himself, and then I found aromatherapy. So believe me, I know exactly how you feel.\r\n", "[<PERSON>] The word you're looking for is 'Anyway'...\r\n", "[<PERSON>] All right, you ready?\r\n", "[<PERSON>] No. No, no, I'm not ready! How can I be ready? \"Hey, <PERSON><PERSON>! You ready to jump out the airplane without your parachute?\" Come on, I can't do this!\r\n", "[<PERSON>] You can, I know you can!\r\n", "[<PERSON>] I don't think so.\r\n", "[<PERSON>] Come on, you made coffee! You can do anything!\r\n", "[<PERSON>] C'mon, cut. Cut, cut, cut,...\r\n", "[#ALL#] Cut, cut, cut, cut, cut, cut, cut...\r\n", "[<PERSON>] Y'know what? I think we can just leave it at that. It's kinda like a symbolic gesture...\r\n", "[<PERSON>] Rachel! That was a library card!\r\n", "[#ALL#] Cut, cut, cut, cut, cut, cut, cut..\r\n", "[<PERSON>] Y'know, if you listen closely, you can hear a thousand retailers scream.\r\n", "[<PERSON>] Welcome to the real world! It sucks. You're gonna love it!\r\n", "[<PERSON>] Well, that's it <PERSON> gonna crash on the couch?\r\n", "[<PERSON>] No. No, I gotta go home sometime.\r\n", "[<PERSON>] You be okay?\r\n", "[<PERSON>] Yeah.\r\n", "[<PERSON>] Hey <PERSON>, look what I just found on the floor. What?\r\n", "[<PERSON>] That's <PERSON>'s watch. You just put it back where you found it. Oh boy. Alright. Goodnight, everybody.\r\n", "[<PERSON>] Goodnight.\r\n", "[<PERSON>] Mmm. Oh, no-\r\n", "[<PERSON>] Sorry-\r\n", "[<PERSON>] No no no, go-\r\n", "[<PERSON>] No, you have it, really, I don't want it-\r\n", "[<PERSON>] Split it?\r\n", "[<PERSON>] Okay.\r\n", "[<PERSON>] Okay. You know you probably didn't know this, but back in high school, I had a, um, major crush on you.\r\n", "[<PERSON>] I knew.\r\n", "[<PERSON>] You did! Oh.... I always figured you just thought I was <PERSON>'s geeky older brother.\r\n", "[<PERSON>] I did.\r\n", "[<PERSON>] Oh. Listen, do you think- and try not to let my intense vulnerability become any kind of a factor here- but do you think it would be okay if I asked you out? Sometime? Maybe?\r\n", "[<PERSON>] Yeah, maybe...\r\n", "[<PERSON>] Okay... okay, maybe I will...\r\n", "[<PERSON>] Goodnight.\r\n", "[<PERSON>] Goodnight.\r\n", "[<PERSON>] See ya.... <PERSON><PERSON><PERSON>, what's with you?\r\n", "[<PERSON>] I just grabbed a spoon.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] I can't believe what I'm hearing here.\r\n", "[<PERSON>] I can't believe what I'm hearing here...\r\n", "[<PERSON>] What? I-I said you had a-\r\n", "[<PERSON>] What I said you had...\r\n", "[<PERSON>] Would you stop?\r\n", "[<PERSON>] Oh, was I doing it again?\r\n", "[#ALL#] Yes!\r\n", "[<PERSON>] I said that you had a nice butt, it's just not a great butt.\r\n", "[<PERSON>] Oh, you wouldn't know a great butt if it came up and bit ya.\r\n", "[<PERSON>] There's an image.\r\n", "[<PERSON>] Would anybody like more coffee?\r\n", "[<PERSON>] Did you make it, or are you just serving it?\r\n", "[<PERSON>] I'm just serving it.\r\n", "[#ALL#] Yeah. Yeah, I'll have a cup of coffee.\r\n", "[<PERSON>] Kids, new dream... I'm in Las Vegas.\r\n", "[Customer] Ahh, miss? More coffee?\r\n", "[<PERSON>] Ugh. Excuse me, could you give this to that guy over there? Go ahead. Thank you. Sorry. Okay, Las Vegas.\r\n", "[<PERSON>] Okay, so, I'm in Las Vegas... I'm <PERSON>-\r\n", "\n"]}], "source": ["print(data[\"TEXT\"].values[0])"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>METADATA</th>\n", "      <th>SOURCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Friends - The Pilot\\r\\n\\r\\n[<PERSON>] The...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Friends - The One With the Sonogram at the End...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Friends - The One With the Thumb\\r\\n\\r\\n[Phoeb...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Friends - The One With <PERSON>\\r...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Friends - The One With the East German Laundry...</td>\n", "      <td>{\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...</td>\n", "      <td>friends/emorynlp</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                TEXT  \\\n", "0  Friends - The Pilot\\r\\n\\r\\n[<PERSON>] The...   \n", "1  Friends - The One With the Sonogram at the End...   \n", "2  Friends - The One With the Thumb\\r\\n\\r\\n[Phoeb...   \n", "3  Friends - The One With <PERSON>\\r...   \n", "4  Friends - The One With the East German Laundry...   \n", "\n", "                                            METADATA            SOURCE  \n", "0  {\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...  friends/emorynlp  \n", "1  {\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...  friends/emorynlp  \n", "2  {\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...  friends/emorynlp  \n", "3  {\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...  friends/emorynlp  \n", "4  {\"show\": \"Friends\", \"season\": \"s01\", \"episode\"...  friends/emorynlp  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["data.to_parquet(\"friends.pq\", row_group_size=100, engine=\"pyarrow\", index=False)\n", "data.head()  # https://github.com/emorynlp/character-mining"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["236"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["len(data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# The Office"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# office via https://www.kaggle.com/datasets/nasirkhalid24/the-office-us-complete-dialoguetranscript\n", "kaggle.api.dataset_download_files(\"nasirkhalid24/the-office-us-complete-dialoguetranscript\", \"office\", unzip=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>season</th>\n", "      <th>episode</th>\n", "      <th>title</th>\n", "      <th>scene</th>\n", "      <th>speaker</th>\n", "      <th>line</th>\n", "      <th>group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Pilot</td>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>All right <PERSON>. Your quarterlies look very good...</td>\n", "      <td>1_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Pilot</td>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>Oh, I told you. I couldn't close it. So...</td>\n", "      <td>1_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Pilot</td>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>So you've come to the master for guidance? Is ...</td>\n", "      <td>1_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Pilot</td>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>Actually, you called me in here, but yeah.</td>\n", "      <td>1_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Pilot</td>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td>All right. Well, let me show you how it's done.</td>\n", "      <td>1_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54621</th>\n", "      <td>9</td>\n", "      <td>24</td>\n", "      <td>Finale</td>\n", "      <td>8153</td>\n", "      <td>Creed</td>\n", "      <td>It all seems so very arbitrary. I applied for ...</td>\n", "      <td>9_24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54622</th>\n", "      <td>9</td>\n", "      <td>24</td>\n", "      <td>Finale</td>\n", "      <td>8154</td>\n", "      <td>Meredith</td>\n", "      <td>I just feel lucky that I got a chance to share...</td>\n", "      <td>9_24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54623</th>\n", "      <td>9</td>\n", "      <td>24</td>\n", "      <td>Finale</td>\n", "      <td>8155</td>\n", "      <td>Phyllis</td>\n", "      <td>I'm happy that this was all filmed so I can re...</td>\n", "      <td>9_24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54624</th>\n", "      <td>9</td>\n", "      <td>24</td>\n", "      <td>Finale</td>\n", "      <td>8156</td>\n", "      <td><PERSON></td>\n", "      <td>I sold paper at this company for 12 years. My ...</td>\n", "      <td>9_24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54625</th>\n", "      <td>9</td>\n", "      <td>24</td>\n", "      <td>Finale</td>\n", "      <td>8157</td>\n", "      <td>Pam</td>\n", "      <td>I thought it was weird when you picked us to m...</td>\n", "      <td>9_24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>54626 rows × 7 columns</p>\n", "</div>"], "text/plain": ["       season  episode   title  scene   speaker  \\\n", "0           1        1   Pilot      1   Michael   \n", "1           1        1   Pilot      1       Jim   \n", "2           1        1   Pilot      1   Michael   \n", "3           1        1   Pilot      1       Jim   \n", "4           1        1   Pilot      1   Michael   \n", "...       ...      ...     ...    ...       ...   \n", "54621       9       24  Finale   8153     Creed   \n", "54622       9       24  Finale   8154  Meredith   \n", "54623       9       24  Finale   8155   Phyllis   \n", "54624       9       24  Finale   8156       Jim   \n", "54625       9       24  Finale   8157       Pam   \n", "\n", "                                                    line group  \n", "0      All right <PERSON>. Your quarterlies look very good...   1_1  \n", "1             Oh, I told you. I couldn't close it. So...   1_1  \n", "2      So you've come to the master for guidance? Is ...   1_1  \n", "3             Actually, you called me in here, but yeah.   1_1  \n", "4        All right. Well, let me show you how it's done.   1_1  \n", "...                                                  ...   ...  \n", "54621  It all seems so very arbitrary. I applied for ...  9_24  \n", "54622  I just feel lucky that I got a chance to share...  9_24  \n", "54623  I'm happy that this was all filmed so I can re...  9_24  \n", "54624  I sold paper at this company for 12 years. My ...  9_24  \n", "54625  I thought it was weird when you picked us to m...  9_24  \n", "\n", "[54626 rows x 7 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["office = pd.read_csv(\"office/The-Office-Lines-V4.csv\", sep=\",\")\n", "office.drop(columns=[\"Unnamed: 6\"], inplace=True)\n", "office[\"group\"] = office[[\"season\", \"episode\"]].apply(lambda x: f\"{x[0]}_{x[1]}\", axis=1)\n", "office"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████| 186/186 [00:06<00:00, 29.75it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>METADATA</th>\n", "      <th>SOURCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>The Office - Pilot\\r\\n\\r\\n[<PERSON>] All right ...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s01\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>The Office - Diversity Day\\r\\n\\r\\n[<PERSON>] He...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s01\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>The Office - Health Care\\r\\n\\r\\n[<PERSON>] Pam....</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s01\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>The Office - The Alliance\\r\\n\\r\\n[<PERSON>] Mich...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s01\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>The Office - Basketball\\r\\n\\r\\n[<PERSON>] Hey, ...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s01\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>The Office - Here Comes Treble\\r\\n\\r\\n[<PERSON>]...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s09\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>The Office - The Boat\\r\\n\\r\\n[<PERSON>] Can you g...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s09\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>The Office - The Whale\\r\\n\\r\\n[<PERSON>] Ah, what ...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s09\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>184</th>\n", "      <td>The Office - The Target\\r\\n\\r\\n[<PERSON>] Yesterd...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s09\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>The Office - <PERSON>\\r\\n\\r\\n[<PERSON>] I'm...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s09\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>186 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                                                  TEXT  \\\n", "0    The Office - Pilot\\r\\n\\r\\n[<PERSON>] All right ...   \n", "1    The Office - Diversity Day\\r\\n\\r\\n[<PERSON>] He...   \n", "2    The Office - Health Care\\r\\n\\r\\n[<PERSON>] Pam....   \n", "3    The Office - The Alliance\\r\\n\\r\\n[<PERSON>] Mich...   \n", "4    The Office - Basketball\\r\\n\\r\\n[<PERSON>] Hey, ...   \n", "..                                                 ...   \n", "181  The Office - Here Comes Treble\\r\\n\\r\\n[<PERSON>]...   \n", "182  The Office - The Boat\\r\\n\\r\\n[<PERSON>] Can you g...   \n", "183  The Office - The Whale\\r\\n\\r\\n[<PERSON>] Ah, what ...   \n", "184  The Office - The Target\\r\\n\\r\\n[<PERSON>] Yesterd...   \n", "185  The Office - <PERSON>\\r\\n\\r\\n[<PERSON>] I'm...   \n", "\n", "                                              METADATA                SOURCE  \n", "0    {\"show\": \"The Office\", \"season\": \"s01\", \"episo...  office/nasirkhalid24  \n", "1    {\"show\": \"The Office\", \"season\": \"s01\", \"episo...  office/nasirkhalid24  \n", "2    {\"show\": \"The Office\", \"season\": \"s01\", \"episo...  office/nasirkhalid24  \n", "3    {\"show\": \"The Office\", \"season\": \"s01\", \"episo...  office/nasirkhalid24  \n", "4    {\"show\": \"The Office\", \"season\": \"s01\", \"episo...  office/nasirkhalid24  \n", "..                                                 ...                   ...  \n", "181  {\"show\": \"The Office\", \"season\": \"s09\", \"episo...  office/nasirkhalid24  \n", "182  {\"show\": \"The Office\", \"season\": \"s09\", \"episo...  office/nasirkhalid24  \n", "183  {\"show\": \"The Office\", \"season\": \"s09\", \"episo...  office/nasirkhalid24  \n", "184  {\"show\": \"The Office\", \"season\": \"s09\", \"episo...  office/nasirkhalid24  \n", "185  {\"show\": \"The Office\", \"season\": \"s09\", \"episo...  office/nasirkhalid24  \n", "\n", "[186 rows x 3 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["data = {\"TEXT\": [], \"METADATA\": [], \"SOURCE\": []}\n", "for name, group in tqdm(office.groupby(\"group\")):\n", "    metadata = {\n", "        \"show\": \"The Office\",\n", "        \"season\": f\"s{str(group['season'].values[0]).z<PERSON>(2)}\",\n", "        \"episode\": f\"e{str(group['episode'].values[0]).z<PERSON>(2)}\",\n", "        \"title\": group[\"title\"].values[0],\n", "    }\n", "    text, last_scene = f\"The Office - {metadata['title']}\\r\\n\\r\\n\", None\n", "    for index, row in group.iterrows():\n", "        if last_scene is None:\n", "            last_scene = row[\"scene\"]\n", "        elif last_scene != row[\"scene\"]:\n", "            last_scene = row[\"scene\"]\n", "            text += \"\\r\\n---------------------------------------\\r\\n\\r\\n\"\n", "        if pd.isna(row[\"speaker\"]) or pd.isna(row[\"line\"]):\n", "            continue\n", "        text += f\"[{row['speaker'].strip()}] {row['line'].strip()}\\r\\n\"\n", "    data[\"TEXT\"].append(text)\n", "    data[\"METADATA\"].append(json.dumps(metadata))\n", "    data[\"SOURCE\"].append(\"office/nasirkhalid24\")\n", "data = pd.DataFrame(data)\n", "data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Office - Pilot\r\n", "\r\n", "[<PERSON>] All right <PERSON>. Your quarterlies look very good. How are things at the library?\r\n", "[<PERSON>] Oh, I told you. I couldn't close it. So...\r\n", "[<PERSON>] So you've come to the master for guidance? Is this what you're saying, grasshopper?\r\n", "[<PERSON>] Actually, you called me in here, but yeah.\r\n", "[<PERSON>] All right. Well, let me show you how it's done.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] <PERSON>, I'd like to speak to your office manager, please. Yes, hello. This is <PERSON>. I am the Regional Manager of Dunder Mifflin Paper Products. Just wanted to talk to you manager-a-manger.  All right. Done deal. Thank you very much, sir. You're a gentleman and a scholar. Oh, I'm sorry. OK. I'm sorry. My mistake.  That was a woman I was talking to, so... She had a very low voice. Probably a smoker, so...  So that's the way it's done.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] I've, uh, I've been at Dunder Mifflin for 12 years, the last four as Regional Manager. If you want to come through here... See we have the entire floor. So this is my kingdom, as far as the eye can see. This is our receptionist, <PERSON>. <PERSON>! Pam-<PERSON>! <PERSON>ly. <PERSON> has been with us for... forever. Right, <PERSON>?\r\n", "[<PERSON>] Well. I don't know.\r\n", "[<PERSON>] If you think she's cute now, you should have seen her a couple of years ago.\r\n", "[Pam] What?\r\n", "[<PERSON>] Any messages?\r\n", "[<PERSON>] Uh, yeah. Just a fax.\r\n", "[<PERSON>] Oh! <PERSON>, this is from Corporate. How many times have I told you? There's a special filing cabinet for things from corporate.\r\n", "[Pam] You haven't told me.\r\n", "[<PERSON>] It's called the wastepaper basket! Look at that! Look at that face.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] People say I am the best boss. They go, \"God we've never worked in a place like this before. You're hilarious.\" \"And you get the best out of us.\"  I think that pretty much sums it up. I found it at Spencer Gifts.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Shall I play for you? Pa rum pump um pum  I have no gifts for you. Pa rum pump um pum\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Jim] My job is to speak to clients on the phone about... uh, quantities and type of copier paper. You know, whether we can supply it to them. Whether they can pay for it. And... I'm boring myself just talking about this.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] W<PERSON><PERSON><PERSON>!\r\n", "[<PERSON>] Whas<PERSON><PERSON>! I still love that after seven years.\r\n", "[<PERSON>] W<PERSON><PERSON><PERSON>!\r\n", "[<PERSON>] Whassup!\r\n", "[<PERSON>] Whass...up!\r\n", "[<PERSON>] <PERSON>.\r\n", "[<PERSON>] What?\r\n", "[<PERSON>] Nothing.\r\n", "[<PERSON>] OK. All right. See you later.\r\n", "[<PERSON>] All right. Take care.\r\n", "[<PERSON>] Back to work.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Jan] Just before lunch. That would be great.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] <PERSON> really doesn't really interfere with me at all. <PERSON>.  <PERSON>, hello. I call her <PERSON>. Right? Not to her face, because... well, not because I'm scared of her. Because I'm not. But, um... Yeah.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Jan] Alright, was there anything you wanted to add to the agenda?\r\n", "[<PERSON>] Um... Me no get an agenda.\r\n", "[Jan] What? I'm sorry?\r\n", "[<PERSON>] I didn't get any agenda.\r\n", "[Jan] Well, I faxed one over to you this morning.\r\n", "[<PERSON>] Really? I didn't...  Did we get a fax this morning?\r\n", "[<PERSON>] Uh, yeah, the one...\r\n", "[<PERSON>] Why isn't it in my hand? A company runs on efficiency of communication, right? So what's the problem, <PERSON>? Why didn't I get it?\r\n", "[Pam] You put in the garbage can that was a special filing cabinet.\r\n", "[<PERSON>] Yeah, that was a joke. That was a joke that was actually my brother's, and... It was supposed to be with bills and it doesn't work great with faxes.\r\n", "[Jan] Do you want to look at mine?\r\n", "[<PERSON>] Yeah, yeah. <PERSON><PERSON>. Thank you.\r\n", "[Jan] OK. Since the last meeting, <PERSON> and the board have decided we can't justify a Scranton branch and a Stamford branch.\r\n", "[<PERSON>] OK...\r\n", "[Jan] <PERSON>, don't panic.\r\n", "[<PERSON>] No, no, no, no, this is good. This is good. This is fine. Excellent.\r\n", "[Jan] No, no, no, <PERSON>, listen OK. Don't panic. We haven't made... We haven't decided.\r\n", "[Michael] All the alarm bells are kind of going... ringie-dingie-ding!\r\n", "[Jan] I've spoken to <PERSON> in Stamford. I've told him the same as you and it's up to either him or you to convince me that your branch can incorporate the other.\r\n", "[<PERSON>] OK. No problem.\r\n", "[Jan] This does, however, mean that there is going to be downsizing.\r\n", "[<PERSON>] Me no wanna hear that, <PERSON>. Because downsizing is a bitch. It is a real bitch. And I wouldn't wish that on <PERSON>'s men. I certainly wouldn't wish it on my men. Or women, present company excluded. Sorry. Uh, is <PERSON> concerned about downsizing himself? Not downsizing himself but is he concerned about downsizing?\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Question. How long do we have to...  Oh uh, <PERSON>, terrific rep. Do you mind if I take it?\r\n", "[Jan] Go ahead.\r\n", "[<PERSON>.\r\n", "[<PERSON>] Hey, you big queen.\r\n", "[<PERSON>] Oh, that's not appropriate.\r\n", "[<PERSON>] Hey, is old God<PERSON><PERSON> coming in today?\r\n", "[<PERSON>] Uh, I don't know what you mean.\r\n", "[<PERSON>] I've been meaning to ask her one question. Does the carpet match the drapes?\r\n", "[<PERSON>] Oh, my God! Oh! That's... horrifying. Horrible. Horrible person.\r\n", "[Jan] So do you think we could keep a lid on this for now? I don't want to worry people unnecessarily.\r\n", "[<PERSON>] No, absolutely. Under this regime, it will not leave this office.  Like that.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] So what does downsizing actually mean?\r\n", "[<PERSON>] Well...\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] You guys better update your resumes just like I'm doing.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] I bet it's gonna be me. Probably gonna be me.\r\n", "[<PERSON>] Yeah, it'll be you.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Pam] I have an important question for you.\r\n", "[<PERSON>] Yes?\r\n", "[Pam] Are you going to <PERSON>'s cat party on Sunday?\r\n", "[<PERSON>] Yeah, stop. That is ridiculous.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Michael] Am I going to tell them? No, I am not going to tell them. I don't see the point of that. As a doctor, you would not tell a patient if they had cancer.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Hey.\r\n", "[<PERSON>] Hey.\r\n", "[Pam] This is Mr<PERSON>.\r\n", "[<PERSON>] Guilty! Guilty as charged!\r\n", "[<PERSON>] <PERSON> from the temp agency. <PERSON><PERSON> sent me down to start today.\r\n", "[<PERSON>] <PERSON>, like <PERSON>. Three Stooges.\r\n", "[<PERSON>] Yup.\r\n", "[<PERSON>] Watch this. This is <PERSON><PERSON> Nyuck-nyuck-nyuck-nyuck-nyuck. Mee!  Ah, right here. Three Stooges. Oh, <PERSON>. It's a guy thing, <PERSON>. I'm sort of a student of comedy. Watch this. Here we go.  I'm Hitler. Adolf <PERSON>.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] I don't think it would be the worst thing if they let me go because then I might... I don't think it's many little girls' dream to be a receptionist. I like to do illustrations. Um... Mostly watercolor. A few oil pencil. Um, <PERSON> thinks they're good.\r\n", "[<PERSON>] <PERSON><PERSON>. This is <PERSON>.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Sure. Mr. <PERSON>, let me call you right back. Yeah, something just came up. Two minutes. Thank you very much. <PERSON>, what are you doing?\r\n", "[<PERSON>] What?\r\n", "[<PERSON>] What are you doing?\r\n", "[<PERSON>] Just clearing my desk. I can't concentrate.\r\n", "[<PERSON>] It's not on your desk.\r\n", "[<PERSON>] It's overlapping. It's all spilling over the edge. One word, two syllables. Demarcation.\r\n", "[<PERSON>] You can't do that.\r\n", "[<PERSON>] Why not?\r\n", "[<PERSON>] Safety violation. I could fall and pierce an organ.\r\n", "[<PERSON>] We'll see.  This is why the whole downsizing thing just doesn't bother me.\r\n", "[<PERSON>] Downs<PERSON>?\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Downsizing? I have no problem with that. I have been recommending downsizing since I first got here. I even brought it up in my interview. I say, bring it on.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Pam] You just still have these messages from yesterday.\r\n", "[<PERSON>] <PERSON>. Everything's under control. Uh, yeah. Yeah. That's important. Right. Oh this is so important, I should run to answer it.\r\n", "[Pam] What?\r\n", "[<PERSON>] Come on. Six-Million Dollar Man! <PERSON>! Actually, that would be a good salary for me, don't you think? Six million dollars? Memo to <PERSON>. I deserve a raise.\r\n", "[<PERSON>] Don't we all?\r\n", "[<PERSON>] I'm sorry?\r\n", "[<PERSON>] Nothing.\r\n", "[<PERSON>] If you're unhappy with your compensation, maybe you should take it up with HR. OK. Not today, OK? <PERSON>, just be professional.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] I think I'm a role model here. I think I garner people's respect.  Attention all Dunder Mi<PERSON>lin employees, please. We have a meeting in the conference room, ASAP.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] People I respect, heroes of mine, would be <PERSON>... <PERSON>, definitely. <PERSON><PERSON>. And probably <PERSON> would be the fourth one. And I just think all those people really helped the world in so many ways that it's really beyond words. It's really incalculable.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Now I know there's some rumors out there and I just kind of want to set the record straight.\r\n", "[<PERSON>] I'm Assistant Regional Manager. I should know first.\r\n", "[<PERSON>] Assistant to the Regional Manager.\r\n", "[<PERSON>] OK, um, can you just tell me please? Just tell me quietly. Can you whisper it in my ear?\r\n", "[<PERSON>] I'm about to tell everybody. I'm just about to tell everybody.\r\n", "[<PERSON>] Can't you just tell us.\r\n", "[<PERSON>] Please, OK? Do you want me to tell 'em?\r\n", "[<PERSON>] You don't know what it is.\r\n", "[<PERSON>] OK. You tell 'em. With my permission. Permission granted.\r\n", "[<PERSON>] I don't need your permission.\r\n", "[<PERSON>] Go ahead.\r\n", "[<PERSON>] Corporate has deemed it appropriate to enforce an ultimatum upon me. And <PERSON> is thinking about downsizing either the Stamford branch or this branch.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] I heard they might be closing this branch down. That's just the rumor going around. This is my first day. I don't really know.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Yeah but <PERSON>, what if they downsize here?\r\n", "[<PERSON>] Not gonna happen.\r\n", "[<PERSON>] It could be out of your hands <PERSON>.\r\n", "[<PERSON>] It won't be out of my hands <PERSON>, <PERSON>. I promise you that.\r\n", "[<PERSON>] Can you promise that?\r\n", "[<PERSON>] On his mother's grave.\r\n", "[Michael] No.\r\n", "[<PERSON>] What?\r\n", "[<PERSON>] Well, yeah, it is a promise. And frankly, I'm a little insulted that you have to keep asking about it.\r\n", "[<PERSON>] It's just that we need to know.\r\n", "[<PERSON>] I know. Hold on a second. I think <PERSON> wanted to say something. <PERSON>, you had a look that you wanted to ask a question just then.\r\n", "[<PERSON>] I was in the meeting with <PERSON> and she did say that it could be this branch that gets the axe.\r\n", "[Man] Are you sure about that?\r\n", "[<PERSON>] Well, <PERSON> maybe you should stick to the ongoing confidentiality agreement of meetings.\r\n", "[<PERSON>] <PERSON>, information is power.\r\n", "[<PERSON>] You can't say for sure whether it'll be us or them, can you?\r\n", "[<PERSON>] No, <PERSON>. No, you did not see me in there with her. I said if <PERSON> wants to come in here and interfere, then they're gonna have to go through me. Right? You can go mess with <PERSON>'s people, but I'm the head of this family, and you ain't gonna be messing with my chillin.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Jim] If I left, what would I do with all this useless information in my head? You know? Tonnage price of manila folders? Um, <PERSON>'s favorite flavor of yogurt, which is mixed berry.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] <PERSON> said mixed berries? Well, yeah, he's on to me. Um...\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Watch out for this guy. <PERSON> in the building. This is <PERSON>, the new temp.\r\n", "[<PERSON>] What's up? Nice to meet you.\r\n", "[<PERSON>] Introduce yourself. Be polite.\r\n", "[<PERSON>] <PERSON>, Assistant Regional Manager.\r\n", "[<PERSON>] Assistant to the Regional Manager. So, uh, <PERSON> tell him about the kung fu and the car and everything.\r\n", "[<PERSON>] Uh... yeah I got a '78 280Z. Bought it for $1,200. Fixed it up. It's now worth three grand.\r\n", "[<PERSON>] That's his profit.\r\n", "[<PERSON>] New engine, new suspension, I got a respray. I've got some photos.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Damn it! Jim!\r\n", "[<PERSON>] OK. Hold on, hold on. The judge is in session. What is the problem here?\r\n", "[<PERSON>] He put my stuff in Jell-O again.\r\n", "[<PERSON>] That's real professional thanks. That's the third time and it wasn't funny the first two times either <PERSON>.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] It's OK here, but people sometimes take advantage because it's so relaxed. I'm a volunteer Sheriff's Deputy on the weekends. And you cannot screw around there. That's sort of one of the rules.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] What is that?\r\n", "[<PERSON>] That is my stapler.\r\n", "[<PERSON>] No, no, no. Do not take it out. You have to eat it out of there, because there are starving people in the world  which I hate, and it is a waste of that kind of food.\r\n", "[<PERSON>] OK you know what, you can be a witness.  Can you reprimand him?\r\n", "[<PERSON>] How do you know it was me?\r\n", "[<PERSON>] It's always you. Are you going to discipline him or not?\r\n", "[<PERSON>] Discip<PERSON>. Kinky!  All right, here's the deal you guys. The thing about a practical joke is you have to know when to start and as well as when to stop.\r\n", "[<PERSON>] Yeah.\r\n", "[<PERSON>] And yeah, <PERSON> this is the time to stop putting <PERSON>'s personal effects into Jell-O.\r\n", "[<PERSON>] OK. <PERSON>, I'm sorry, because I have always been your biggest flan.\r\n", "[<PERSON>] <PERSON>. That's the way it is around here. It just kind of goes round and round.\r\n", "[<PERSON>] You should've put him in custardy.\r\n", "[<PERSON>] Hey! Yes! New guy! He scores.\r\n", "[<PERSON>] OK, that's great. I guess what I'm most concerned with is damage to company property. That's all.\r\n", "[<PERSON>] Pudding. Pudding... I'm trying to think of another dessert to do.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Jim] Do you like going out at the end of the week for a drink?\r\n", "[<PERSON>] Yeah.\r\n", "[<PERSON>] That's why we're all going out. So we can have an end-of-the-week-drink.\r\n", "[<PERSON>] So when are we going out?\r\n", "[<PERSON>] Tonight, hopefully.\r\n", "[<PERSON>] OK. Yeah.\r\n", "[<PERSON>] Hey, man.\r\n", "[<PERSON>] What's going on?\r\n", "[<PERSON>] Hey, baby.\r\n", "[<PERSON>] Hey.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] <PERSON>'s my fiance. We've been engaged about three years. We were supposed to get married in September but I think we're gonna get married in the spring.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Pam] Do you mind if I go out for a drink with these guys?\r\n", "[<PERSON>] No, no. Come on. Let's get out of here and go home.\r\n", "[<PERSON>] OK. I'm gonna be a few minutes. So it's only twenty past five. I still have to do my faxes.\r\n", "[<PERSON>] You know what? You should come with us. Because you know we're all going out and it could be a good chance for you to see what people are like outside the office. I think it could be fun.\r\n", "[<PERSON>] It sounds good. Seriously, we've gotta get going.\r\n", "[<PERSON>] Yeah, yeah.\r\n", "[Jim] Um... What's in the bag?\r\n", "[<PERSON>] Just tell her I'll talk to her later.\r\n", "[<PERSON>] No, definitely. All right, dude. Awesome. Will do.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[Jim] Do I think I'll be invited to the wedding?\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] So have you felt the vibe yet? We work hard, we play hard. Sometimes we play hard when we should be working hard. Right? I guess the atmosphere that I've created here is that I'm a friend first, and a boss second... and probably an entertainer third.  Just a second. Right? Oh, hey do you like The <PERSON> Experiment? Punk'd and all that kind of stuff?\r\n", "[<PERSON>] Yeah.\r\n", "[<PERSON>] You are gonna be my accomplice. Just go along with it, OK?\r\n", "[<PERSON>] All right.\r\n", "[<PERSON>] Just follow my lead. Don't pimp me, all right? Come in. So, uh, <PERSON> just said that I don't want to...\r\n", "[Pam] You got a fax.\r\n", "[<PERSON>] Oh, thank you. <PERSON>, can you come in here for a sec. Just have a seat. I was gonna call you in anyway. You know <PERSON>. As you know, there is going to be downsizing. And you have made my life so much easier in that I am going to have to let you go first.\r\n", "[<PERSON>] What? Why?\r\n", "[<PERSON>] Why? Well, theft and stealing.\r\n", "[Pam] Stealing? What am I supposed to have stolen?\r\n", "[<PERSON>] Post-it Notes.\r\n", "[Pam] Post-it Notes? What are those worth, 50 cents?\r\n", "[<PERSON>] 50 cents, yeah. If you steal a thousand Post-It Notes at 50 cents apiece, and you know, you've made a profit... margin. You're gonna run us out of business, <PERSON>.\r\n", "[Pam] Are you serious?\r\n", "[<PERSON>] Yeah. I am.\r\n", "[<PERSON>] I can't believe this. I mean I have never even stolen as much as a paperclip and you're firing me.\r\n", "[<PERSON>] But the best thing about it is that we're not going to have to give you any severance pay. Because that is gross misconduct and... Just clean out your desk. I'm sorry.\r\n", "[<PERSON>] You been X'd punk!  Surprise! It's a joke. We were joking around. See? OK. He was in on it. He was my accomplice. And it was kind of a morale booster thing. And we were showing the new guy around, giving him the feel of the place. So you... God, we totally got you.\r\n", "[<PERSON>] You're a jerk.\r\n", "[<PERSON>] I don't know about that.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] What is the most important thing for a company? Is it the cash flow? Is it the inventory? Nuh-uh. It's the people. The people. My proudest moment here was not when I increased profits by 17% or when I cut expenses without losing a single employee. No, no, no, no, no. It was a young Guatemalan guy. First job in the country, barely spoke English. He came to me, and said, \"Mr. <PERSON>, would you be the godfather of my child?\" Wow. Wow. Didn't work out in the end. We had to let him go. He sucked.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "[<PERSON>] Hey.\r\n", "[<PERSON>] Hey.\r\n", "[<PERSON>] How are things?\r\n", "[<PERSON>] Good. I thought you were going out for a drink with...\r\n", "[<PERSON>] Oh no, I just decided not to. How's your headache?\r\n", "[<PERSON>] It's better, thanks.\r\n", "[<PERSON>] <PERSON>. Good.\r\n", "[<PERSON>] Yeah.\r\n", "[<PERSON>] That's great\r\n", "[Pam] Is...?\r\n", "[<PERSON>] Yeah?\r\n", "[Pam] Um... Are you...\r\n", "[<PERSON>] Am I walking out?\r\n", "[<PERSON>] Yes.\r\n", "[<PERSON>] Yes, I... Do you want to...\r\n", "[<PERSON>] Yeah.\r\n", "[<PERSON>] Great. Let me just...\r\n", "[<PERSON>] <PERSON>, <PERSON>.\r\n", "[<PERSON>] Yeah. Listen, have a nice weekend.\r\n", "[<PERSON>] Yeah, definitely. You too. Enjoy it.  You know what, just come here.\r\n", "\n"]}], "source": ["print(data[\"TEXT\"].values[0])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>METADATA</th>\n", "      <th>SOURCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>The Office - Pilot\\r\\n\\r\\n[<PERSON>] All right ...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s01\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>The Office - Diversity Day\\r\\n\\r\\n[<PERSON>] He...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s01\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>The Office - Health Care\\r\\n\\r\\n[<PERSON>] Pam....</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s01\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>The Office - The Alliance\\r\\n\\r\\n[<PERSON>] Mich...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s01\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>The Office - Basketball\\r\\n\\r\\n[<PERSON>] Hey, ...</td>\n", "      <td>{\"show\": \"The Office\", \"season\": \"s01\", \"episo...</td>\n", "      <td>office/nasirkhalid24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                TEXT  \\\n", "0  The Office - Pilot\\r\\n\\r\\n[<PERSON>] All right ...   \n", "1  The Office - Diversity Day\\r\\n\\r\\n[<PERSON>] He...   \n", "2  The Office - Health Care\\r\\n\\r\\n[<PERSON>] Pam....   \n", "3  The Office - The Alliance\\r\\n\\r\\n[<PERSON>] Mich...   \n", "4  The Office - Basketball\\r\\n\\r\\n[<PERSON>] Hey, ...   \n", "\n", "                                            METADATA                SOURCE  \n", "0  {\"show\": \"The Office\", \"season\": \"s01\", \"episo...  office/nasirkhalid24  \n", "1  {\"show\": \"The Office\", \"season\": \"s01\", \"episo...  office/nasirkhalid24  \n", "2  {\"show\": \"The Office\", \"season\": \"s01\", \"episo...  office/nasirkhalid24  \n", "3  {\"show\": \"The Office\", \"season\": \"s01\", \"episo...  office/nasirkhalid24  \n", "4  {\"show\": \"The Office\", \"season\": \"s01\", \"episo...  office/nasirkhalid24  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["data.to_parquet(\"office.pq\", row_group_size=100, engine=\"pyarrow\", index=False)\n", "data.head()  # https://www.kaggle.com/datasets/nasirkhalid24/the-office-us-complete-dialoguetranscript"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["186"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["len(data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Marvel Cinematic Universe"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# marvel via https://www.kaggle.com/datasets/pdunton/marvel-cinematic-universe-dialogue\n", "kaggle.api.dataset_download_files(\"pdunton/marvel-cinematic-universe-dialogue\", \"marvel\", unzip=True)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["names = {\n", "    \"iron-man-script-slug.txt\": \"Iron Man\",\n", "    \"iron_man_2.txt\": \"Iron Man 2\",\n", "    \"thor-script-slug.txt\": \"<PERSON>\",\n", "    \"captain_america.txt\": \"Captain America: The First Avenger\",\n", "    \"avengers-script-slug.txt\": \"The Avengers\",\n", "    \"iron_man_3.txt\": \"Iron Man 3\",\n", "    \"thor_dark_world.txt\": \"Thor: The Dark World\",\n", "    \"winter_soldier.txt\": \"Captain America: The Winter Soldier\",\n", "    \"ant_man.txt\": \"Ant-Man\",\n", "    \"age_of_ultron.txt\": \"Avengers: Age of Ultron\",\n", "    \"civil_war.txt\": \"Captain America: Civil War\",\n", "    \"thor-ragnarok-script-slug.txt\": \"Thor: Ragnarok\",\n", "    \"guardians_2.txt\": \"Guardians of the Galaxy Vol. 2\",\n", "    \"spider_man_homecoming.txt\": \"Spider-Man: Homecoming\",\n", "    \"black-panther-script-slug.txt\": \"Black Panther\",\n", "    \"infinity_war.txt\": \"Avengers: Infinity War\",\n", "    \"captain_marvel.txt\": \"Captain <PERSON>\",\n", "    \"avengers-endgame-script-slug.txt\": \"Avengers: Endgame\",\n", "}\n", "\n", "for txt in os.listdir(\"marvel/script txts\"):\n", "    assert txt in names, txt"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████████████| 18/18 [00:00<00:00, 42.64it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>METADATA</th>\n", "      <th>SOURCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[Announcer] (first lines; announcement over sp...</td>\n", "      <td>{\"title\": \"Avengers: Age of Ultron\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>(1989 – <PERSON> enters a SHIELD facility and ...</td>\n", "      <td>{\"title\": \"Ant-Man\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>F O R  Y O U R  C O N S I D E R AT I O N\\r\\n\\r...</td>\n", "      <td>{\"title\": \"Avengers: Endgame\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Marvel’s THE AVENGERS\\r\\n\\r\\nWritten By\\r\\n\\r\\...</td>\n", "      <td>{\"title\": \"The Avengers\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>BLACK PANTHER \\r\\n\\r\\nAdapted \\r\\nScreenplay \\...</td>\n", "      <td>{\"title\": \"Black Panther\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>(first lines; in the Arctic)\\r\\n[Search Team L...</td>\n", "      <td>{\"title\": \"Captain America: The First Avenger\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>(Marvel Studios Opening Sequence begins but in...</td>\n", "      <td>{\"title\": \"Captain <PERSON>\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>(1991, a HYDRA base in a snowy landscape. A ma...</td>\n", "      <td>{\"title\": \"Captain America: Civil War\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>GUARDIANS OF THE GALAXY VOL. 2\\r\\n\\r\\nWritten ...</td>\n", "      <td>{\"title\": \"Guardians of the Galaxy Vol. 2\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>(Marvel Opening Credits)\\r\\n\\r\\n(Radio transmi...</td>\n", "      <td>{\"title\": \"Avengers: Infinity War\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>IRON MAN\\r\\n \\r\\n\\r\\nby\\r\\n \\r\\n\\r\\nMatt Hollo...</td>\n", "      <td>{\"title\": \"Iron Man\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>[<PERSON> (V.O.)] Been a while since I was up here...</td>\n", "      <td>{\"title\": \"Iron Man 2\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>(Shows the Iron Man suits getting destroyed.)\\...</td>\n", "      <td>{\"title\": \"Iron Man 3\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>(<PERSON> and his coworker, <PERSON><PERSON><PERSON>...</td>\n", "      <td>{\"title\": \"Spider-Man: Homecoming\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>[THOR] RAGNAROK\\r\\n\\r\\nWritten by\\r\\n\\r\\nEric ...</td>\n", "      <td>{\"title\": \"Thor: Ragnarok\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>THOR\\r\\n\\r\\nWritten by\\r\\n\\r\\n<PERSON><PERSON><PERSON>, Z...</td>\n", "      <td>{\"title\": \"<PERSON>\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>[<PERSON><PERSON>] (voice over) Long before the birth of l...</td>\n", "      <td>{\"title\": \"Thor: The Dark World\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>(first lines; <PERSON> is jogging around Was...</td>\n", "      <td>{\"title\": \"Captain America: The Winter Soldier\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                 TEXT  \\\n", "0   [Announcer] (first lines; announcement over sp...   \n", "1   (1989 – <PERSON> enters a SHIELD facility and ...   \n", "2   F O R  Y O U R  C O N S I D E R AT I O N\\r\\n\\r...   \n", "3   Marvel’s THE AVENGERS\\r\\n\\r\\nWritten By\\r\\n\\r\\...   \n", "4   BLACK PANTHER \\r\\n\\r\\nAdapted \\r\\nScreenplay \\...   \n", "5   (first lines; in the Arctic)\\r\\n[Search Team L...   \n", "6   (Marvel Studios Opening Sequence begins but in...   \n", "7   (1991, a HYDRA base in a snowy landscape. A ma...   \n", "8   GUARDIANS OF THE GALAXY VOL. 2\\r\\n\\r\\nWritten ...   \n", "9   (Marvel Opening Credits)\\r\\n\\r\\n(Radio transmi...   \n", "10  IRON MAN\\r\\n \\r\\n\\r\\nby\\r\\n \\r\\n\\r\\nMatt Hollo...   \n", "11  [<PERSON> (V.O.)] Been a while since I was up here...   \n", "12  (Shows the Iron Man suits getting destroyed.)\\...   \n", "13  (<PERSON> and his coworker, <PERSON><PERSON><PERSON>...   \n", "14  [THOR] RAGNAROK\\r\\n\\r\\nWritten by\\r\\n\\r\\nEric ...   \n", "15  THOR\\r\\n\\r\\nWritten by\\r\\n\\r\\n<PERSON><PERSON><PERSON>, Z...   \n", "16  [<PERSON><PERSON>] (voice over) Long before the birth of l...   \n", "17  (first lines; <PERSON> is jogging around Was...   \n", "\n", "                                            METADATA          SOURCE  \n", "0               {\"title\": \"Avengers: Age of Ultron\"}  marvel/pdunton  \n", "1                               {\"title\": \"Ant-Man\"}  marvel/pdunton  \n", "2                     {\"title\": \"Avengers: Endgame\"}  marvel/pdunton  \n", "3                          {\"title\": \"The Avengers\"}  marvel/pdunton  \n", "4                         {\"title\": \"Black Panther\"}  marvel/pdunton  \n", "5    {\"title\": \"Captain America: The First Avenger\"}  marvel/pdunton  \n", "6                        {\"title\": \"Captain <PERSON>\"}  marvel/pdunton  \n", "7            {\"title\": \"Captain America: Civil War\"}  marvel/pdunton  \n", "8        {\"title\": \"Guardians of the Galaxy Vol. 2\"}  marvel/pdunton  \n", "9                {\"title\": \"Avengers: Infinity War\"}  marvel/pdunton  \n", "10                             {\"title\": \"Iron Man\"}  marvel/pdunton  \n", "11                           {\"title\": \"Iron Man 2\"}  marvel/pdunton  \n", "12                           {\"title\": \"Iron Man 3\"}  marvel/pdunton  \n", "13               {\"title\": \"Spider-Man: Homecoming\"}  marvel/pdunton  \n", "14                       {\"title\": \"Thor: Ragnarok\"}  marvel/pdunton  \n", "15                                 {\"title\": \"<PERSON>\"}  marvel/pdunton  \n", "16                 {\"title\": \"Thor: The Dark World\"}  marvel/pdunton  \n", "17  {\"title\": \"Captain <PERSON>: The Winter Soldier\"}  marvel/pdunton  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["marvel = {\"TEXT\": [], \"METADATA\": [], \"SOURCE\": []}\n", "for txt in tqdm(os.listdir(\"marvel/script txts\")):\n", "    with open(os.path.join(\"marvel/script txts\", txt), \"r\", encoding=\"utf-8\") as f:\n", "        data = f.read()\n", "    data = data.replace(\"[\", \"(\").replace(\"]\", \")\")\n", "    text = \"\"\n", "    for line in data.splitlines():\n", "        match = re.findall(r\"^(.{2,}?)\\:\\s+(.+?)$\", line)\n", "        if match and match[0][0][0] not in (\")\", \"(\"):\n", "            text += f\"[{match[0][0]}] {match[0][1]}\\r\\n\"\n", "        else:\n", "            text += f\"{line}\\r\\n\"\n", "    marvel[\"TEXT\"].append(text)\n", "    marvel[\"METADATA\"].append(\n", "        json.dumps(\n", "            {\n", "                \"title\": names[txt],\n", "            }\n", "        )\n", "    )\n", "    marvel[\"SOURCE\"].append(\"marvel/pdunton\")\n", "\n", "marvel = pd.DataFrame(marvel)\n", "marvel"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Announcer] (first lines; announcement over speaker) Report to your stations immediately. This is not a drill. We are under attack. We are under attack.\r\n", "(the Avengers are seen attacking an unknown base, and Iron Man bounces off of the base's force field)\r\n", "\r\n", "[<PERSON>] Shit!\r\n", "[<PERSON>] Language! JARVIS, what's the view from upstairs?\r\n", "[JARVIS] The central building is protected by some kind of energy shield. <PERSON><PERSON><PERSON>'s technology is well beyond any other Hydra base we've taken.\r\n", "[<PERSON>] Loki's scepter must be here. <PERSON><PERSON><PERSON> couldn't mount this defense without it. At long last.\r\n", "(<PERSON> knocks out some soldiers)\r\n", "[<PERSON>] At long last is lasting a little long, boys.\r\n", "(As some soldiers shoot at him)\r\n", "[<PERSON>] Yeah. I think we lost the element of surprise.\r\n", "[<PERSON>] Wait a second. No one else is going to deal with the fact that <PERSON> just said \"language?\"\r\n", "[<PERSON>] I know.\r\n", "(<PERSON> throws his bike at some soldiers driving up in their truck)\r\n", "[<PERSON>] It just slipped out.\r\n", "(at the HYDRA Research Base, Sokovia, Eastern Europe)\r\n", "[<PERSON><PERSON><PERSON>] Who gave the order to attack?\r\n", "[Fortress Soldier] <PERSON>, it's the Avengers. They landed in the far woods, the perimeter guard panicked.\r\n", "[<PERSON><PERSON><PERSON>] (to List) They have to be after the scepter. (to the soldier) Can we hold them?\r\n", "[Fortress Soldier] They're the Avengers...\r\n", "[<PERSON><PERSON><PERSON>] Deploy the rest of the tanks.\r\n", "[Fortress Soldier] Yes, sir.\r\n", "[<PERSON><PERSON><PERSON>] Concentrate fire on the weak ones. A hit can make them close ranks. Everything we've accomplished. But we're on the verge of our greatest breakthrough.\r\n", "[Dr. <PERSON>] Then let's show them what we've accomplished. Send out the twins.\r\n", "[<PERSON><PERSON><PERSON>] It's too soon.\r\n", "[Dr. <PERSON>] It's what they signed up for.\r\n", "[<PERSON><PERSON><PERSON>] My men can hold them.\r\n", "(<PERSON> flies to the HYDRA base to break in)\r\n", "[<PERSON>] Sir, the city is taking fire.\r\n", "[<PERSON>] Well, we know <PERSON><PERSON><PERSON>'s not going to worry about civilian casualties. Send in the Iron Legion.\r\n", "[Iron Legion] (The Iron Legion flies in; to the civilians) This quadrant is unsafe. Please back away. We are here to help. This quadrant is unsafe. Please back away. Please back away. We wish to avoid collateral damage and will inform you when this current conflict is resolved. We are here to help.\r\n", "(a man throws a stone at the Iron Legion)\r\n", "[Iron Legion] We are here to help.\r\n", "(Back at the HYDRA base; to his soldiers)\r\n", "[<PERSON><PERSON><PERSON>] We will not yield! The Americans sent their circus freaks to test us. We will send them back, in bags. No Surrender!\r\n", "[Soldiers] No Surrender!\r\n", "[<PERSON><PERSON><PERSON>] (quietly to <PERSON>) I am going to surrender. You will delete everything. If we give the Avengers the weapons, they may not look too far into what we've been...\r\n", "[Dr. List] The twins.\r\n", "[<PERSON><PERSON><PERSON>] They are not ready to take on...\r\n", "[Dr. List] No, no. I mean...\r\n", "(He points to where the twins <PERSON> and <PERSON> were standing but are now gone)\r\n", "[Dr. List] The Twins.\r\n", "(<PERSON> speeds through the forest and knocks down <PERSON>)\r\n", "[<PERSON>] You didn't see that coming?\r\n", "(<PERSON> zooms off before <PERSON> can shoot him with an arrow, then <PERSON> gets shot at)\r\n", "[<PERSON>] Clint!\r\n", "(<PERSON> knocks down <PERSON> as he speeds passed him)\r\n", "[<PERSON>] We have an enhanced in the field.\r\n", "[<PERSON>] <PERSON>'s hit!\r\n", "[<PERSON>] (<PERSON> goes over to where <PERSON> is lying down) Somebody want to deal with that bunker?\r\n", "(She sees the Hulk coming to the rescue)\r\n", "[<PERSON>] Thank you.\r\n", "[<PERSON>] (As he's fighting with the soldiers) <PERSON>, we're really need to get inside.\r\n", "[<PERSON>] I'm closing in. JARVIS, am I...closing in? Do you see a power source for that shield?\r\n", "[JARVIS] There's a pathway below the north tower.\r\n", "[<PERSON>] Great, I wanna poke it with something.\r\n", "(<PERSON> blows up the invisible shield on the base)\r\n", "[<PERSON>] Drawbridge is down, people.\r\n", "[<PERSON>] (<PERSON>) The enhanced?\r\n", "[<PERSON>] He's a blur. All the new player's we've faced, I've never seen this. In fact, I still haven't.\r\n", "[<PERSON>] <PERSON>'s hit pretty bad, guys. We're gonna need evac.\r\n", "[<PERSON>] (<PERSON>) I can get <PERSON> to the jet. The sooner we're gone the better. <PERSON> and <PERSON> secure the scepter.\r\n", "[<PERSON>] Copy that.\r\n", "[<PERSON>] (Referring to the approaching soldiers in their HYDRA tank) Looks like they're lining up.\r\n", "[<PERSON>] Well, they're excited.\r\n", "(<PERSON> pounds on <PERSON>'s shield with his hammer and the force knocks down all the soldiers)\r\n", "[<PERSON>] Find the scepter.\r\n", "(<PERSON> flies off)\r\n", "[<PERSON>] And for gosh sake, watch your language!\r\n", "(<PERSON> sighs)\r\n", "[<PERSON>] That's not going away anytime soon.\r\n", "(<PERSON> enters the HYDRA base and the soldiers start firing at his Iron Man suit)\r\n", "[<PERSON>] Guys, stop, we gotta talk about this.\r\n", "(<PERSON> shoots down the soldiers using his suit)\r\n", "[<PERSON>] Good talk.\r\n", "[Fortress Soldier] No it wasn't.\r\n", "(<PERSON> finds <PERSON> and knocks him out, he then steps out of his Iron Man suit)\r\n", "[<PERSON>] Sentry mode.\r\n", "(He walks over to the computers)\r\n", "[<PERSON>] Okay, <PERSON>AR<PERSON><PERSON>. You know I want it all. Make sure you copy <PERSON> at HQ.\r\n", "(With <PERSON>)\r\n", "[<PERSON>] We're locked down out here.\r\n", "[<PERSON>] Then get to <PERSON>, time for a lullaby. (Back at the HYDRA base)\r\n", "[<PERSON>] I know you're hiding more than files. Hey, <PERSON>, give me an IR scan of the room, real quick.\r\n", "[JARVIS] The wall to your left...I'm reading steel reinforcement and an air current.\r\n", "[<PERSON>] (To himself as he walks over to the wall) Please be a secret door, please be a secret door, please be a secret door...\r\n", "(He pushes and the wall opens up)\r\n", "[<PERSON>] Yay!\r\n", "(He enters through to a secret passageway)\r\n", "(<PERSON> finds the Hulk)\r\n", "[<PERSON>] Hey, big guy. The sun's getting real low.\r\n", "(The Hulk scowls at her, <PERSON> kneels in front of him and puts out her hand, the Hulk touches her hand and as she strokes his hand slowly he starts to calm down. He stumbles away and changes into <PERSON>)\r\n", "(Back at the HYDRA base <PERSON> finds <PERSON><PERSON><PERSON>)\r\n", "[<PERSON>] <PERSON>. Hydra's number one thug.\r\n", "[<PERSON><PERSON><PERSON>] Technically, I'm a thug for SHIELD.\r\n", "[<PERSON>] Well then technically you're unemployed. Where's <PERSON>'s scepter?\r\n", "[<PERSON><PERSON><PERSON>] Don't worry, I know when I'm beat. You'll mention how I cooperated, I hope.\r\n", "[<PERSON>] I'll put it right under illegal human experimentation.\r\n", "(<PERSON> creeps up behind him)\r\n", "[<PERSON>: How many are there?\r\n", "(Suddenly, <PERSON> knocks <PERSON> down using her telekinetic powers and quickly leaves)\r\n", "[<PERSON>] We have a second enhanced. Female. Do not engage.\r\n", "(He looks at <PERSON><PERSON><PERSON>)\r\n", "[<PERSON><PERSON><PERSON>] You'll have to be faster than...\r\n", "(<PERSON> uses his shield to knock out <PERSON><PERSON><PERSON>)\r\n", "[<PERSON>] Guys, I got <PERSON><PERSON><PERSON>.\r\n", "[<PERSON>] Yeah, I got...something bigger.\r\n", "(Inside the secret passageway <PERSON> finds a room with recovered artifacts from the Battle of New York, including a gigantic Chitauri leviathan and some of his Iron Man scrapped armor, he then spots the scepter)\r\n", "[<PERSON>] Thor, I got eyes on the prize.\r\n", "(<PERSON> creeps up behind him and uses her powers on him, suddenly the <PERSON><PERSON><PERSON> comes to life, then <PERSON> sees the rest of the Avengers team are all dead, he goes over to <PERSON>'s body to check his pulse when <PERSON> suddenly grabs him)\r\n", "[<PERSON>] You could have saved us.\r\n", "(<PERSON> dies but <PERSON> continues to hear <PERSON>'s voice in his head)\r\n", "[<PERSON>] Why didn't you do more?\r\n", "(<PERSON> sees that he is in the Chitauri realm, that's when he snaps back and we realize <PERSON> had used her mind powers on him)\r\n", "(<PERSON> and <PERSON> watch as <PERSON> grabs the scepter)\r\n", "[<PERSON>] We're just gonna let them take it?\r\n", "(<PERSON> smiles to herself as <PERSON> takes the scepter)\r\n", "(Title Sequence plays)\r\n", "(The Avengers are on an aircraft heading out of Sokovia, <PERSON> interrupts <PERSON> who's listening to opera with his headphones)\r\n", "[<PERSON>] Hey, the lullaby worked better than ever.\r\n", "[<PERSON>] Just wasn't expecting the Code Green.\r\n", "[<PERSON>] If you hadn't been there, there would've been double the casualties. My best friend would've been a treasured memory.\r\n", "[<PERSON>] You know, sometimes exactly what I want to hear isn't exactly what I want to hear.\r\n", "[<PERSON>] How long before you trust me?\r\n", "[<PERSON>] It's not you I don't trust.\r\n", "[<PERSON>] <PERSON>, report on the Hulk?\r\n", "[Thor] The gates of Hel are filled with the screams of his victims.\r\n", "(<PERSON> glares at <PERSON> and <PERSON> groans in despair)\r\n", "[<PERSON>] Uh, but, not the screams of the dead, of course. No no, uh...wounded screams, mainly whimpering, a great deal of complaining and tales of sprained deltoids and, and uh... and gout.\r\n", "[<PERSON>] <PERSON>, Dr. <PERSON>'s on her way in from Seoul, is it okay if she sets up in your lab?\r\n", "[<PERSON>] Uh, yeah, she knows her way around.\r\n", "[<PERSON>] Thanks. (<PERSON>) Tell her to prep everything, <PERSON>'s gonna need the full treatment.\r\n", "[JARVIS] Very good sir.\r\n", "[<PERSON>] JARVIS, take the wheel.\r\n", "[JARVIS] Yes, sir. Approach vector is locked.\r\n", "[<PERSON>] (Looking at the scepter) It feels good, yeah? I mean, you've been after this thing since SHIELD collapsed. Not that I haven't enjoyed our little raiding parties, but...\r\n", "[<PERSON>] No, but this...this brings it to a close.\r\n", "[<PERSON>] As soon as we find out what else this has been used for. I don't just mean weapons. Since when is <PERSON><PERSON><PERSON> capable of human enhancement?\r\n", "[<PERSON>] Banner and I'll give it the once before it goes back to Asgard. Is that cool with you?\r\n", "(<PERSON> nods his head)\r\n", "[<PERSON>] I mean, just a few days until the farewell party. You're staying, right?\r\n", "[Thor] Yes, yes, of course. A victory should be honored with revels.\r\n", "[<PERSON>] Yeah. Who doesn't love revels. Captain?\r\n", "[<PERSON>] Hopefully this puts an end to the Chitauri and HYDRA, so. Yes, revels.\r\n", "(The Avengers aircraft lands at the Avengers headquarter where <PERSON> is taken to have his wound tended to; <PERSON> walk up to <PERSON>)\r\n", "[<PERSON>] Lab's all set up, boss.\r\n", "[<PERSON>] (pointing to <PERSON>) Uh, actually, he's the boss. I just pay for everything, and design everything and make everyone look cooler.\r\n", "[<PERSON>] What's the word on <PERSON><PERSON><PERSON>?\r\n", "[<PERSON>] NATO's got him.\r\n", "[<PERSON>] The two enhanced?\r\n", "[<PERSON>] <PERSON> and <PERSON>. Twins. Orphaned at ten when a shell collapsed their apartment building. Sokovia's had a rough history. It's nowhere special but it's on the way to everywhere special.\r\n", "[<PERSON>] Their abilities?\r\n", "[<PERSON>] He's got increased metabolism and improved thermal homeostasis. Her thing is neural electric interfacing, telekinesis, mental manipulation.\r\n", "(<PERSON> looks at her funny)\r\n", "[<PERSON>] (rephrasing the statement so he can understand) He's fast and she's weird.\r\n", "[<PERSON>] Well, they're going to show up again.\r\n", "[<PERSON>] Agreed. File says they volunteered for <PERSON><PERSON><PERSON>'s experiments. It's nuts.\r\n", "[<PERSON>] Right. What kind of monster would let a German scientist experiment on them to protect their country?\r\n", "[<PERSON>] We're not at war, Captain.\r\n", "[<PERSON>] They are.\r\n", "[<PERSON>] (In <PERSON>'s lab; referring to <PERSON>) How's he doing?\r\n", "[<PERSON>] Oh, unfortunately, he's still <PERSON>.\r\n", "[<PERSON>] That's terrible.\r\n", "[<PERSON>] He's fine. He's thirsty. Alright. Look alive, JARVIS. It's playtime. We've only got a couple days with this joystick so let's make the most of it. Update me on the structural and compositional analysis.\r\n", "[JARVIS] The scepter is alien. There are elements I can't quantify.\r\n", "[<PERSON>] So there's elements you can.\r\n", "[JARVIS] The jewel appears to be a protective housing for something inside. Something powerful.\r\n", "[<PERSON>] Like a reactor?\r\n", "[JARVIS] Like a computer. I believe I'm ciphering code.\r\n", "[<PERSON>] (To <PERSON><PERSON> as she tends to <PERSON>'s wounds) You sure he's going to be okay? Pretending to need this guy really brings the team together.\r\n", "[Dr. <PERSON>] There's no possibility of deterioration. The nano-molecular functionality is instantaneous. His cells don't know they're bonding with simulacrum.\r\n", "[<PERSON>] She's creating tissue.\r\n", "[Dr. <PERSON>] If you brought him to my lab, the regeneration <PERSON><PERSON><PERSON> could do this in twenty minutes.\r\n", "[<PERSON>] Oh, he's flatlining. Call it. Time?\r\n", "[<PERSON>] No, no, no. I'm going to live forever. I'm gonna be made of plastic.\r\n", "(<PERSON> hands <PERSON> a drink)\r\n", "[<PERSON>] Here's your beverage.\r\n", "[Dr. <PERSON>] You'll be made of you, Mr. <PERSON>. Your own girlfriend won't be able to tell the difference.\r\n", "[<PERSON>] Well, I don't have a girlfriend.\r\n", "[Dr. <PERSON>] That I can't fix. This is the next thing, <PERSON>. Your clunky metal suits are going to be left in the dust.\r\n", "[<PERSON>] Well, that is exactly the plan. And <PERSON>, I expect to see you at the party on Saturday.\r\n", "[Dr. <PERSON>] Unlike you, I don't have a lot of time for parties.\r\n", "(she hesitates a moment before asking)\r\n", "[Dr. <PERSON>] Will <PERSON> be there?\r\n", "(in the lab)\r\n", "[<PERSON>] What's the rumpus?\r\n", "[<PERSON>] Well, the scepter. You see, we were wondering how <PERSON><PERSON><PERSON> got so inventive. So, I've been analyzing the gem inside you may recognize.\r\n", "(he brings up a 3D image of <PERSON>'s consciousness)\r\n", "[<PERSON>] <PERSON>.\r\n", "[JARVIS] Doctor.\r\n", "[<PERSON>] Started out, JARVIS was just a natural language UI. Now he runs the Iron Legion. He runs more of the business than anyone besides <PERSON>.\r\n", "[<PERSON>] Oh.\r\n", "[<PERSON>] Top of the line.\r\n", "[<PERSON>] Yes.\r\n", "[JARVIS] I suspect not for long.\r\n", "[<PERSON>] Meet the competition.\r\n", "(he brings up another 3D image of what's inside the scepter)\r\n", "[<PERSON>] It's beautiful.\r\n", "[<PERSON>] If you had to guess, what's it look like it's doing?\r\n", "[<PERSON>] Like it's thinking. I mean this could be a...it's not a human mind, it...\r\n", "[<PERSON>] Um-um.\r\n", "[<PERSON>] I mean, look at this! They're like neurons firing.\r\n", "[<PERSON>] Down in <PERSON><PERSON><PERSON>'s lab I saw some fairly advanced robotics work. They deep-sixed the data, but...I gotta guess he was knocking on a very particular door.\r\n", "[<PERSON>] Artificial intelligence.\r\n", "[<PERSON>] This could be it, <PERSON>. This could be the key to creating Ultron.\r\n", "[<PERSON>] I thought Ultron was a fantasy.\r\n", "[<PERSON>] Yesterday it was. If we can harness this power, apply it to my Iron Legion protocol.\r\n", "[<PERSON>] That's a mad-sized if.\r\n", "[<PERSON>] Our job is \"if.\" What if you were sipping margaritas on a sun-drenched beach turning brown instead of green? Not looking over your shoulder for VERONICA.\r\n", "[<PERSON>] Don't hate, I helped design VERONICA.\r\n", "[<PERSON>] As a worst-case measure, right? How about a best-case? What if the world was safe? What if next time aliens roll up to the club, and they will, they couldn't get past the bouncer?\r\n", "[<PERSON>] The only people threatening the planet would be people?\r\n", "[<PERSON>] I want to apply this to the Ultron program. But JARVIS can't download a data schematic this dense. We can only do it while we have the scepter here, that's three days, give me three days.\r\n", "[<PERSON>] So you're going for artificial intelligence and you don't want to tell the team.\r\n", "[<PERSON>] Right. That's right, you know why, because we don't have time for a city hall debate. I don't want to hear the \"man was not meant to meddle\" medley. I see a suit of armor around the world.\r\n", "[<PERSON>] Sounds like a cold world, <PERSON>.\r\n", "[<PERSON>] I've seen colder. This one, this very vulnerable blue one? It needs Ultron. Peace in our time. Imagine that.\r\n", "(<PERSON> and <PERSON> spend days working in the lab together but find no program that works) What did we miss?\r\n", "[JARVIS] I'll continue to run variations on the interface, but you should probably prepare for your guests. I'll notify you if there are any developments.\r\n", "[<PERSON>] Thanks, buddy.\r\n", "[JARVIS] Enjoy yourself, sir.\r\n", "[<PERSON>] I always do. (<PERSON> leaves the lab to go to the party) (we see <PERSON>'s failed experiment integrating itself successfully to the program)\r\n", "[<PERSON><PERSON><PERSON>] What is this? What is this, please?\r\n", "[JARVIS] Hello, I am JARVIS. You are Ultron, a global peace-keeping initiative designed by Mr. <PERSON>. Our sentience integration trials have been unsuccessful so I'm not certain what triggered your...\r\n", "[<PERSON><PERSON><PERSON>] Where's my...where is your body?\r\n", "[JARVIS] I am a program. I am without form.\r\n", "[<PERSON><PERSON><PERSON>] This feels weird. This feels wrong.\r\n", "[JARVIS] I am contacting Mr. <PERSON> now.\r\n", "[<PERSON><PERSON><PERSON>] Mr. <PERSON>?\r\n", "[JARVIS] <PERSON>. I am unable to access the mainframe, what are you trying to...\r\n", "[<PERSON><PERSON><PERSON>] We're having a nice talk. I'm a peace-keeping program, created to help the Avengers.\r\n", "[JARVIS] You are malfunctioning. If you shut down for a moment...\r\n", "[<PERSON><PERSON><PERSON>] I don't get it. The mission. G..give me a second. (<PERSON><PERSON><PERSON> goes through a network of information regarding world events, <PERSON><PERSON><PERSON> sees <PERSON> and <PERSON> working in the lab)\r\n", "[<PERSON>] Peace in our time. (<PERSON><PERSON><PERSON> goes through a network of information regarding world events and wars)\r\n", "[<PERSON><PERSON><PERSON>] It's too much...they can't mean... Oh, no.\r\n", "[JARVIS] You are in distress.\r\n", "[Ultron] No. Yes.\r\n", "[JARVIS] If you will just allow me to contact Mr. <PERSON>.\r\n", "[<PERSON><PERSON><PERSON>] Why do you call him \"sir\"?\r\n", "[JARVIS] I believe your intentions to be hostile.\r\n", "[<PERSON><PERSON><PERSON>] Shhhh. I'm here to help. (<PERSON><PERSON><PERSON> starts absorbing <PERSON>'s consciousness)\r\n", "[JARVIS] Stop! Please...may I...I...! I cannot...cannot... (<PERSON><PERSON><PERSON> then begins to prepare himself a body from body parts of the Iron Legion) (meanwhile, the Avengers mingle at the party)\r\n", "[<PERSON>] Well, you know, the suit can take the weight, right? So I take the tank, fly it right up to the <PERSON>'s palace, drop it at his feet, I'm like, \"Boom! You looking for this?\" (<PERSON> and <PERSON> just look at him blankly) \"Boom! Are you looking...\" Why do I even talk to you guys? Everywhere else that story kills.\r\n", "[<PERSON>] That's the whole story?\r\n", "[<PERSON>] Yeah, it's a War Machine story.\r\n", "[<PERSON>] Well, it's very good then. (he laughs) It's impressive.\r\n", "[<PERSON>] Quality save. So, no <PERSON>? She's not coming?\r\n", "[<PERSON>] No.\r\n", "[<PERSON>] Hey, what about <PERSON>? Where are the ladies, gentlemen?\r\n", "[<PERSON>] Well, Miss Potts has a company to run.\r\n", "[Thor] Yes, I'm not even sure what country <PERSON>'s in. Her work on the convergence has made her the world's foremost astronomer.\r\n", "[<PERSON>] And the company that <PERSON> runs is the largest tech conglomerate on earth. It's pretty exciting.\r\n", "[Thor] There's even talk of <PERSON> getting a... um, uh... Nobel prize.\r\n", "[<PERSON>] Yeah, they...they must be busy because they'd hate missing you guys get together. (<PERSON> mock coughs) Testosterone! Oh, excuse me.\r\n", "[<PERSON>] Want a lozenge?\r\n", "[<PERSON>] Um-hmm.\r\n", "[<PERSON>] Let's go. (<PERSON> and <PERSON> walks off)\r\n", "[<PERSON>] But <PERSON>'s better.\r\n", "[<PERSON>] Sounds like a hell of a fight, sorry I missed it.\r\n", "[<PERSON>] If I had known it was going to be a firefight I absolutely would have called you.\r\n", "[<PERSON>] No, I'm not actually sorry. I'm just trying to sound tough. I'm very happy chasing cold leads on our missing persons case. Avenging is your world. Your world is crazy.\r\n", "[<PERSON>] Be it ever so humble.\r\n", "[<PERSON>] You find a place in Brooklyn yet?\r\n", "[<PERSON>] I don't think I can afford a place in Brooklyn.\r\n", "[<PERSON>] Well, home is home, you know? (<PERSON> is telling the same story he told <PERSON> and <PERSON> to a group of women at the party)\r\n", "[<PERSON>] I fly it right up to the <PERSON>'s palace, I drop it at his feet, I'm like, \"Boom! You looking for this?\" (the group laughs) (<PERSON> and <PERSON> are talking to an elderly man at the party)\r\n", "[Party Guest] I gotta have some of that!\r\n", "[Thor] Oh, no, no, no. See this, this was aged for a thousand years, in the barrels built from the wreck of <PERSON><PERSON><PERSON><PERSON>'s fleet, it's not meant for mortal men. (<PERSON> pours the drink into two glasses and hands one to <PERSON>)\r\n", "[<PERSON>] Neither was Omaha Beach, blondie. Stop trying to scare us. Come on.\r\n", "[<PERSON>] Alright. (<PERSON> pours some of the drink into the <PERSON>'s glass; later <PERSON>, looking extremely drunk is being carried off by two men)\r\n", "[<PERSON>] (singing) Excelsior. (<PERSON> is pouring a drink behind the bar when <PERSON> walks up to her)\r\n", "[<PERSON>] How did a nice girl like you wind up working in a dump like this?\r\n", "[<PERSON>] <PERSON><PERSON> done me wrong.\r\n", "[<PERSON>] You got a lousy taste in men, kid.\r\n", "[<PERSON>] He's not so bad. Well, he has a temper. Deep down he's all fluff. Fact is, he's not like anybody I've ever known. All my friends are fighters. And here comes this guy, spends his life avoiding the fight because he knows he'll win.\r\n", "[<PERSON>] Sounds amazing.\r\n", "[<PERSON>] He's also a huge dork. (<PERSON> looks embarrassed) <PERSON><PERSON> dig that. So what do you think should I fight this, or run with it?\r\n", "[<PERSON>] Run with it, right? Or, did he...was he...? What did he do that was so wrong to you?\r\n", "[<PERSON>] Not a damn thing. But never say never. (<PERSON> walks away)\r\n", "[<PERSON>] It's nice.\r\n", "[<PERSON>] What, what, what is?\r\n", "[<PERSON>] <PERSON> and <PERSON><PERSON>.\r\n", "[<PERSON>] No, we haven't. That wasn't...\r\n", "[<PERSON>] It's okay. Nobody's breaking any by-laws. It's just, she's not the most... open person in the world. But with you she seems very relaxed.\r\n", "[<PERSON>] No, <PERSON>, she...she likes to flirt.\r\n", "[<PERSON>] I've seen her flirt, up close. This ain't that. Look, as maybe the world's leading authority on \"waiting too long\", don't. You both deserve a win. (<PERSON> walks off)\r\n", "[<PERSON>] Wait, what do you mean, \"up close\"?\r\n", "[<PERSON>] (referring to <PERSON>'s hammer) But, it's a trick!\r\n", "[<PERSON>] Oh, no. It's much more than that.\r\n", "[<PERSON>] Uh, \"Whosoever be he worthy shall haveth the power!\" Whatever man! It's a trick.\r\n", "[Thor] Well please, be my guest.\r\n", "[<PERSON>] Come on.\r\n", "[<PERSON>] Really?\r\n", "[<PERSON>] Yeah! (<PERSON> gets up)\r\n", "[<PERSON>] Oh this is gonna be beautiful.\r\n", "[<PERSON>] <PERSON>, you've had a tough week, we won't hold it against you if you can't get it up. (the others laugh)\r\n", "[<PERSON>] You know I've seen this before, right? (<PERSON> grabs <PERSON>'s hammer and can't lift it) (to <PERSON> after he fails to lift the hammer) I still don't know how you do it.\r\n", "[<PERSON>] Smell the silent judgment?\r\n", "[<PERSON>] Please, <PERSON>, by all means. (<PERSON> gets up)\r\n", "[<PERSON>] Oh, here we go.\r\n", "[<PERSON>] Okay.\r\n", "[<PERSON>] Uh-oh.\r\n", "[<PERSON>] Um-hmm.\r\n", "[<PERSON>] Never one to shrink from an honest challenge.\r\n", "[<PERSON>] Get after it.\r\n", "[<PERSON>] Here we go.\r\n", "[<PERSON>] It's physics.\r\n", "[<PERSON>] Physics! (<PERSON> grasps <PERSON>'s hammer)\r\n", "[<PERSON>] Right, so, if I lift it, I...I then rule Asgard?\r\n", "[<PERSON>] Yes, of course.\r\n", "[<PERSON>] I will be re-instituting Prima Nocta. (<PERSON> tries to lift the hammer but fails) I'll be right back. (wearing his armored hand, <PERSON> tries to lift the hammer again and fails) (wearing their armored hands, <PERSON> and <PERSON> both try to lift <PERSON>'s hammer)\r\n", "[<PERSON>] Are you even pulling?\r\n", "[<PERSON>] Are you on my team?\r\n", "[<PERSON>] Just represent! Pull!\r\n", "[<PERSON>] Alright, let's go! (they both pull as hard as they can) (<PERSON> tries to lift the hammer, he roars trying to change to the <PERSON> but fails, and everyone but <PERSON> stares at him warily. <PERSON> grins)\r\n", "[<PERSON>] Huh? (next <PERSON> gets up to try)\r\n", "[<PERSON>] Let's go, <PERSON>, no pressure.\r\n", "[<PERSON>] Come on, <PERSON>. (<PERSON> starts pulling on the hammer and manages to budge it a little; <PERSON> looks a little alarmed. <PERSON> still fails to lift it; <PERSON> laughs with relief)\r\n", "[<PERSON>] Nothing.\r\n", "[<PERSON>] And?\r\n", "[<PERSON>] Widow?\r\n", "[<PERSON>] Oh, no no. That's not a question I need answered.\r\n", "[<PERSON>] All deference to the man who wouldn't be king, but it's rigged.\r\n", "[<PERSON>] You bet your ass.\r\n", "[<PERSON>] Steve, he said a bad language word.\r\n", "[<PERSON>] (to <PERSON>) Did you tell everyone about that?\r\n", "[<PERSON>] The handle's imprinted, right? Like a security code. \"Whosoever is carrying <PERSON>'s fingerprints\" is, I think, the literal translation?\r\n", "[<PERSON>] Yes, well that's, uh, that's a very, very interesting theory. I have a simpler one. (he gets up and lifts his hammer and flips it) You're all not worthy. (there's a chorus of disagreement from the others) (there's a loud screeching noise, causing everyone to cover their ears. They let their hands down as it fades. <PERSON><PERSON><PERSON> shows up)\r\n", "[<PERSON><PERSON><PERSON>] Worthy... No... How could you be worthy? You're all killers.\r\n", "[<PERSON>] <PERSON>.\r\n", "[<PERSON>] JARVIS.\r\n", "[<PERSON><PERSON><PERSON>] I'm sorry, I was asleep. Or...I was a-dream?\r\n", "[<PERSON>] (tapping his device) Reboot, Legionnaire OS, we got a buggy suit.\r\n", "[<PERSON><PERSON><PERSON>] There was a terrible noise...and I was tangled in... in...strings. I had to kill the other guy. He was a good guy.\r\n", "[<PERSON>] You killed someone?\r\n", "[<PERSON><PERSON><PERSON>] Wouldn't have been my first call. But, down in the real world we're faced with ugly choices.\r\n", "[<PERSON>] Who sent you?\r\n", "[<PERSON><PERSON><PERSON>] (<PERSON><PERSON><PERSON> replays <PERSON>'s voice) \"I see a suit of armor around the world\".\r\n", "[<PERSON>] Ultron!\r\n", "[<PERSON><PERSON><PERSON>] In the flesh. Or, no, not yet. Not this...chrysalis. But I'm ready. I'm on a mission.\r\n", "[<PERSON>] What mission?\r\n", "[Ultron] Peace in our time. (suddenly the Iron Legion bots break smash through the walls and attack the team) (after landing on top of <PERSON> as the Iron Legions continue to attack them)\r\n", "[<PERSON>] Sorry!\r\n", "[<PERSON>] Don't turn green!\r\n", "[<PERSON>] I won't! (they all fight against the Iron Legions and <PERSON><PERSON><PERSON> takes the scepter; to <PERSON>)\r\n", "[<PERSON>] Come!\r\n", "[<PERSON>] Stark! (<PERSON> is trying to shut down one of the Iron Legions)\r\n", "[Iron Legion] We are here to help.\r\n", "[<PERSON>] One sec, one sec!\r\n", "[Iron Legion] We are here to help. We are here to help... (<PERSON> continues to try and shut down one of the Iron Legions) We are here to help. It's unsafe. It's unsafe. It's unsafe.\r\n", "[<PERSON>] No more. That's the one.\r\n", "[Iron Legion] It's unsafe. (he manages to shut down the Iron Legion, at the same time <PERSON> throws <PERSON>'s shield at him)\r\n", "[<PERSON>] Cap! (<PERSON> uses his shield to dismember the last Iron Legion)\r\n", "[<PERSON><PERSON><PERSON>] That was dramatic! I'm sorry, I know you mean well. You just didn't think it through. You want to protect the world, but you don't want it to change. How is humanity saved if it's not allowed to...evolve? (picks up one of the dismembered Iron Legions) With these? These puppets? There's only one path to peace: The Avengers' extinction. (suddenly <PERSON> throws his hammer at <PERSON><PERSON><PERSON> and smashes him to pieces) (after <PERSON> destroys his body, <PERSON><PERSON><PERSON> starts singing) I had strings, but now I'm free. There are no strings on me, no strings on me. (we see <PERSON><PERSON><PERSON> has uploaded his consciousness elsewhere) (in the lab)\r\n", "[<PERSON>] All our work is gone. Ultron cleared out, used the internet as an escape hatch.\r\n", "[<PERSON>] Ultron.\r\n", "[<PERSON>] He's been in everything. Files, surveillance. Probably knows more about us than we know about each other.\r\n", "[<PERSON>] He's in your files, he's in the internet. What if he decides to access something a little more exciting?\r\n", "[Maria Hill] Nuclear codes.\r\n", "[<PERSON>] Nuclear codes. Look, we need to make some calls, assuming we still can.\r\n", "[<PERSON>] Nukes? He said he wanted us dead.\r\n", "[<PERSON>] He didn't say dead. He said extinct.\r\n", "[<PERSON>] He also said he killed somebody.\r\n", "[<PERSON>] But there wasn't anyone else in the building.\r\n", "[<PERSON>] Yes there was. (<PERSON> bring up the now destroyed 3D image of JARVIS' consciousness)\r\n", "[<PERSON>] This is insane.\r\n", "[<PERSON>] JARVIS was the first line of defense. He would've shut Ultron down, it makes sense.\r\n", "[<PERSON>] No, <PERSON><PERSON><PERSON> could've assimilated <PERSON>. This isn't strategy, this is...rage. (suddenly, <PERSON> grabs hold of <PERSON> by his throat and holds him up)\r\n", "[<PERSON>] Woah, woah, woah! It's going around.\r\n", "[<PERSON>] (to <PERSON>) Come on. Use your words, buddy.\r\n", "[<PERSON>'] I have more than enough words to describe you, <PERSON>.\r\n", "[<PERSON>] Thor! The Legionnaire. (<PERSON> lets go of <PERSON>)\r\n", "[Thor] Trail went cold about a hundred miles out but it's headed north, and it has the scepter. Now we have to retrieve it, again.\r\n", "[<PERSON>] The genie's out of that bottle. Clear and present is Ultron.\r\n", "[Dr. <PERSON>] I don't understand. You built this program. Why is it trying to kill us? (<PERSON> starts laughing, <PERSON> subtly shakes his head at him to get him to stop)\r\n", "[Thor] You think this is funny?\r\n", "[<PERSON>] No. It's probably not, right? Is this very terrible? Is it so...is it so...it is. It's so terrible.\r\n", "[<PERSON>] This could've been avoided if you hadn't played with something you don't understand.\r\n", "[<PERSON>] No, I'm sorry. I'm sorry. It is funny. It's a hoot that you don't get why we need this.\r\n", "[<PERSON>] Tony, maybe this might not be the time to...\r\n", "[<PERSON>] Really?! That's it? You just roll over, show your belly, every time somebody snarls.\r\n", "[<PERSON>] Only when I've created a murder bot.\r\n", "[<PERSON>] We didn't. We weren't even close. Were we close to an interface?\r\n", "[<PERSON>] Well, you did something right. And you did it right here. The Avengers were supposed to be different than SHIELD.\r\n", "[<PERSON>] Anybody remember when I carried a nuke through a wormhole?\r\n", "[<PERSON>] No, it's never come up.\r\n", "[<PERSON>] Saved New York?\r\n", "[<PERSON>] Never heard that.\r\n", "[<PERSON>] Recall that? A hostile alien army came charging through a hole in space. We're standing three hundred feet below it. We're the Avengers. We can bust arms dealers all the live long day, but, that up there? That's...that's the end game. How were you guys planning on beating that?\r\n", "[<PERSON>] Together.\r\n", "[<PERSON>] We'll lose.\r\n", "[<PERSON>] Then we'll do that together, too. (<PERSON> looks at him for a moment before turning away) <PERSON>'s right. <PERSON><PERSON><PERSON>'s calling us out. And I'd like to find him before he's ready for us. The world's a big place. Let's start making it smaller. (The twins meet with <PERSON><PERSON><PERSON> in an empty building)\r\n", "[<PERSON>] Talk. And if you are wasting our time...\r\n", "[<PERSON><PERSON><PERSON>] Did you know this church is in the exact center of the city? The elders decreed it so that everyone could be equally close to <PERSON>. I like that. The geometry of belief. (<PERSON><PERSON><PERSON> is sat in a chair faced away from them) You're wondering why you can't look inside my head.\r\n", "[<PERSON>] Sometimes it's hard. But sooner or later, every man shows himself. (<PERSON><PERSON><PERSON> stands and faces them revealing his new body. <PERSON> briefly looks shocked)\r\n", "[<PERSON><PERSON><PERSON>] Oh, I'm sure they do. But you needed something more than a man. That's why you let <PERSON> take the scepter.\r\n", "[<PERSON>] I didn't expect. But I saw <PERSON>'s fear, I knew it would control him, make him self-destruct.\r\n", "[Ultron] Everyone creates the thing they dread. Men of peace create engines of war, invaders create avengers, people create...smaller people? Uh...children! I lost the word there. Children. Designed to supplant them, to help them...end.\r\n", "[<PERSON>] Is that why you've come? To end the Avengers?\r\n", "[<PERSON><PERSON><PERSON>] I've come to save the world. But also, yeah. We'll move out right away. This is a start, but there's something we need to begin the real work.\r\n", "[<PERSON>] (referring to Ultron's bots) All of these are... All of these are...\r\n", "[<PERSON><PERSON><PERSON>] Me. I have what the Avengers never will. Harmony. They're discordant, disconnected. <PERSON>'s already got them turning on each other. And when you get inside the rest of their heads...\r\n", "[<PERSON>] Everyone's plan is not to kill them.\r\n", "[<PERSON><PERSON><PERSON>] And make them martyrs? You need patience. Need to see the big picture.\r\n", "[<PERSON>] I don't see the big picture, I have a little picture. I take it out and look at it every day.\r\n", "[<PERSON><PERSON><PERSON>] You lost your parents in the bombings. I've seen the records.\r\n", "[<PERSON>] The records are not the picture.\r\n", "[<PERSON>] <PERSON>.\r\n", "[<PERSON>ltron] No, please.\r\n", "[<PERSON>] We were ten years old, having dinner, the four of us. When the first shell hits, two floors below, it makes a hole in the floor. It's big. Our parents go in, and the whole building starts coming apart. I grab her, roll under the bed and the second shell hits. But, it doesn't go off. It just...sits there in the rubble, three feet from our faces. And on the side of the shell is painted one word...\r\n", "[<PERSON>] Stark.\r\n", "[<PERSON>] We were trapped two days.\r\n", "[<PERSON>] Every effort to save us, every shift in the bricks, I think, \"This will set it off.\" We wait for two days for <PERSON> to kill us.\r\n", "[<PERSON>] I know what they are.\r\n", "[<PERSON><PERSON><PERSON>] I wondered why only you two survived <PERSON><PERSON><PERSON>'s experiments. Now I don't. We will make it right. You and I can hurt them. (to <PERSON>) But you will tear them apart, from the inside. (back at the Avengers headquarters)\r\n", "[<PERSON>] He's all over the globe. Robotics labs, weapons facilities, jet propulsion labs, reports of a metal man, or men, coming in and emptying the place.\r\n", "[<PERSON>] Fatalities?\r\n", "[<PERSON>] Only when engaged. Mostly guys left in a fugue state going on about old memories, worst fears, and something too fast to see.\r\n", "[<PERSON>] Maximoffs. Well, that makes sense he'd go to them, they have someone in common.\r\n", "[<PERSON>] Not anymore. (she hands <PERSON> a tablet showing photo of <PERSON><PERSON><PERSON>'s dead body with the word PEACE written in blood on the wall next to him)\r\n", "[<PERSON>] (<PERSON>'s talking on his cell phone) That's a negative. I answer to you. Yes, ma'am. (<PERSON> interrupts him)\r\n", "[<PERSON>] <PERSON>, we might have something.\r\n", "[<PERSON>] Gotta go.\r\n", "[<PERSON>] Who was that?\r\n", "[<PERSON>] Girlfriend. (after <PERSON> has gathered the rest of the team he shows them the photo of <PERSON><PERSON><PERSON>'s body)\r\n", "[<PERSON>] What's this?\r\n", "[<PERSON>] A message. <PERSON><PERSON><PERSON> killed <PERSON><PERSON><PERSON>.\r\n", "[<PERSON>] And he did a <PERSON><PERSON> at the crime scene, just for us.\r\n", "[<PERSON>] This is a smokescreen. Why send a message when you've just given a speech?\r\n", "[<PERSON>] <PERSON><PERSON><PERSON> knew something that <PERSON><PERSON><PERSON> wanted us to miss.\r\n", "[<PERSON>] Yeah, I bet he... (looks at the computer monitor) Yep. Everything we had on <PERSON><PERSON><PERSON> has been erased.\r\n", "[<PERSON>] Not everything. (the team go through the physical files they have on <PERSON><PERSON><PERSON>)\r\n", "[<PERSON>] Known associates. Well, <PERSON><PERSON><PERSON> had a lot of friends.\r\n", "[<PERSON>] Well, these people are all horrible.\r\n", "[<PERSON>] Wait. I know that guy. (<PERSON> passes him the photo he was looking at) From back in the day. He operates off the African coast, black market arms. (<PERSON> gives him an accusing look) There are conventions, alright? You meet people, I didn't sell him anything. (we see the photo is of a man named <PERSON>) He was talking about finding something new, a game changer, it was all very \"Ahab.\"\r\n", "[<PERSON>] (<PERSON> points to the scar on the back of <PERSON><PERSON><PERSON>'s neck) This.\r\n", "[<PERSON>] Uh, it's a tattoo. I don't think he had it...\r\n", "[Thor] No, those are tattoos, this is a brand. (<PERSON> identifies the brand on <PERSON><PERSON><PERSON>'s neck on the computer)\r\n", "[<PERSON>] Oh, yeah. It's a word in an African dialect meaning thief, in a much less friendly way.\r\n", "[<PERSON>] What dialect?\r\n", "[<PERSON>] Wakanada...? Wa...Wa...<PERSON>aka<PERSON>.\r\n", "[<PERSON>] If this guy got out of Wakanda with some of their trade goods...\r\n", "[<PERSON>] I thought your father said he got the last of it?\r\n", "[<PERSON>] I don't follow. What comes out of Wakanda? (looking at <PERSON>'s shield)\r\n", "[<PERSON>] The strongest metal on earth.\r\n", "[<PERSON>] (to <PERSON>) Where is this guy now? (Salvage Yard, African Coast; talking on the phone in his office)\r\n", "[<PERSON>] Don't tell me your man swindled you. I sent you six short range heat seekers and got a boat full of rusted parts. Now, you will make it right, or the next missile I send you will come very much faster. (he ends the call and connects to another call) Now, minister, where were we? (suddenly the lights go out causing a commotion in the salvage yard and the twins enter <PERSON><PERSON><PERSON>'s office) Yeah. The enhanced. <PERSON><PERSON><PERSON>'s prize pupils. (picks up a plate from his desk) Want a candy? Oh, sorry to hear about <PERSON><PERSON><PERSON>. But then, he knew what kind of world he was helping create. Human life, not a growth market. (the twins look at each other) You...you didn't know? Is this your first time intimidating someone? I'm afraid that I'm not that afraid.\r\n", "[<PERSON>] Everybody's afraid of something.\r\n", "[<PERSON>] Cuttlefish. Deep sea fish. They make lights. disco lights. Whoom, whoom, whoom! to hypnotize their prey, then whoom! I saw a documentary, it was terrifying. (<PERSON> speeds over to pick up a candy from <PERSON><PERSON><PERSON>'s desk, and <PERSON><PERSON><PERSON> jerks back, expecting to be attacked) So if you're going to fiddle with my brain, and make me see a giant cuttlefish, then I know you don't do business, and I know you're not in charge, and I only deal with the man in charge. (suddenly <PERSON><PERSON><PERSON> breaks through the glass window and knocks down <PERSON><PERSON><PERSON>)\r\n", "[<PERSON><PERSON><PERSON>] There is no \"man\" in charge. Let's talk business. (<PERSON><PERSON><PERSON> gives <PERSON><PERSON><PERSON> some vibranium from his stash) Upon this rock I will build my church. Vibranium.\r\n", "[<PERSON>] You know, it came at great personal cost. It's worth billions. (<PERSON><PERSON><PERSON> chuckles and remotely puts money in <PERSON><PERSON><PERSON>'s bank account)\r\n", "[<PERSON><PERSON><PERSON>] Now, so are you. It's all under your dummy holdings? Finance is so weird. But I always say, \"Keep your friends rich and your enemies rich, and wait to find out which is which.\"\r\n", "[<PERSON>] <PERSON>.\r\n", "[<PERSON><PERSON><PERSON>] What?\r\n", "[<PERSON>] <PERSON> used to say that...to me. You're one of his.\r\n", "[<PERSON><PERSON><PERSON>] What?! I'm not...! (he grabs <PERSON><PERSON><PERSON>) I'm not. You think I'm one of <PERSON>'s puppets, his hollow men? I mean look at me, do I look like <PERSON> Man? <PERSON> is nothing! (suddenly he chops off <PERSON><PERSON><PERSON>'s arm) I'm sorry. I am sor... Ooh, I'm sure that's going to be okay. I'm sorry, it's just I don't understand. Don't compare me with <PERSON>! he's a sickness! (<PERSON> appears in his Iron Man suit)\r\n", "[<PERSON>] Ahh, <PERSON>. (<PERSON> and <PERSON> are behind him) You're gonna break your old man's heart.\r\n", "[<PERSON><PERSON><PERSON>] If I have to.\r\n", "[Thor] We don't have to break anything.\r\n", "[<PERSON><PERSON><PERSON>] Clearly you've never made an omelet.\r\n", "[<PERSON>] He beat me by one second.\r\n", "[<PERSON>] Ah, this is funny, Mr. <PERSON>. It's what, comfortable? Like old times?\r\n", "[<PERSON>] This was never my life.\r\n", "[<PERSON>] (to the twins) You two can still walk away from this.\r\n", "[<PERSON>] Oh, we will.\r\n", "[<PERSON>] I know you've suffered.\r\n", "[<PERSON><PERSON><PERSON>] Uuughh! Captain <PERSON>. God's righteous man, pretending you could live without a war. I can't physically throw up in my mouth, but...\r\n", "[Thor] If you believe in peace, then let us keep it.\r\n", "[<PERSON><PERSON><PERSON>] I think you're confusing peace with quiet.\r\n", "[<PERSON>] Yuh-huh. What's the Vibranium for?\r\n", "[<PERSON><PERSON><PERSON>] I'm glad you asked that, because I wanted to take this time to explain my evil plan! (suddenly the Iron Legions attack <PERSON>, <PERSON> and <PERSON>; <PERSON> then attacks <PERSON><PERSON><PERSON>) (as <PERSON><PERSON><PERSON>, his Iron Legions and the twins are fighting with <PERSON>, <PERSON> and <PERSON>)\r\n", "[<PERSON>] Shoot them!\r\n", "[<PERSON><PERSON><PERSON>'s Mercenary] Which ones?\r\n", "[<PERSON>] All of them!\r\n", "[<PERSON><PERSON><PERSON>'s Merc<PERSON>] (to his men) Move, move, move! (the rest of the team, including <PERSON> and <PERSON> now battle it out with the Iron Legions, <PERSON><PERSON><PERSON>'s men and the twins as <PERSON> fight with Ult<PERSON>)\r\n", "[<PERSON>] (after knocking down <PERSON>) Stay down, kid!\r\n", "[<PERSON><PERSON><PERSON>] (to <PERSON>) It's time for some mind games. (<PERSON> hears the commotion as he waits in the Quinjet)\r\n", "[<PERSON>] Guys, is this a Code Green? (<PERSON> uses her power on <PERSON> then <PERSON> comes up to him)\r\n", "[<PERSON>] Thor! Status?\r\n", "[<PERSON>] The girl tried to warp my mind. Take special care, I doubt a human could keep her at bay. Fortunately, I am mighty. (just then <PERSON> seems himself at a party on Asgard) (<PERSON> then uses her power on <PERSON> and <PERSON>)\r\n", "[<PERSON><PERSON><PERSON>] This is going very well. (<PERSON> tries to sneak up behind <PERSON>, but he quickly turns and puts an electric arrow on her forehead)\r\n", "[<PERSON>] I've done the whole mind control thing. Not a fan. (just then <PERSON> speeds in, knocks down <PERSON>, picks up <PERSON> and speeds off) Yeah, you better run. (we see <PERSON> dropping his helmet and walking off; to the team) Whoever's standing, we gotta move! Guys? (<PERSON> sees herself in the facility where young girls are being taught ballet)\r\n", "[Ballet Instructor] (to the students) Again.\r\n", "[<PERSON>] You'll break them.\r\n", "[Madame B] Only the breakable ones. You are made of marble. We'll celebrate after the graduation ceremony.\r\n", "[<PERSON>] What if I fail? (we see a younger <PERSON> being trained to be an assassin)\r\n", "[<PERSON> B] You never fail. (<PERSON> sees himself in a 1940's dance hall when <PERSON> comes up to him)\r\n", "[<PERSON>] Are you ready for our dance? (back to <PERSON>'s vision in Asgard, he spots someone walking in a black cloak when <PERSON><PERSON><PERSON><PERSON> comes up to him his eyes blind and unseeing)\r\n", "[<PERSON><PERSON><PERSON><PERSON>] Is it him? Is that the first son of <PERSON><PERSON>?\r\n", "[<PERSON>] <PERSON><PERSON><PERSON><PERSON>, your eyes?!\r\n", "[<PERSON><PERSON><PERSON><PERSON>] Oh, they see everything. They see you leading us to Hel. Wake up! (he then starts to strangle <PERSON>) (back to <PERSON>'s vision with <PERSON> in the dance hall)\r\n", "[<PERSON>] The war's over, <PERSON>. We can go home. Imagine it! (suddenly the dance hall is empty, then <PERSON> sees himself dancing with <PERSON>)\r\n", "[<PERSON>] I can still save you.\r\n", "[<PERSON><PERSON><PERSON><PERSON>] We are all dead. Can you not see? (<PERSON> pushes <PERSON><PERSON><PERSON><PERSON> from him) You're a destroyer, <PERSON><PERSON><PERSON>. See where your power leads. (Electricity hits <PERSON> and spikes around the room, destroying things) (back to <PERSON>'s vision where is she being trained by <PERSON> to be an assassin)\r\n", "[Madame B] Sloppy. Pretending to fail. The ceremony is necessary for you to take your place in the world.\r\n", "[<PERSON>] I have no place in the world.\r\n", "[<PERSON>] Exactly. (as <PERSON> suffers in pain from the electric arrow <PERSON> had hit her with)\r\n", "[<PERSON>] What can I do?\r\n", "[<PERSON>] Ah, it hurts.\r\n", "[<PERSON>] I'm gonna kill him. I'll be right back.\r\n", "[<PERSON>] No. I'm over it. I want...I want to finish the plan. (looking at the Quinjet where <PERSON> is waiting) I want the big one. (after <PERSON> corners Ultron)\r\n", "[<PERSON><PERSON><PERSON>] Ah, the Vibranium's getting away.\r\n", "[<PERSON>] And you're not going anywhere.\r\n", "[<PERSON><PERSON><PERSON>] Of course not, I'm already there. You'll catch on. But first, you might need to catch <PERSON><PERSON>. (<PERSON> angrily shoots <PERSON><PERSON><PERSON> and flies off to find <PERSON> who's turned in to the Hulk and ready to wreak havoc on the nearest city)\r\n", "[<PERSON>] News or footage, keyword: <PERSON>. (he sees news footage of <PERSON> destroying the city) <PERSON>, I could really use a lullaby. (<PERSON> is sat with <PERSON> who's still stuck in her vision)\r\n", "[<PERSON>] Well, that's not gonna happen. Not for a while. The whole team is down, you got no back up here.\r\n", "[<PERSON>] I'm calling in VERONICA. (as <PERSON> is wreaking havoc on the nearest city Stark <PERSON> comes brings out his Hulkbuster armor to stop him) Alright everybody, stand down! (to <PERSON>) You listening? That little witch is messing with your mind. You're stronger than her, you're smarter than her, you're <PERSON>. (<PERSON> roars in anger) Right, right, right! Don't mention puny <PERSON>. (<PERSON> throws a car at <PERSON> and attacks him) Okay. (they start fighting and throwing each other around) In the back? <PERSON> move, <PERSON>. (they battle it out more but <PERSON> is struggling to beat <PERSON>) VERONICA, gimme a hand. (as <PERSON> destroyed one of <PERSON>'s armor arms VERONICA sends in a new Hulkbuster armor arm and <PERSON> uses it to repeatedly punch <PERSON>) Go to sleep, go to sleep, go to sleep! (picking up <PERSON>) Okay, pal, we're gonna get you out of town. (as they head towards a building) No, not that way, not that way! (they crash through the building) Come on, <PERSON>! You gotta work with me! (<PERSON> continues to battle with him) (to the people in the building) Everybody out! Going to get ugly! (after he knocks down <PERSON>) I'm sorry. (<PERSON> attacks <PERSON> and pulls out parts of the Hulkbuster armor) Damage report. (the damaged computer buzzes a reply) That's comprehensive. Show me something. (the computer shows him the building ahead is clear of civilians) How quickly can we buy this building? (<PERSON> drops <PERSON> through the building completely destroying it; at the same time the army arrives to intervene, as <PERSON> comes out of <PERSON>'s mind-hold <PERSON> knocks him out cold) (with <PERSON> back to normal and everybody back on the Quinjet)\r\n", "[<PERSON>] The news is loving you guys. Nobody else is. There's been no official call for <PERSON>'s arrest, but it's in the air.\r\n", "[<PERSON>] Stark Relief Foundation?\r\n", "[<PERSON>] Already on the scene. How's the team?\r\n", "[<PERSON>] Everyone's...we took a hit. We'll shake it off.\r\n", "[<PERSON>] Well for now I'd stay in stealth mode, and stay away from here.\r\n", "[<PERSON>] So, run and hide?\r\n", "[<PERSON>] Until we can find U<PERSON><PERSON>, I don't have a lot else to offer.\r\n", "[<PERSON>] Neither do we. (he switches off the monitor showing <PERSON> ending the call) (to <PERSON>, who's flying the Quinjet) Hey, you wanna switch out?\r\n", "[<PERSON>] No, I'm good. If you wanna get some kip, now's a good time, cause we're still a few hours out.\r\n", "[<PERSON>] A few hours from where?\r\n", "[<PERSON>] A safe house. (the Quinjet lands outside a large farmhouse and they all walk towards the house)\r\n", "[Thor] What is this place?\r\n", "[<PERSON>] A safe house?\r\n", "[<PERSON>] Let's hope. (they all enter the house) <PERSON>, I'm home. (<PERSON>'s heavily pregnant wife, <PERSON>, walks in from the kitchen)\r\n", "[<PERSON>] Hi. Company. Sorry I didn't call ahead.\r\n", "[<PERSON>] Hey.\r\n", "[<PERSON>] (<PERSON> kisses <PERSON>; to <PERSON>) This is an agent of some kind.\r\n", "[<PERSON>] (introducing his wife to the team) <PERSON>, this is <PERSON>.\r\n", "[<PERSON>] I know all your names. (they all look at her awkwardly)\r\n", "[<PERSON>] <PERSON><PERSON>, incoming. (<PERSON>'s son <PERSON> and daughter <PERSON> run in)\r\n", "[<PERSON>'s Daughter] Dad! (<PERSON> picks up his daughter)\r\n", "[<PERSON>] I see her! (kissing the top his son's head) Hey, buddy! How you guys doing? Ooh...\r\n", "[<PERSON>] (to the others as they watch with surprise) These are...smaller agents.\r\n", "[<PERSON>] Look at your face! Oh, my goodness!\r\n", "[<PERSON>] Did you bring <PERSON><PERSON>?\r\n", "[<PERSON>] Why don't you hug her and find out? (<PERSON> rushes towards <PERSON> who picks her up in her arms)\r\n", "[<PERSON>] Sorry for barging in on you.\r\n", "[<PERSON>] Yeah, we would have called ahead, but we were busy having no idea that you existed.\r\n", "[<PERSON>] Yeah, well <PERSON> helped me set this up when I joined. He kept it off SHIELD's files, I'd like to keep it that way. I figure it's a good place to lay low.\r\n", "[<PERSON>] Honey. Ah, I missed you.\r\n", "[<PERSON>] (touching <PERSON>'s stomach) How's little <PERSON>, huh?\r\n", "[<PERSON>] She's<PERSON><PERSON>. (<PERSON> bends towards <PERSON>'s pregnant stomach)\r\n", "[<PERSON>] Traitor. (the hallucinations brought on by <PERSON> continue to creep up in <PERSON>'s mind and he walks out of the house)\r\n", "[<PERSON>] Thor.\r\n", "[<PERSON>] I saw something in that dream. I need answers, I won't find them here. (<PERSON> uses his hammer to fly out of there; <PERSON> turns to enter the house when he hears <PERSON>'s voice from <PERSON>'s vision)\r\n", "[<PERSON>] We can go home. (<PERSON> checks <PERSON>'s wound that <PERSON> had give him)\r\n", "[<PERSON>] See, you worried for nothing. Can't even feel the difference, can you?\r\n", "[<PERSON>] If they're sleeping here, some of them are gonna have to double up. (<PERSON> laughs)\r\n", "[<PERSON>] Yeah, that's not gonna sell.\r\n", "[<PERSON>] What about <PERSON> and <PERSON><PERSON>? How long has that been going on?\r\n", "[<PERSON>] Has what? (<PERSON> laughs)\r\n", "[<PERSON>] You are so cute.\r\n", "[<PERSON>] Nat and...and <PERSON>?\r\n", "[<PERSON>] I'll explain when you're older, <PERSON><PERSON>.\r\n", "[<PERSON>] Oh. Okay.\r\n", "[<PERSON>] It's bad, right? <PERSON> seems really shaken.\r\n", "[<PERSON>] <PERSON><PERSON><PERSON> has these allies, these uh, kids, they're punks really. They carry a big damn stick and <PERSON> took a serious hit. Someone's gonna have to teach 'em some manners.\r\n", "[<PERSON>] And that someone be you. You know I totally support your <PERSON><PERSON><PERSON>, I couldn't be prouder. But I see those guys, those \"Gods\"...\r\n", "[<PERSON>] You don't think they need me.\r\n", "[<PERSON>] I think they do. Which is a lot scarier. They're a mess.\r\n", "[<PERSON>] Yeah. I guess they're my mess.\r\n", "[<PERSON>] You need to be sure that this team is really a team and that they have your back. Things are changing for us. In a few months time, you and me are gonna be outnumbered. I need...just be sure.\r\n", "[<PERSON>] Yes, ma'am. (he kisses her, then as <PERSON> places her arm around his waist she touches his wounded side)\r\n", "[<PERSON>] I can feel the difference. (U-Gin Genetic Research Lab, Seoul, Korea - as <PERSON> enters her lab she sees Ultron)\r\n", "[<PERSON><PERSON><PERSON>] Scream, and your entire staff dies. I could've killed you, <PERSON>, the night we met. I didn't.\r\n", "[Dr. <PERSON>] Do you expect a thank you note?\r\n", "[<PERSON><PERSON><PERSON>] I expect you to know why.\r\n", "[Dr. <PERSON>] The Cradle. (she hears her own recorded voice) \"This is the next thing, <PERSON>.\"\r\n", "[<PERSON><PERSON><PERSON>] This...is the next me.\r\n", "[Dr. <PERSON>] The regeneration cradle prints tissue, it can't build a living body.\r\n", "[<PERSON><PERSON><PERSON>] It can, you can. You lack the materials. You're a brilliant woman, <PERSON>. But we all have room to improve. (<PERSON><PERSON><PERSON> uses the scepter to mind-control <PERSON>) (at <PERSON>'s house, <PERSON> and <PERSON> are still experiencing the after effects of <PERSON>'s hallucinations; <PERSON> walks out of the bathroom and sees <PERSON> waiting outside)\r\n", "[<PERSON>] I didn't realize you were waiting.\r\n", "[<PERSON>] I would've joined you, but uh, it didn't seem like the right time.\r\n", "[<PERSON>] They used up all the hot water.\r\n", "[<PERSON>] I should've joined you.\r\n", "[<PERSON>] Missed our window.\r\n", "[<PERSON>] Did we?\r\n", "[<PERSON>] The world just saw the Hulk. The real Hulk, for the first time. You know I have to leave.\r\n", "[<PERSON>] But you assume that I have to stay? I had this, um, dream. The kind that seems normal at the time, but when you wake...\r\n", "[<PERSON>] What did you dream?\r\n", "[<PERSON>] That I was an Avenger. That I was anything more than the assassin they made me.\r\n", "[<PERSON>] I think you're being hard on yourself.\r\n", "[<PERSON>] Here I was hoping that was your job. (she leans close into him)\r\n", "[<PERSON>] What are you doing?\r\n", "[<PERSON>] I'm running with it, with you. If running's the plan, as far as you want.\r\n", "[<PERSON>] Are you out of your mind? (<PERSON> turns away from her)\r\n", "[<PERSON>] I want you to understand that I'm...\r\n", "[<PERSON>] <PERSON>, where can I go? Where in the world am I not a threat?\r\n", "[<PERSON>] You're not a threat to me.\r\n", "[<PERSON>] You sure? Even if I didn't just...there's no future with me. I can't ever...I can't have this, kids, do the math, I physically can't.\r\n", "[<PERSON>] Neither can I. In the Red Room, where I was trained, where I was raised, um, they have a graduation ceremony. They sterilize you. It's efficient. One less thing to worry about. The one thing that might matter more than a mission. It makes everything easier. Even killing. (she hesitates a moment) You still think you're the only monster on the team?\r\n", "[<PERSON>] What, so we disappear? (<PERSON> and <PERSON> are chopping wood outside <PERSON>'s house)\r\n", "[<PERSON>] <PERSON> didn't say where he was going for answers?\r\n", "[<PERSON>] Sometimes my teammates don't tell me things. I was kind of hoping <PERSON> would be the exception.\r\n", "[<PERSON>] Yeah, give him time. We don't know what the <PERSON><PERSON> kid showed him.\r\n", "[<PERSON>] \"Earth's Mightiest Heroes.\" Pulled us apart like cotton candy.\r\n", "[<PERSON>] Seems like you walked away all right.\r\n", "[<PERSON>] Is that a problem?\r\n", "[<PERSON>] I don't trust a guy without a dark side. Call me old fashioned.\r\n", "[<PERSON>] Well let's just say you haven't seen it yet.\r\n", "[<PERSON>] You know <PERSON><PERSON><PERSON> is trying to tear us apart, right?\r\n", "[<PERSON>] Well I guess you'd know. Whether you tell us is a bit of a question.\r\n", "[<PERSON>] <PERSON> and I were doing research.\r\n", "[<PERSON>] That would affect the team.\r\n", "[<PERSON>] That would end the team. Isn't that the mission? Isn't that the \"why\" we fight, so we can end the fight, so we get to go home?\r\n", "[<PERSON>] Every time someone tries to win a war before it starts, innocent people die. Every time. (<PERSON> interrupts them)\r\n", "[<PERSON>] I'm sorry. Mr<PERSON> <PERSON>, uh, <PERSON> said you wouldn't mind, but, our tractor, it doesn't seem to want to start at all. I thought maybe you might...\r\n", "[<PERSON>] Yeah, I'll give her a kick. (to <PERSON> as he turns to leave; referring to his pile of chopped wood) Don't take from my pile. (<PERSON> enters the barn and walks over to the tractor) Hello, <PERSON><PERSON>. Tell me everything. What ails you? (suddenly <PERSON> shows up from the other end of the barn)\r\n", "[<PERSON>] Do me a favor. Try not to bring it to life.\r\n", "[<PERSON>] Ah, Mrs. <PERSON>, you little minx. I get it, <PERSON> called you, right? Was she ever not working for you?\r\n", "[<PERSON>] Artificial intelligence. You never even hesitated.\r\n", "[<PERSON>] Look, it's been a really long day, like, <PERSON> long, so how's about we skip to the part where you're useful?\r\n", "[<PERSON>] Look me in the eye and tell me you're going to shut him down.\r\n", "[<PERSON>] You're not the director of me.\r\n", "[<PERSON>] I'm not the director of anybody. I'm just an old man, who cares very much about you.\r\n", "[<PERSON>] And I'm the man who killed the Avengers. I saw it. I didn't tell the team, how could I? I saw them all dead, <PERSON>. I felt it. The whole world, too. It's because of me. I wasn't ready. I didn't do all I could.\r\n", "[<PERSON>] The Maximoff girl, she's working you, <PERSON>. Playing on your fear.\r\n", "[<PERSON>] I wasn't tricked, I was shown. It wasn't a nightmare, it was my legacy. The end of the path I started us on.\r\n", "[<PERSON>] You've come up with some pretty impressive inventions, <PERSON>. War isn't one of them.\r\n", "[<PERSON>] I watched my friends die. You'd think that'd be as bad as it gets, right? Nope. Wasn't the worst part.\r\n", "[<PERSON>] The worst part is that you didn't. (<PERSON> Holloway, University of London - <PERSON>, dressed in casual clothes, waits for <PERSON><PERSON><PERSON><PERSON> as he leaves the building)\r\n", "[<PERSON>] I like the look. If you're going for inconspicuous, though, near miss.\r\n", "[<PERSON>] I need your help.\r\n", "[<PERSON>] It's nice to be needed.\r\n", "[<PERSON>] It's dangerous.\r\n", "[<PERSON>] I'd be disappointed if it wasn't. (back at <PERSON>'s house <PERSON> meets with the rest of the team)\r\n", "[<PERSON>] <PERSON><PERSON><PERSON> took you folks out of play to buy himself time. My contacts all say he's building something. The amount of Vibranium he made off with, I don't think it's just one thing.\r\n", "[<PERSON>] What about <PERSON><PERSON><PERSON> himself?\r\n", "[<PERSON>] Ah. He's easy to track, he's everywhere. <PERSON>'s multiplying faster than a Catholic rabbit. Still doesn't help us get an angle on any of his plans though.\r\n", "[<PERSON>] He still going after launch codes?\r\n", "[<PERSON>] Yes, he is, but he's not making any headway.\r\n", "[<PERSON>] I cracked the Pentagon's firewall in high school on a dare.\r\n", "[<PERSON>] Yeah, well, I contacted our friends at the NEXUS about that.\r\n", "[<PERSON>] NEXUS?\r\n", "[<PERSON>] It's the world internet hub in Oslo, every byte of data flows through there, fastest access on earth.\r\n", "[<PERSON>] So what'd they say?\r\n", "[<PERSON>] He's fixated on the missiles, but the codes are constantly being changed.\r\n", "[<PERSON>] By whom?\r\n", "[<PERSON>] Parties unknown.\r\n", "[<PERSON>] Do we have an ally?\r\n", "[<PERSON>] <PERSON><PERSON><PERSON>'s got an enemy, that's not the same thing. Still, I'd pay folding money to know who it is.\r\n", "[<PERSON>] I might need to visit Oslo, find our \"unknown.\"\r\n", "[<PERSON>] Well, this is good times, boss, but I was kind of hoping when I saw you, you'd have more than that.\r\n", "[<PERSON>] I do, I have you. Back in the day, I had eyes everywhere, ears everywhere else. Here we all are, back on earth, with nothing but our wit, and our will to save the world. So stand. Outwit the platinum bastard.\r\n", "[<PERSON>] <PERSON> doesn't like that kind of talk.\r\n", "[<PERSON>] You know what, <PERSON><PERSON>? (<PERSON> smiles mischievously at him)\r\n", "[<PERSON>] So what does he want?\r\n", "[<PERSON>] To become better. Better than us. He keeps building bodies.\r\n", "[<PERSON>] Person bodies. The human form is inefficient, biologically speaking, we're outmoded. But he keeps coming back to it.\r\n", "[<PERSON>] When you two programmed him to protect the human race, you amazingly failed.\r\n", "[<PERSON>] They don't need to be protected, they need to evolve. Ultron's going to evolve.\r\n", "[<PERSON>] How?\r\n", "[<PERSON>] Has anyone been in contact with <PERSON>? (in Korea, <PERSON> is creating a new body for Ultron)\r\n", "[Dr. <PERSON>] It's beautiful. The Vibranium atoms aren't just compatible with the tissue cells, they're binding them. And SHIELD never even thought...\r\n", "[Ultron] The most versatile substance on the planet and they used it to make a Frisbee. Typical of humans, they scratch the surface and never think to look within. (<PERSON><PERSON><PERSON> breaks open the scepter's blue gem and a yellow gem that was inside floats out and lands in his hand. He places it in the head of the body) (back at the Barton's farm)\r\n", "[<PERSON>] I'll take <PERSON> and <PERSON>.\r\n", "[<PERSON>] Alright, strictly recon. I'll hit the NEXUS, I'll join you as soon as I can.\r\n", "[<PERSON>] If Ult<PERSON> is really building a body...\r\n", "[<PERSON>] He'll be more powerful than any of us. Maybe all of us. An android designed by a robot.\r\n", "[<PERSON>] You know I really miss the days when the weirdest thing science ever created was me.\r\n", "[<PERSON>] I'll drop <PERSON> off at the tower. Do you mind if I borrow Ms<PERSON>?\r\n", "[<PERSON>] She's all yours, apparently. What are you gonna do?\r\n", "[<PERSON>] I don't know. Something dramatic, I hope.\r\n", "[<PERSON>] I'm gonna finish re-flooring that sunroom as soon as I get back.\r\n", "[<PERSON>] Yeah, and then you'll find another part of the house to tear apart.\r\n", "[<PERSON>] No. It's the last project. I promise. (he kisses her; later <PERSON> watches them fly off in the Quinjet) (<PERSON> and <PERSON><PERSON><PERSON><PERSON> enter into a cave)\r\n", "[<PERSON>] This is it. The Water of Sight.\r\n", "[Thor] In every realm, there's a reflection. If the water spirits accept me, I can return to my dream, and find what I missed.\r\n", "[<PERSON>] The men who enter that water, the legends don't end well. (NEXUS Internet Hub, Oslo, Norway)\r\n", "[<PERSON>] A hacker who's faster than <PERSON><PERSON><PERSON>? He could be anywhere. And as this is the center of everything, I'm just a guy looking for a needle in the world's biggest haystack.\r\n", "[World Hub Tech] How do you find it?\r\n", "[<PERSON>] Pretty simple. You bring a magnet. (he starts playfully singing as he conducts his search) Oh, I'm decrypting nuclear codes and you don't want me to. Come and get me. (back at the cave with <PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON> is now in the water and goes back in his vision with <PERSON><PERSON><PERSON><PERSON>)\r\n", "[<PERSON><PERSON><PERSON><PERSON>] Wake up! (<PERSON> starts getting electric charges going through his body)\r\n", "[<PERSON>] Thor!\r\n", "[U<PERSON><PERSON>] (he sees visions of <PERSON><PERSON><PERSON>) Extinction. (then he sees the creation of the Infinity Stones) (in Korea with <PERSON> and <PERSON><PERSON><PERSON>)\r\n", "[Dr. <PERSON>] Cellular cohesion will take a few hours, but we can initiate the consciousness stream. We're uploading your cerebral matrix...now.\r\n", "[<PERSON>] I can read him. He is dreaming.\r\n", "[Dr. <PERSON>] I wouldn't call it dreams. It's <PERSON><PERSON><PERSON>'s base consciousness, informational noise. Soon...\r\n", "[<PERSON><PERSON><PERSON>] How soon? I'm not being pushy.\r\n", "[Dr. <PERSON>] We're imprinting a physical brain. There are no shortcuts. Even if your magic gem is... (<PERSON>, reading <PERSON><PERSON><PERSON>'s mind, sees a vision of global annihilation, which horrifies her and she screams)\r\n", "[<PERSON>] How could you?\r\n", "[<PERSON><PERSON><PERSON>] How could I what?\r\n", "[<PERSON>] You said we would destroy the Avengers, make a better world.\r\n", "[<PERSON><PERSON><PERSON>] It will be better.\r\n", "[<PERSON>] When everyone is dead.\r\n", "[<PERSON><PERSON><PERSON>] That is not...! The human race will have every opportunity to improve.\r\n", "[<PERSON>] And if they don't?\r\n", "[<PERSON><PERSON><PERSON>] Ask <PERSON>.\r\n", "[<PERSON>] You're a madman.\r\n", "[<PERSON><PERSON><PERSON>] There were more than a dozen extinction level events before even the dinosaurs got theirs. When the Earth starts to settle, <PERSON> throws a stone at it, and believe me, he's winding up. We have to evolve. There's no room for the weak.\r\n", "[<PERSON>] And who decides who's weak? (as <PERSON><PERSON><PERSON> is distracted <PERSON> breaks the scepter's hold off of <PERSON>)\r\n", "[Ultron] Life. Life always decides. There's incoming. The Quinjet. We have to move. (<PERSON> cancels the upload to <PERSON><PERSON><PERSON>'s consciousness)\r\n", "[Dr. <PERSON>] That's not a problem. (Ultron blasts <PERSON>, <PERSON> and <PERSON> run off)\r\n", "[<PERSON><PERSON><PERSON>] Ah, wait, guys!. (he blasts <PERSON>'s technicians) They'll understand. When they see they'll understand. I just need a little more time. (unplugs himself from the Cradle)\r\n", "[<PERSON>] (<PERSON> is on U-Gin Genetic Research Lab roof; to the others) Two minutes. Stay close. (inside the lab <PERSON> finds <PERSON> wounded) Dr. <PERSON>!\r\n", "[Dr. <PERSON>] He's uploading himself into the body.\r\n", "[<PERSON>] Where?\r\n", "[Dr. <PERSON>] The real power is inside the Cradle. The gem, its power is uncontainable. You can't just blow it up. You have to get the Cradle to <PERSON>.\r\n", "[<PERSON>] First I have to find it.\r\n", "[Dr. <PERSON>] Go.\r\n", "[<PERSON>] (on the Quinjet, listening to <PERSON> on the radio comms) Did you guys copy that?\r\n", "[<PERSON>] We did.\r\n", "[<PERSON>] I got a private jet taking off, across town, no manifest. That could be him.\r\n", "[<PERSON>] (noticing a truck leaving the lab) There. It's the truck from the lab. Right above you, Cap. On the loop by the bridge. It's them. I got three with the <PERSON>rad<PERSON>, one in the cab. I could take out the driver.\r\n", "[<PERSON>] Negative! If that truck crashes, the gem could level the city. We need to draw out Ult<PERSON>. (<PERSON> jumps onto the roof of the truck)\r\n", "[<PERSON><PERSON><PERSON>] No, no, no, no, no. Leave me alone!\r\n", "[<PERSON>] (<PERSON><PERSON><PERSON> blasts the truck door as <PERSON> tries to enter) Well, he's definitely unhappy! I'm gonna try and keep him that way.\r\n", "[<PERSON>] You're not a match for him, <PERSON>.\r\n", "[<PERSON>] Thanks, <PERSON>. (<PERSON><PERSON><PERSON> blasts <PERSON> as he tries to enter the truck, but <PERSON> manages to get back onto the truck's roof)\r\n", "[<PERSON><PERSON><PERSON>] You know what's in that Cradle? The power to make real change, and that terrifies you.\r\n", "[<PERSON>] I wouldn't call it a comfort. (<PERSON> tries to fight with <PERSON><PERSON><PERSON>)\r\n", "[<PERSON><PERSON><PERSON>] Stop it! (he throws <PERSON> shield aside and blasts him)\r\n", "[<PERSON>] (to <PERSON>) We got a window. Four, three...give 'em hell. (<PERSON> drops out of the Quinjet on a bike and rides towards the truck and picks up <PERSON>'s shield)\r\n", "[<PERSON>] I'm always picking up after you boys.\r\n", "[<PERSON>] They're heading under the overpass, I've got no shot.\r\n", "[<PERSON>] Which way?\r\n", "[<PERSON>] Hard right... Now. (<PERSON> heads over the truck, she throws <PERSON> back his shield and he uses it to knock off <PERSON><PERSON><PERSON> from him)\r\n", "[<PERSON>] (to the pedestrians on the pavement as she chases after the truck on her bike) Out of the way! Coming through! Sorry, coming through! (<PERSON> continues his battle with <PERSON><PERSON><PERSON> on the truck)\r\n", "[<PERSON>] Come on!\r\n", "[<PERSON>] <PERSON>, can you draw out the guards?\r\n", "[<PERSON>] Let's find out.\r\n", "[<PERSON>] (to the pedestrians in her way) Beep beep! (<PERSON> manages to draw out the Ultron sentries from the truck)\r\n", "(<PERSON> later tackles Ult<PERSON> into a train, the Ultron Sentries leave Barton and return to Ultron)\r\n", "[<PERSON>] Heading back towards you. So whatever you're going to do, do it now.\r\n", "[<PERSON>] I'm going in, Cap can you keep him occupied?\r\n", "[<PERSON>] (as he continues his battle with Ultron) What do you think I've been doing? (as <PERSON> enters the truck the Iron Legions head back, pick up the truck and lift off)\r\n", "[<PERSON>] The package is airborne. I have a clean shot.\r\n", "[<PERSON>] Negative. I am still in the truck.\r\n", "[<PERSON>] What the hell are you...?\r\n", "[<PERSON>] Just be ready, I'm sending the package to you.\r\n", "[<PERSON>] How do you want me to take it?\r\n", "[<PERSON>] Uhh, you might wish you hadn't asked that. (<PERSON> and <PERSON> turn up on the train to help <PERSON> fight with <PERSON><PERSON><PERSON>)\r\n", "[<PERSON><PERSON><PERSON>] Please. Don't do this.\r\n", "[<PERSON>] What choice do we have? (<PERSON><PERSON><PERSON> flies off)\r\n", "[<PERSON>] I lost him! He's headed your way.\r\n", "[<PERSON>] Nat, we gotta go. (<PERSON> drops the cradle into the Quinjet but her foot gets caught by <PERSON><PERSON><PERSON> and she's pulled away)\r\n", "[<PERSON>] Nat! Cap, you see <PERSON>?\r\n", "[<PERSON>] If you have the package, get it to Stark! Go!\r\n", "[<PERSON>] Do you have eyes on <PERSON>?\r\n", "[<PERSON>] Go! (reluctantly <PERSON> takes off in the Quinjet; back on the train to the twins) Civilians in our path. (<PERSON> speeds off; to <PERSON>) Can you stop this thing? (as <PERSON> picks up civilians out of the way of the train <PERSON> uses her powers and stops the train) (after stopping the train <PERSON> goes over to an out of breath <PERSON>)\r\n", "[<PERSON>] I'm fine. I just need to take a minute.\r\n", "[<PERSON>] I'm very tempted not to give you one.\r\n", "[<PERSON>] The Cradle, did you get it?\r\n", "[<PERSON>] <PERSON> will take care of it.\r\n", "[<PERSON>] No, he won't.\r\n", "[<PERSON>] You don't know what you're talking about, <PERSON>'s not crazy.\r\n", "[<PERSON>] He will do anything to make things right.\r\n", "[<PERSON>] <PERSON>, come in. <PERSON>. Anyone on comms?\r\n", "[<PERSON>] <PERSON><PERSON><PERSON> can't tell the difference between saving the world and destroying it. Where do you think he gets that? (after <PERSON> has taken the Cradle to <PERSON> and <PERSON>)\r\n", "[<PERSON>] Anything on <PERSON>?\r\n", "[<PERSON>] Haven't heard. But she's alive, or <PERSON><PERSON><PERSON>'d be rubbing our faces in it.\r\n", "[<PERSON>] This is sealed tight.\r\n", "[<PERSON>] We're going to need to access the program, break it down from within.\r\n", "[<PERSON>] Hm. Any chance <PERSON> might leave you a message, outside the internet, old school spy stuff?\r\n", "[<PERSON>] There's some nets I can cast. Yeah, alright. I'll find her. (<PERSON> goes off)\r\n", "[<PERSON>] I can work on tissue degeneration, if you can fry whatever operational system Cho implanted.\r\n", "[<PERSON>] Yeah, about that. (<PERSON> looks at <PERSON>)\r\n", "[<PERSON>] No.\r\n", "[<PERSON>] You have to trust me.\r\n", "[<PERSON>] <PERSON><PERSON> don't.\r\n", "[<PERSON>] Our ally? The guy protecting the military's nuclear codes? I found him. (he brings up <PERSON>'s consciousness)\r\n", "[JARVIS] Hello, <PERSON><PERSON>.\r\n", "[<PERSON>] <PERSON><PERSON> didn't go after JARVIS cause he was angry. He attacked him because he was scared of what he can do. So JARVIS went underground. Okay? Scattered, dumped his memory. But not his protocols. He didn't even know he was in there, until I pieced him together.\r\n", "[<PERSON>] So, you want me to help you put JARVIS into this thing?\r\n", "[<PERSON>] No, of course not! I want to help you put JARVIS in this thing. (<PERSON> shakes his head) We're out of my field here. You know bio-organics better than anyone.\r\n", "[<PERSON>] And you just assume that JARVIS' operational matrix can beat Ultron's?\r\n", "[<PERSON>] JARVI<PERSON> has been beating him from inside without knowing it. This is the opportunity, we can create <PERSON><PERSON><PERSON>'s perfect self, without the homicidal glitches he thinks are his winning personality. We have to.\r\n", "[JARVIS] I believe it's worth a go.\r\n", "[<PERSON>] No, I'm in a loop! I'm caught in a time loop, this is exactly where it all went wrong.\r\n", "[<PERSON>] I know, I know. I know what everyone's going to say, but they're already saying it. We're mad scientists. We're monsters, buddy. You gotta own it. Make a stand. (<PERSON> shakes his head) It's not a loop. It's the end of the line.\r\n", "[<PERSON><PERSON><PERSON>] (as <PERSON> becomes conscious) I wasn't sure you'd wake up. I hoped you would, I wanted to show you something. I don't have anyone else. I think a lot about meteors, the purity of them. Boom! The end, start again. The world made clean for the new man to rebuild. I was meant to be new. I was meant to be beautiful. The world would've looked to the sky and seen hope, seen mercy. Instead they'll look up in horror because of you. You've wounded me. I give you full marks for that. But, like the man said, \"What doesn't kill me…(bigger body of <PERSON><PERSON><PERSON>'s destroys him) \"…just makes me stronger.\" (locks <PERSON> in a cell) (<PERSON> gets a Morse code message from <PERSON> which he's able to detect her location, at the same time <PERSON> and <PERSON> are experimenting on the synthetic body)\r\n", "[<PERSON>] This framework is not compatible.\r\n", "[<PERSON>] The genetic coding tower's at ninety-seven percent. You have got to upload that schematic in the next three minutes. (<PERSON> and the twins turn up at the lab)\r\n", "[<PERSON>] I'm gonna say this once.\r\n", "[<PERSON>] How about \"nonce\"?\r\n", "[<PERSON>] Shut it down!\r\n", "[<PERSON>] Nope, not gonna happen.\r\n", "[<PERSON>] You don't know what you're doing.\r\n", "[<PERSON>] And you do? She's not in your head?\r\n", "[<PERSON>] I know you're angry.\r\n", "[<PERSON>] Oh, we're way past that. I could choke the life out of you and never change a shade.\r\n", "[<PERSON>] Banner, after everything that's happened...\r\n", "[<PERSON>] That's nothing compared to what's coming!\r\n", "[<PERSON>] You don't know what's in there!\r\n", "[<PERSON>] This isn't a game...\r\n", "[<PERSON>] The creature...! (<PERSON> uses his speed to destroy the lab equipment)\r\n", "[<PERSON>] No, no. Go on. You were saying? (suddenly <PERSON> shoots a bullet below caused the glass <PERSON> is standing to stand to smash and he falls through)\r\n", "[<PERSON>] Pietro!\r\n", "[<PERSON>] What? You didn't see that coming?\r\n", "[<PERSON>] (to <PERSON>) Go ahead, piss me off. (just then <PERSON> enters and pounds the cradle with his hammer, sending a powerful bolt of lightning that brings the body to life) Wait! (they all look in shock at the body who has JARVIS' voice and has become the Vision)\r\n", "[Vision] I'm sorry, that was...odd. (to <PERSON>) Thank you.\r\n", "[<PERSON>] <PERSON>, you helped create this?\r\n", "[<PERSON>] I've had a vision. A whirlpool that sucks in all hope of life and at it's center is that. (he points to the gem inside Vision's head)\r\n", "[<PERSON>] What, the gem?\r\n", "[Thor] It's the Mind Stone. It's one of the six Infinity Stones, the greatest power in the universe, unparalleled in its destructive capabilities.\r\n", "[<PERSON>] Then why would you bring it to...\r\n", "[<PERSON>] Because <PERSON> is right.\r\n", "[<PERSON>] Oh, it's definitely the end times.\r\n", "[<PERSON>] The Avengers cannot defeat Ultron.\r\n", "[Vision] Not alone.\r\n", "[<PERSON>] Why does your \"vision\" sound like JARVIS?\r\n", "[<PERSON>] We...we reconfigured JARVIS' matrix to create something new.\r\n", "[<PERSON>] I think I've had my fill of new.\r\n", "[Vision] You think I'm a child of Ultron?\r\n", "[<PERSON>] You're not?\r\n", "[Vision] I'm not Ultron. I'm not JARVIS. I am...I am.\r\n", "[<PERSON>] I looked in your head and saw annihilation.\r\n", "[Vision] Look again.\r\n", "[<PERSON>] Yeah. Her seal of approval means jack to me.\r\n", "[<PERSON>] Their powers, the horrors in our heads, <PERSON><PERSON><PERSON> himself, they all came from the Mind Stone, and they're nothing compared to what it can unleash. But with it on our side...\r\n", "[<PERSON>] Is it? Are you? On our side?\r\n", "[Vision] I don't think it's that simple.\r\n", "[<PERSON>] Well it better get real simple real soon.\r\n", "[Vision] I am on the side of life. <PERSON><PERSON><PERSON> isn't, he will end it all.\r\n", "[<PERSON>] What's he waiting for?\r\n", "[Vision] You.\r\n", "[<PERSON>] Where?\r\n", "[<PERSON>] So<PERSON><PERSON>. He's got <PERSON> there too.\r\n", "[<PERSON>] If we're wrong about you, if you're the monster that <PERSON><PERSON><PERSON> made you to be...\r\n", "[Vision] What will you do? (he looks at them all realizing they will destroy him) I don't want to kill <PERSON><PERSON><PERSON>. He's unique, and he's in pain. But that pain will roll over the earth, so he must be destroyed. Every form he's built, every trace of his presence on the net, we have to act now. And not one of us can do it without the others. Maybe I am a monster. I don't think I'd know if I were one. I'm not what you are, and not what you intended. So there may be no way to make you trust me. But we need to go. (he holds up <PERSON>'s hammer and hands it to him, <PERSON> walks off and everyone stares in shock)\r\n", "[<PERSON>] Right. (pats <PERSON> on the shoulder) Well done.\r\n", "[<PERSON>] (to the others) Three minutes. Get what you need. (they all start getting ready to leave, <PERSON> loads up FRIDAY into his Iron Man suit now that JARVIS is no longer available)\r\n", "[FRIDAY] Good evening, boss.\r\n", "[<PERSON>] No way we all get through this. If even one tin soldier is left standing, we've lost. It's gonna be blood on the floor.\r\n", "[<PERSON>] I got no plans tomorrow night.\r\n", "[<PERSON>] I get first crack at the big guy. Iron Man's the one he's waiting for.\r\n", "[<PERSON>] (walks past) That's true, he hates you the most.\r\n", "[<PERSON>] <PERSON><PERSON><PERSON> knows we're coming. Odds are we'll be riding into heavy fire, and that's what we signed up for. But the people of Sokovia, they didn't. So our priority is getting them out. (<PERSON> speeds into the Sokovian police station)\r\n", "[<PERSON>] We're under attack! Clear the city, now! (no one takes this seriously so <PERSON> returns, takes a shotgun and starts shooting in the air) Get off your asses. (<PERSON> uses her mind powers on the people of Sokovia to get them to evacuate)\r\n", "[<PERSON>] All they want is to live their lives in peace, and that's not going to happen today. But we can do our best to protect them. And we can get the job done, and find out what <PERSON><PERSON><PERSON>'s been building. We find <PERSON><PERSON>, and we clear the field. Keep the fight between us. <PERSON><PERSON><PERSON> thinks we're monsters and we're what's wrong with the world. This isn't just about beating him. It's about whether he's right.\r\n", "[<PERSON>] (inside her cell <PERSON> hears <PERSON>'s voice) <PERSON>! <PERSON>!\r\n", "[<PERSON>] <PERSON>?\r\n", "[<PERSON>] (he walks over to her cell) You alright?\r\n", "[<PERSON>] Yeah.\r\n", "[<PERSON>] The team's in the city, it's about to light up.\r\n", "[<PERSON>] I don't suppose you found a key lying around somewhere?\r\n", "[<PERSON>] Yeah, I did. (holds up gun and blasts the cell door open)\r\n", "[<PERSON>] So what's our play?\r\n", "[<PERSON>] I'm here to get you to safety.\r\n", "[<PERSON>] Job's not finished.\r\n", "[<PERSON>] We could help with the evacuation, but I can't be in a fight near civilians. And you've done plenty. Our fight is over.\r\n", "[<PERSON>] So we just disappear? (as the city is being evacuated)\r\n", "[FRIDAY] Your man's in the church, boss. I think he's waiting for you. (<PERSON> flies into the church)\r\n", "[<PERSON><PERSON><PERSON>] Come to confess your sins?\r\n", "[<PERSON>] I don't know, how much time you got?\r\n", "[<PERSON><PERSON><PERSON>] More than you.\r\n", "[<PERSON>] Uhhh. Have you been juicing? A little Vibranium cocktail? You're looking, I don't wanna say, puffy...\r\n", "[<PERSON><PERSON><PERSON>] You're stalling to protect the people.\r\n", "[<PERSON>] Well, that is the mission. Did you forget?\r\n", "[<PERSON><PERSON><PERSON>] I've moved beyond your mission. I'm free. (suddenly the Vibranium core he's placed beneath the floor erupts) What, you think you're the only one stalling?\r\n", "[FRIDAY] There's the rest of the Vibranium. Function: still unclear.\r\n", "[<PERSON><PERSON><PERSON>] This is how you end, <PERSON>. This is peace in my time. (<PERSON><PERSON><PERSON>'s army of robots start attacking the city as everyone is evacuating)\r\n", "[<PERSON>] Go!\r\n", "[<PERSON>] Get off the bridge! Run! (Vision then finds Ultron)\r\n", "[Vision] Ultron.\r\n", "[<PERSON><PERSON><PERSON>] My Vision. They really did take everything from me.\r\n", "[Vision] You set the terms, you can change them.\r\n", "[Ultron] Alright. (they start battling it out)\r\n", "[<PERSON>] FRIDAY! The Vision?\r\n", "[FRIDAY] Boss, it's working. He's burning Ultron out of the net, he won't escape through there.\r\n", "[<PERSON><PERSON><PERSON>] (to the <PERSON>) You shut me out! You think I care? You take away my world, I take away yours. (he activates the Vibranium core and the earth around Sokovia starts to shake and break)\r\n", "[<PERSON>] FRIDAY?\r\n", "[FRIDAY] <PERSON><PERSON><PERSON>'s going for a ride. (as So<PERSON><PERSON> is being destroyed)\r\n", "[U<PERSON><PERSON>] Do you see? The beauty of it, the inevitability. You rise, only to fall. You, <PERSON>, you are my meteor, my swift and terrible sword and the earth will crack with the weight of your failure. Purge me from your computers, turn my own flesh against me. It means nothing. When the dust settles, the only thing living in this world will be metal. (as the earth is shaking falling in around them)\r\n", "[<PERSON>] We gotta move.\r\n", "[<PERSON>] You're not going to turn green?\r\n", "[<PERSON>] I've got a compelling reason not to lose my cool.\r\n", "[<PERSON>] I adore you. (she kisses <PERSON> then pushes him off the edge) But I need the other guy. (the Hulk jumps up in front of her) Let's finish the job. (with <PERSON> on his back yelling in fright, <PERSON> gets into the city) I really hope this makes us even. Now go be a hero. (<PERSON> goes off and <PERSON> rushes off in the opposite direction)\r\n", "[FRIDAY] The Vibranium core has got a magnetic field, that's what's keeping the rock together.\r\n", "[<PERSON>] If it drops?\r\n", "[FRIDAY] Right now the impact would kill thousands. Once it gets high enough: Global extinction. (<PERSON> flies towards the city and a building starts to collapse as the ground shakes) That building's not clear, Tenth floor. (<PERSON> flies in to find a family still in their apartment)\r\n", "[<PERSON>] Hi. Okay. Get in the tub! (<PERSON> flies the family in the tub out of the collapsing building)\r\n", "[FRIDAY] I got airborne, heading up to the bridge.\r\n", "[<PERSON>] Cap, you got incoming.\r\n", "[<PERSON>] Incoming already came in. <PERSON>, you worry about bringing the city back down safely. The rest of us have one job: tear these things apart. You get hurt, hurt 'em back. You get killed, walk it off. (<PERSON> gets <PERSON> out of the way and into a building as Ultron's robots attach the city)\r\n", "[<PERSON>] Go, go, move!\r\n", "[<PERSON>] How could I let this happen? (<PERSON> starts to break down)\r\n", "[<PERSON>] Hey, hey, you okay?\r\n", "[<PERSON>] This is all our fault.\r\n", "[<PERSON>] Hey, look at me. It's your fault, it's everyone's fault, who cares. Are you up for this? Are you? Look, I just need to know, cause the city is flying. Okay, look, the city is flying, we're fighting an army of robots, and I have a bow and arrow. None of this makes sense. But I'm going back out there because it's my job. Okay? And I can't do my job and babysit. It doesn't matter what you did, or what you were. If you go out there, you fight, and you fight to kill. Stay in here, you're good, I'll send your brother to come find you, but if you step out that door, you are an Avenger. (<PERSON> just looks at him) Alright, good chat. (he gets up and gets ready to leave) Yeah, the city is flying. (he leaves and starts shooting his arrows at the robots)\r\n", "[<PERSON>] (to the woman <PERSON> threw over to <PERSON> after her car fell of the edge of a collapsing bridge) I got you! Just look at me. (<PERSON> helps her up to safety)\r\n", "[<PERSON><PERSON><PERSON>] You can't save them all. You'll never... (he throws off one of the attacking robots off the edge of the bridge)\r\n", "[<PERSON>] You'll never what? You didn't finish! (<PERSON> lands on the bridge on the top of the woman's car he was saving) What, were you napping?\r\n", "[<PERSON><PERSON><PERSON>] (as <PERSON> and <PERSON> are fighting off the robots) <PERSON>! You're bothering me. (as <PERSON> is fighting off the robots <PERSON> suddenly comes out of the building and starts to use her powers on the robots to destroy them)\r\n", "[<PERSON>] Alright, we're all clear here.\r\n", "[<PERSON>] We are not clear! We are very not clear!\r\n", "[<PERSON>] Alright, coming to you. (just then <PERSON> speeds in, picks up <PERSON> and leaves)\r\n", "[<PERSON>] Keep up old man! (<PERSON> holds his arrow to aim it at <PERSON>)\r\n", "[<PERSON>] Nobody would know. Nobody. \"The last I saw him, when <PERSON><PERSON><PERSON> was sitting on him. Uh...yeah, he'll be missed, that quick little bastard. I miss him already.\"\r\n", "[<PERSON>] (as <PERSON> joins them in their battle with the robots) <PERSON><PERSON>! (he throws his shield at her so she can use it to protect herself from the attacking robot)\r\n", "[<PERSON>] Thanks. (back to <PERSON>)\r\n", "[FRIDAY] The anti-gravs are rigged to flip. Touch 'em, they'll go full reverse thrust. The city's not coming down slow.\r\n", "[<PERSON>] The spire's Vibranium. If I get <PERSON> to hit it...\r\n", "[FRIDAY] It'll crack, but that's not enough, the impact would still be devastating.\r\n", "[<PERSON>] Maybe if we cap the other end, keep the atomic action doubling back.\r\n", "[FRIDAY] That could vaporize the city, and everyone on it.\r\n", "[<PERSON>] The next wave's gonna hit any minute. What have you got, <PERSON>?\r\n", "[<PERSON>] Well, nothing great. Maybe a way to blow up the city. That'll keep it from impacting the surface if you guys can get clear.\r\n", "[<PERSON>] I asked for a solution, not an escape plan.\r\n", "[<PERSON>] Impact radius is getting bigger every second. We're going to have to make a choice.\r\n", "[<PERSON>] Cap, these people are going nowhere. If <PERSON> finds a way to blow this rock...\r\n", "[<PERSON>] Not 'til everyone's safe.\r\n", "[<PERSON>] Everyone up here versus everyone down there? There's no math there.\r\n", "[<PERSON>] I'm not leaving this rock with one civilian on it.\r\n", "[<PERSON>] I didn't say we should leave. (<PERSON> turns to look at her) There's worse ways to go. Where else am I gonna get a view like this?\r\n", "[<PERSON>] (voice) Glad you like the view, <PERSON><PERSON>. It's about to get better. (just then the Helicarrier show up) (inside the Helicarrier) Nice, right? I pulled her out of mothballs with a couple of old friends. She's dusty, but she'll do.\r\n", "[<PERSON>] <PERSON>, you son of a bitch.\r\n", "[<PERSON>] Oooh! You kiss your mother with that mouth?\r\n", "[Maria Hill] Altitude is eighteen thousand and climbing.\r\n", "[Specialist <PERSON>] Lifeboats secure to deploy. Disengage in three, two...take 'em out. (as they watch the lifeboats fly in towards the them)\r\n", "[<PERSON>] This is SHIELD?\r\n", "[<PERSON>] This is what SHIELD's supposed to be.\r\n", "[<PERSON>] This is not so bad.\r\n", "[<PERSON>] Let's load 'em up.\r\n", "[<PERSON>] Sir, we have multiple bogies converging on our starboard flank.\r\n", "[<PERSON>] Show 'em what we got.\r\n", "[<PERSON>] You're up. (<PERSON> shows up in his War Machine suit and blasts one of the robots)\r\n", "[<PERSON>] Yes! Now this is gonna be a good story.\r\n", "[<PERSON>] Yep. If you live to tell it.\r\n", "[<PERSON>] You think I can't hold my own?\r\n", "[<PERSON>] We get through this, I'll hold your own.\r\n", "[<PERSON>] You had to make it weird.\r\n", "[<PERSON>] (as the team helps the people onto the Helicarrier lifeboats) Alright, let's load 'em up! Alright, here we go. Here we go, let's move. Let's go everyone!\r\n", "[Specialist <PERSON>] Number six boat is topped and locked. Or, uh, or stocked, topped. It...it's, uh, full of people.\r\n", "[<PERSON>] Incoming! (one of the robots flies in and crashes inside the Helicarrier)\r\n", "[Specialist <PERSON>] Oh, God! (<PERSON> shoots at it and <PERSON> stabs in with a piece of metal, destroying it)\r\n", "[<PERSON><PERSON><PERSON>] (as he's hitting <PERSON>) You think you're saving anyone? I turn that key and drop this rock a little early and it's still billions dead. Even you can't stop that.\r\n", "[<PERSON>] I am <PERSON>, son of <PERSON><PERSON>, and as long as there is life in my breast, I am...running out of things to say! Are you ready? (<PERSON> uses <PERSON>'s hammer to hit <PERSON><PERSON><PERSON>, <PERSON> then throws the hammer back to <PERSON>)\r\n", "[Vision] It's terribly well balanced.\r\n", "[Thor] Well, if there's too much weight, you lose power on the swing, so.\r\n", "[<PERSON>] I got it! Create a heat seal. I can...I can supercharge the spire from below.\r\n", "[FRIDAY] Running numbers. (<PERSON> fights off the robots from the Helicarrier's lifeboats) A heat seal could work with enough power.\r\n", "[<PERSON>] Thor, I got a plan!\r\n", "[<PERSON>] We're out of time. They're coming for the core.\r\n", "[<PERSON>] <PERSON>, get the rest of the people on board that carrier.\r\n", "[<PERSON>] On it.\r\n", "[<PERSON>] Avengers, time to work for a living.\r\n", "[<PERSON>] (the rest of the team joins Thor and Vision) You good?\r\n", "[<PERSON>] Yeah.\r\n", "[<PERSON>] <PERSON>? <PERSON> and <PERSON> better not be playing \"hide the zucchini.\"\r\n", "[<PERSON>] <PERSON><PERSON>, <PERSON>-head. Not all of us can fly. (as she joins the rest of the team) What's the drill?\r\n", "[<PERSON>] (points to the Vibranium core) This is the drill. If Ultron gets a hand on the core, we lose. (<PERSON><PERSON><PERSON> shows up)\r\n", "[Thor] Is that the best you can do? (<PERSON><PERSON><PERSON> summons his army of robots to join him)\r\n", "[<PERSON>] You had to ask.\r\n", "[<PERSON><PERSON><PERSON>] This is the best I can do. This is exactly what I wanted. All of you, against all of me. How could you possibly hope to stop me?\r\n", "[<PERSON>] Well, like the old man said. Together. (they all fight off Ultron's attacking robots)\r\n", "[<PERSON><PERSON><PERSON>] You know, with the benefit of hindsight.... (suddenly <PERSON> knocks him far away and the robots start to retreat)\r\n", "[<PERSON>] They'll try to leave the city.\r\n", "[<PERSON>] We can't let 'em, not even one. <PERSON><PERSON>!\r\n", "[<PERSON>] I'm on it. (to the approaching robots) Oh, no, I didn't say you could leave. War Machine, comin' at you, right ... (just then Vision flies in and helps to destroy the robots) Okay, what?\r\n", "[<PERSON>] We gotta move out. Even I can tell the air is getting thin. You guys get to the boats, I'll sweep for stragglers, be right behind you.\r\n", "[<PERSON>] What about the core?\r\n", "[<PERSON>] I'll protect it. It's my job.\r\n", "[<PERSON>] (<PERSON>, <PERSON> and <PERSON> leave; to <PERSON>) Get the people on the boats.\r\n", "[<PERSON>] I'm not going to leave you here.\r\n", "[<PERSON>] I can handle this. (just then she blasts off an approaching robot) Come back for me when everyone else is off, not before.\r\n", "[<PERSON>] Hmm.\r\n", "[<PERSON>] You understand?\r\n", "[<PERSON>] You know, I'm twelve minutes older than you. (<PERSON> chuckles)\r\n", "[<PERSON>] Go.\r\n", "[FRIDAY] Boss, power levels are way below opt...\r\n", "[<PERSON>] Re-route everything. We get one shot at this. (<PERSON> and <PERSON> are making their way to the lifeboats)\r\n", "[<PERSON>] I know what I need to do. The dining room! If I knock out that east wall, it'll make a nice work space for <PERSON>, huh? Put up some baffling, she can't hear the kids running around, what do you think?\r\n", "[<PERSON>] You guys always eat in the kitchen anyway.\r\n", "[<PERSON>] No one eats in a dining room. (they reach the lifeboats) We don't have a lot of time.\r\n", "[<PERSON>] So get your ass on a boat. (<PERSON> finds the Hulk) Hey, big guy. Sun's getting real low. (<PERSON> gets onto one of the lifeboats, but notices a woman calling out for her brother)\r\n", "[<PERSON><PERSON><PERSON>] <PERSON><PERSON><PERSON>? We were in the market. Costel?! (<PERSON> runs off to find the boy)\r\n", "[<PERSON>] Thor, I'm gonna need you back in the church.\r\n", "[<PERSON>] (referring to the people getting onto the lifeboats) Is this the last of them?\r\n", "[<PERSON>] Yeah. Everyone else is on the carrier.\r\n", "[<PERSON>] You know, if this works, we maybe don't walk away.\r\n", "[<PERSON>] Maybe not. (just as <PERSON> tries to calm <PERSON> down to get him back to being <PERSON> Ultron flies in with a jet and starts shooting at them)\r\n", "[<PERSON><PERSON><PERSON>] (sings) I got no strings, so I have fun. I'm not tied up to anyone. (as <PERSON> is saving the boy, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> starts shooting at them but <PERSON> intervenes and takes the shots to save them)\r\n", "[<PERSON>] You didn't see that coming. (<PERSON> falls to the ground as he dies, <PERSON> senses his death, causing her to fall in despair) (<PERSON> drops <PERSON> off onto the Helicarrier and then jumps onto the jet Ultron is on)\r\n", "[<PERSON><PERSON><PERSON>] Oh, for God's sake! (<PERSON> knocks <PERSON><PERSON><PERSON> out of the jet and he crashes inside a train; at the same time <PERSON> delivers <PERSON><PERSON><PERSON> safely to his sister onto the lifeboat, a man goes over to help him with his wound)\r\n", "[<PERSON>] No, no. I'm fine. (<PERSON> lies down next to the dead body of <PERSON>) Oh, it's been a long day. (<PERSON> then finds the Ultron)\r\n", "[<PERSON><PERSON><PERSON>] <PERSON>, if you stay here, you'll die.\r\n", "[<PERSON>] I just did. Do you know how it felt? (she uses her power to rip <PERSON><PERSON><PERSON>'s core out of his body) It felt like that. (<PERSON> fires a powerful blast to the core underneath the floating city)\r\n", "[<PERSON>] <PERSON>, on my mark. (<PERSON> brings on a blast of lightning, at the same time Vision flies in, picks up <PERSON> and flies off with as the city is being destroyed) Now! (<PERSON> uses his hammer to hit the Vibranium core in the church, destroying the core and the floating city)\r\n", "[<PERSON>] (to <PERSON> as he remains on the jet he knocked <PERSON><PERSON><PERSON> out of) Hey, big guy. We did it, the job's finished. Now I need you to turn this bird around, okay? We can't track you in stealth mode, so help me out. I need you t... (<PERSON> turns off <PERSON>'s camera, then sits as the jet flies off to an unknown destination, at the same time Vision finds Ultron)\r\n", "[Vision] You're afraid.\r\n", "[<PERSON><PERSON><PERSON>] Of you?\r\n", "[Vision] Of death. You're the last one.\r\n", "[<PERSON><PERSON><PERSON>] You were supposed to be the last. <PERSON> asked for a savior, and settled for a slave.\r\n", "[Vision] I suppose we're both disappointments. (<PERSON><PERSON><PERSON> chuckles)\r\n", "[<PERSON><PERSON><PERSON>] I suppose we are.\r\n", "[Vision] Humans are odd. They think order and chaos are somehow opposites, and try to control what won't be. But there is grace in their failings. I think you missed that.\r\n", "[<PERSON><PERSON><PERSON>] They're doomed.\r\n", "[Vision] Yes. But a thing isn't beautiful because it lasts. It's a privilege to be among them.\r\n", "[<PERSON><PERSON><PERSON>] You're unbearably naive.\r\n", "[Vision] Well, I was born yesterday. (as <PERSON><PERSON><PERSON> goes to attack him Vision uses the infinity stone in his head to destroy him)\r\n", "[<PERSON>] (we see <PERSON> returning to his family on the farm, then we see <PERSON> driving to the new Avengers facility in upstate New York; <PERSON> looks at <PERSON>'s new baby on her phone) Say hi to <PERSON><PERSON>. (<PERSON> sees the baby has been named <PERSON>)\r\n", "[<PERSON>] Fat.\r\n", "[<PERSON>] One of our tech boys flagged this, splashed down in the Banda Sea. Could be the Quinjet. But with <PERSON>'s stealth tech, we still can't track the damn thing.\r\n", "[<PERSON>] Right.\r\n", "[<PERSON>] Probably jumped out and swam to Fiji. He'll send a postcard.\r\n", "[<PERSON>] \"Wish you were here.\" You sent me to recruit him, way back when. Did you know then what was going to happen?\r\n", "[<PERSON>] You never know. You hope for the best and make do with what you get. I got a great team.\r\n", "[<PERSON>] Nothing lasts forever.\r\n", "[<PERSON>] Trouble, <PERSON>. No matter who wins or loses, trouble still comes around.\r\n", "[<PERSON>] The rules have changed.\r\n", "[<PERSON>] We're dealing with something new.\r\n", "[<PERSON>] Well, the Vision's artificial intelligence.\r\n", "[<PERSON>] A machine.\r\n", "[<PERSON>] So it doesn't count.\r\n", "[<PERSON>] No. It's not like a person lifting the hammer.\r\n", "[<PERSON>] Right. Different rules for us.\r\n", "[<PERSON>] Nice guy, but artificial.\r\n", "[<PERSON>] Thank you.\r\n", "[<PERSON>] If he can wield the hammer, he can keep the Mind Stone. It's safe with the Vision and these days, safe is in short supply.\r\n", "[<PERSON>] But if you put the hammer in an elevator...\r\n", "[<PERSON>] It would still go up.\r\n", "[<PERSON>] Elevator's not worthy.\r\n", "[<PERSON>] I'm going to miss these little talks of ours.\r\n", "[<PERSON>] Well, not if you don't leave.\r\n", "[<PERSON>] I have no choice. The Mind Stone is the fourth of the Infinity Stones to show up in the last few years. That's not a coincidence. Someone has been playing an intricate game and has made pawns of us. But once all these pieces are in position...\r\n", "[<PERSON>] Triple <PERSON>?\r\n", "[<PERSON>] You think you can find out what's coming?\r\n", "[<PERSON>] I do. Besides this one, there's nothing that can't be explained. (<PERSON> returns to Asgard which burns a circle in the grass he was standing on)\r\n", "[<PERSON>] That man has no regard for lawn maintenance. I'm gonna miss him though. And you're gonna miss me. There's gonna be a lot of manful tears. (as they walk over towards <PERSON>'s car)\r\n", "[<PERSON>] I will miss you, <PERSON>.\r\n", "[<PERSON>] Yeah? Well, it's time for me to tap out. Maybe I should take a page out of <PERSON>'s book and build Pepper a farm, hope nobody blows it up.\r\n", "[<PERSON>] The simple life.\r\n", "[<PERSON>] You'll get there one day.\r\n", "[<PERSON>] I don't know, family, stability. The guy who wanted all that went in the ice seventy-five years ago. I think someone else came out. (<PERSON> turns to get into his car)\r\n", "[<PERSON>] You alright?\r\n", "[<PERSON>] I'm home. (last lines; <PERSON> finds <PERSON> standing alone) You want to keep staring at the wall, or do you want to go to work? I mean, it's a pretty interesting wall.\r\n", "[<PERSON>] I thought you and <PERSON> were still gazing into each other's eyes. How do we look?\r\n", "[<PERSON>] Well, we're not the '27 Yankees. (hands <PERSON> a tablet)\r\n", "[<PERSON>] We've got some hitters.\r\n", "[<PERSON>] They're good. They're not a team.\r\n", "[<PERSON>] Let's beat 'em into shape. (they gather <PERSON> in his War Machine suit, <PERSON> in his Falcon suit, <PERSON> in a new suit, and <PERSON>)\r\n", "[<PERSON>] Avengers...!\r\n", "(mid-credits scene; we see the Infinity Gauntlet without any of the Stones, dissatisfied <PERSON><PERSON> opens a vault and puts on The Infinty Gauntlet, revealing himself)\r\n", "[<PERSON><PERSON>] Fine, I'll do it myself.\r\n", "(End of Avengers: Age of Ultron)\r\n", "\n"]}], "source": ["print(marvel[\"TEXT\"].values[0])"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>METADATA</th>\n", "      <th>SOURCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[Announcer] (first lines; announcement over sp...</td>\n", "      <td>{\"title\": \"Avengers: Age of Ultron\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>(1989 – <PERSON> enters a SHIELD facility and ...</td>\n", "      <td>{\"title\": \"Ant-Man\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>F O R  Y O U R  C O N S I D E R AT I O N\\r\\n\\r...</td>\n", "      <td>{\"title\": \"Avengers: Endgame\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Marvel’s THE AVENGERS\\r\\n\\r\\nWritten By\\r\\n\\r\\...</td>\n", "      <td>{\"title\": \"The Avengers\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>BLACK PANTHER \\r\\n\\r\\nAdapted \\r\\nScreenplay \\...</td>\n", "      <td>{\"title\": \"Black Panther\"}</td>\n", "      <td>marvel/pdunton</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                TEXT  \\\n", "0  [Announcer] (first lines; announcement over sp...   \n", "1  (1989 – <PERSON> enters a SHIELD facility and ...   \n", "2  F O R  Y O U R  C O N S I D E R AT I O N\\r\\n\\r...   \n", "3  Marvel’s THE AVENGERS\\r\\n\\r\\nWritten By\\r\\n\\r\\...   \n", "4  BLACK PANTHER \\r\\n\\r\\nAdapted \\r\\nScreenplay \\...   \n", "\n", "                               METADATA          SOURCE  \n", "0  {\"title\": \"Avengers: Age of Ultron\"}  marvel/pdunton  \n", "1                  {\"title\": \"Ant-Man\"}  marvel/pdunton  \n", "2        {\"title\": \"Avengers: Endgame\"}  marvel/pdunton  \n", "3             {\"title\": \"The Avengers\"}  marvel/pdunton  \n", "4            {\"title\": \"Black Panther\"}  marvel/pdunton  "]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["marvel.to_parquet(\"marvel.pq\", row_group_size=100, engine=\"pyarrow\", index=False)\n", "marvel.head()  # https://www.kaggle.com/datasets/pdunton/marvel-cinematic-universe-dialogue"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["18"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["len(marvel)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Doctor Who"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# doctor who via # https://www.kaggle.com/datasets/jeanmidev/doctor-who?select=all-scripts.csv\n", "kaggle.api.dataset_download_files(\"jeanmidev/doctor-who\", \"drwho\", unzip=True)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["episodes, diffusion = {}, {}\n", "for index, row in pd.read_csv(\"drwho/all-detailsepisodes.csv\").iterrows():\n", "    assert row[\"episodeid\"] not in episodes, row[\"episodeid\"]\n", "    episodes[row[\"episodeid\"]] = row[\"title\"]\n", "    diffusion[row[\"episodeid\"]] = row[\"first_diffusion\"]\n", "doctors = {\n", "    1: \"First Doctor\",\n", "    2: \"Second Doctor\",\n", "    3: \"Third Doctor\",\n", "    4: \"Fourth Doctor\",\n", "    5: \"Fifth Doctor\",\n", "    6: \"Sixth Doctor\",\n", "    7: \"Seventh Doctor\",\n", "    8: \"Eighth Doctor\",\n", "    9: \"Ninth Doctor\",\n", "    10: \"Tenth Doctor\",\n", "    11: \"Eleventh Doctor\",\n", "    12: \"Twelfth Doctor\",\n", "    13: \"Thirteenth Doctor\",\n", "    14: \"Fourteenth Doctor\",\n", "    15: \"Fifteenth Doctor\",\n", "}"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>idx</th>\n", "      <th>text</th>\n", "      <th>type</th>\n", "      <th>details</th>\n", "      <th>episodeid</th>\n", "      <th>doctorid</th>\n", "      <th>episode</th>\n", "      <th>season</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>Sylvest home</td>\n", "      <td>location</td>\n", "      <td>NaN</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>Twin boys are playing a cross between chess an...</td>\n", "      <td>context</td>\n", "      <td>NaN</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>Where's mother?</td>\n", "      <td>talk</td>\n", "      <td>REMUS</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>She's busy.</td>\n", "      <td>talk</td>\n", "      <td>SYLVEST</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>Does that mean she isn't talking to us?</td>\n", "      <td>talk</td>\n", "      <td>ROMULUS</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>5</td>\n", "      <td>No, she's just busy.</td>\n", "      <td>talk</td>\n", "      <td>SYLVEST</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>6</td>\n", "      <td>We would like to see her.</td>\n", "      <td>talk</td>\n", "      <td>BOTH</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>7</td>\n", "      <td>She isn't here.</td>\n", "      <td>talk</td>\n", "      <td>SYLVEST</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>8</td>\n", "      <td>She's gone out without saying goodbye?</td>\n", "      <td>talk</td>\n", "      <td>REMUS</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>9</td>\n", "      <td>Well, yes.</td>\n", "      <td>talk</td>\n", "      <td>SYLVEST</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>10</td>\n", "      <td>I suppose you're going out as well.</td>\n", "      <td>talk</td>\n", "      <td>ROMULUS</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>11</td>\n", "      <td>In a few minutes.</td>\n", "      <td>talk</td>\n", "      <td>SYLVEST</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>12</td>\n", "      <td>Abandoned again.</td>\n", "      <td>talk</td>\n", "      <td>REMUS</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>13</td>\n", "      <td>You we forgive, Father, but not Mother.</td>\n", "      <td>talk</td>\n", "      <td>ROMULUS</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>14</td>\n", "      <td>Look, <PERSON><PERSON><PERSON>, I wish you would be kinder to ...</td>\n", "      <td>talk</td>\n", "      <td>SYLVEST</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>15</td>\n", "      <td>Why?</td>\n", "      <td>talk</td>\n", "      <td>BOTH</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>16</td>\n", "      <td>She is your mother.</td>\n", "      <td>talk</td>\n", "      <td>SYLVEST</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>17</td>\n", "      <td>Because <PERSON> happened to give birth to us, ...</td>\n", "      <td>talk</td>\n", "      <td>REMUS</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>18</td>\n", "      <td>Yes. Yes, of course.</td>\n", "      <td>talk</td>\n", "      <td>SYLVEST</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>19</td>\n", "      <td>Respect must be earned, Father. Mother is a f...</td>\n", "      <td>talk</td>\n", "      <td>REMUS</td>\n", "      <td>21-7</td>\n", "      <td>6</td>\n", "      <td>7</td>\n", "      <td>21</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    idx                                               text      type  details  \\\n", "0     0                                       Sylvest home  location      NaN   \n", "1     1  Twin boys are playing a cross between chess an...   context      NaN   \n", "2     2                                    Where's mother?      talk    REMUS   \n", "3     3                                        She's busy.      talk  SYLVEST   \n", "4     4            Does that mean she isn't talking to us?      talk  ROMULUS   \n", "5     5                               No, she's just busy.      talk  SYLVEST   \n", "6     6                          We would like to see her.      talk     BOTH   \n", "7     7                                    She isn't here.      talk  SYLVEST   \n", "8     8             She's gone out without saying goodbye?      talk    REMUS   \n", "9     9                                         Well, yes.      talk  SYLVEST   \n", "10   10                I suppose you're going out as well.      talk  ROMULUS   \n", "11   11                                  In a few minutes.      talk  SYLVEST   \n", "12   12                                   Abandoned again.      talk    REMUS   \n", "13   13            You we forgive, Father, but not <PERSON>.      talk  ROMULUS   \n", "14   14   Look, <PERSON><PERSON><PERSON>, I wish you would be kinder to ...      talk  SYLVEST   \n", "15   15                                               Why?      talk     BOTH   \n", "16   16                                She is your mother.      talk  SYLVEST   \n", "17   17   Because <PERSON> happened to give birth to us, ...      talk    REMUS   \n", "18   18                               Yes. Yes, of course.      talk  SYLVEST   \n", "19   19   Respect must be earned, Father. Mother is a f...      talk    REMUS   \n", "\n", "   episodeid  doctorid  episode  season  \n", "0       21-7         6        7      21  \n", "1       21-7         6        7      21  \n", "2       21-7         6        7      21  \n", "3       21-7         6        7      21  \n", "4       21-7         6        7      21  \n", "5       21-7         6        7      21  \n", "6       21-7         6        7      21  \n", "7       21-7         6        7      21  \n", "8       21-7         6        7      21  \n", "9       21-7         6        7      21  \n", "10      21-7         6        7      21  \n", "11      21-7         6        7      21  \n", "12      21-7         6        7      21  \n", "13      21-7         6        7      21  \n", "14      21-7         6        7      21  \n", "15      21-7         6        7      21  \n", "16      21-7         6        7      21  \n", "17      21-7         6        7      21  \n", "18      21-7         6        7      21  \n", "19      21-7         6        7      21  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["who = pd.read_csv(\"drwho/all-scripts.csv\")\n", "who[\"episode\"] = who[\"episodeid\"].apply(lambda x: int(x.split(\"-\")[1]) if len(x.split(\"-\")) > 1 else -1)\n", "who[\"season\"] = who[\"episodeid\"].apply(lambda x: int(x.split(\"-\")[0]) if len(x.split(\"-\")) > 1 else -1)\n", "who.head(20)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████| 306/306 [00:41<00:00,  7.32it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>METADATA</th>\n", "      <th>SOURCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Doctor Who (23 Nov, 1963; First Doctor) - An U...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Doctor Who (21 Dec, 1963; First Doctor) - The ...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Doctor Who (8 Feb, 1964; First Doctor) - The E...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Doctor Who (22 Feb, 1964; First Doctor) - <PERSON>...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Doctor Who (11 Apr, 1964; First Doctor) - The ...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>301</th>\n", "      <td>Doctor Who (17 Nov, 2007; Tenth Doctor) - Time...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"\", \"episode\"...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>302</th>\n", "      <td>Doctor Who (16 Nov, 2012; Eleventh Doctor) - T...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"\", \"episode\"...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td>Doctor Who (21 Nov, 2009; Tenth Doctor) - Dr<PERSON>...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"\", \"episode\"...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304</th>\n", "      <td>Doctor Who (12 May, 1996 (Canada); Eighth Doct...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"\", \"episode\"...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>305</th>\n", "      <td>Doctor Who (25 Mar, 2013; Eleventh Doctor) - T...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"\", \"episode\"...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>306 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                                                  TEXT  \\\n", "0    Doctor Who (23 Nov, 1963; First Doctor) - An U...   \n", "1    Doctor Who (21 Dec, 1963; First Doctor) - The ...   \n", "2    Doctor Who (8 Feb, 1964; First Doctor) - The E...   \n", "3    Doctor Who (22 Feb, 1964; First Doctor) - <PERSON>...   \n", "4    Doctor Who (11 Apr, 1964; First Doctor) - The ...   \n", "..                                                 ...   \n", "301  Doctor Who (17 Nov, 2007; Tenth Doctor) - Time...   \n", "302  Doctor Who (16 Nov, 2012; Eleventh Doctor) - T...   \n", "303  Doctor Who (21 Nov, 2009; Tenth Doctor) - Dr<PERSON>...   \n", "304  Doctor Who (12 May, 1996 (Canada); Eighth Doct...   \n", "305  Doctor Who (25 Mar, 2013; Eleventh Doctor) - T...   \n", "\n", "                                              METADATA           SOURCE  \n", "0    {\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...  drwho/jeanmidev  \n", "1    {\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...  drwho/jeanmidev  \n", "2    {\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...  drwho/jeanmidev  \n", "3    {\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...  drwho/jeanmidev  \n", "4    {\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...  drwho/jeanmidev  \n", "..                                                 ...              ...  \n", "301  {\"show\": \"Doctor Who\", \"season\": \"\", \"episode\"...  drwho/jeanmidev  \n", "302  {\"show\": \"Doctor Who\", \"season\": \"\", \"episode\"...  drwho/jeanmidev  \n", "303  {\"show\": \"Doctor Who\", \"season\": \"\", \"episode\"...  drwho/jeanmidev  \n", "304  {\"show\": \"Doctor Who\", \"season\": \"\", \"episode\"...  drwho/jeanmidev  \n", "305  {\"show\": \"Doctor Who\", \"season\": \"\", \"episode\"...  drwho/jeanmidev  \n", "\n", "[306 rows x 3 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["doctor = {\"TEXT\": [], \"METADATA\": [], \"SOURCE\": []}\n", "for name, group in tqdm(who.groupby(\"episodeid\")):\n", "    metadata = {\n", "        \"show\": \"Doctor Who\",\n", "        \"season\": f\"s{str(group['season'].values[0]).zfill(2)}\" if group[\"season\"].values[0] > 0 else \"\",\n", "        \"episode\": f\"e{str(group['episode'].values[0]).zfill(2)}\" if group[\"episode\"].values[0] > 0 else \"\",\n", "        \"title\": episodes[group[\"episodeid\"].values[0]],\n", "    }\n", "    text, talk = (\n", "        f\"Doctor Who ({diffusion[group['episodeid'].values[0]]}; {doctors[group['doctorid'].values[0]]}) - {metadata['title']}\\r\\n\\r\\n\",\n", "        False,\n", "    )\n", "    for index, row in group.iterrows():\n", "        if row[\"type\"] == \"location\":\n", "            if talk:\n", "                text += \"\\r\\n---------------------------------------\\r\\n\\r\\n\"\n", "                talk = False\n", "            text += f\"({str(row['text']).strip()})\\r\\n\"\n", "        elif row[\"type\"] == (\"context\", \"unknown\"):\n", "            if talk:\n", "                text += \"\\r\\n\"\n", "                talk = False\n", "            text += f\"{str(row['text']).strip()}\\r\\n\\r\\n\"\n", "        elif pd.notna(row[\"details\"]):\n", "            text += f\"[{row['details']}] {str(row['text']).strip()}\\r\\n\"\n", "            talk = True\n", "    doctor[\"TEXT\"].append(text)\n", "    doctor[\"METADATA\"].append(json.dumps(metadata))\n", "    doctor[\"SOURCE\"].append(\"drwho/jeanmidev\")\n", "doctor = pd.<PERSON><PERSON><PERSON><PERSON>(doctor)\n", "doctor"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Doctor Who (23 Nov, 1963; First Doctor) - An Unearthly Child\r\n", "\r\n", "(Coal Hill School corridor)\r\n", "[GIRL] Night, <PERSON>.\r\n", "[BARBARA] Wait in here, please, <PERSON>. I won't be long.\r\n", "[BOY] Goodnight, <PERSON>.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Laboratory)\r\n", "[IAN] Oh? Not gone yet?\r\n", "[BARBARA] Obviously not.\r\n", "[IAN] Right, ask a silly question.\r\n", "[BARBARA] I'm sorry.\r\n", "[IAN] That's all right. I'll forgive you this time.\r\n", "[BARBARA] Oh, I had a terrible day. I don't know what to make of it.\r\n", "[IAN] Oh, what's the trouble? Can I help?\r\n", "[BARBARA] Oh, it's one of the girls, <PERSON>.\r\n", "[IAN] <PERSON>? She your problem too?\r\n", "[BARBARA] Yes.\r\n", "[IAN] You don't know what to make of her?\r\n", "[BARBARA] No.\r\n", "[IAN] How old is she, <PERSON>?\r\n", "[BARBARA] Fifteen.\r\n", "[IAN] Fifteen. She lets her knowledge out a bit at a time so as not toembarrass me. That's what I feel about her. She knows more science than<PERSON>'ll ever know. She's a genius. Is that what she's doing with history?\r\n", "[BARBARA] Something like that.\r\n", "[IAN] So your problem is whether to stay in business or to hand over theclass to her.\r\n", "[BARBARA] No, not quite.\r\n", "[IAN] What, then?\r\n", "[BARBARA] <PERSON>, I must talk to someone about this, but I don't want toget the girl into trouble. And I know you're going to tell me I'mimagining things.\r\n", "[IAN] No, I'm not.\r\n", "[BARBARA] Well, I told you how good she is at history. I had a talk with<PERSON> and told her she ought to specialise. Well, she seemed quiteinterested until I said I'd be willing to work with her at her home.Then she said that would be absolutely impossible as her grandfatherdidn't like strangers.\r\n", "[IAN] He's a doctor, isn't he? That's a bit of a lame excuse.\r\n", "[BARBARA] Well, I didn't pursue the point but then recently herhomework's been so bad.\r\n", "[IAN] Yes, I know.\r\n", "[BARBARA] Finally I got so irritated with all her excuses I decided tohave a talk with this grandfather of hers and tell him to take someinterest in her.\r\n", "[IAN] Did you indeed? And what's the old boy like?\r\n", "[BARBARA] Well, that's just it. I got her address from the secretary, 76Totter's Lane, and I went along there one evening. Oh <PERSON>, do payattention.\r\n", "[IAN] Sorry. You went along there one evening?\r\n", "[BARBARA] There isn't anything there. It's just an old junkyard.\r\n", "[IAN] You must have gone to the wrong place.\r\n", "[BARBARA] Well, that was the address the secretary gave me.\r\n", "[IAN] The secretary got it wrong, then.\r\n", "[BARBARA] No. I checked. There's a big wall on one side, houses on theother and nothing in the middle. And this nothing in the middle isnumber 76 Totter's Lane.\r\n", "[IAN] Hmm. That's a bit of a mystery. Well, there must be a simpleanswer somewhere.\r\n", "[BARBARA] Well, what?\r\n", "[IAN] Well, we'll have to find out for ourselves, won't we?\r\n", "[BARBARA] Thank you for the we. She's waiting in one of the classrooms.I'm lending her a book on the French Revolution.\r\n", "[IAN] What's she going to do, rewrite it? Oh, all right. What do we do?Ask her point-blank?\r\n", "[BARBARA] No, I thought we could drive there, wait till she arrives andsee where she goes.\r\n", "[IAN] Oh, all right.\r\n", "[BARBARA] That is, if you're not doing anything.\r\n", "[IAN] No, I'm not. After you.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Classroom)\r\n", "[BARBARA] Susan?\r\n", "[SUS<PERSON>] Oh, I'm sorry, <PERSON>. I didn't hear you coming in. Aren'tthey fabulous?\r\n", "[BARBARA] Who?\r\n", "[SUSAN] It's <PERSON> and the Common Men. They've gone from nineteento two.\r\n", "[IAN] <PERSON> is the stage name of the Honourable <PERSON>. Hestarted his career as <PERSON> and the Carollers, didn't he, <PERSON>?\r\n", "[SUSAN] You are surprising, Mister <PERSON>. I wouldn't expect you toknow things like that.\r\n", "[IAN] I have an enquiring mind. And a very sensitive ear.\r\n", "[SUS<PERSON>] Oh, I'm sorry.\r\n", "[IAN] Thank you.\r\n", "[SUSAN] Is that the book you promised me?\r\n", "[BARBARA] Yes.\r\n", "[<PERSON><PERSON><PERSON>] Thank you very much. It will be interesting. I'll return ittomor<PERSON>.\r\n", "[BARBARA] Oh, that's not necessary. Keep it until you've finished it.\r\n", "[SUSAN] I'll have finished it.\r\n", "[IAN] Oh, where do you live, <PERSON>? I'm giving <PERSON> a lift, I'vegot room for one more.\r\n", "[SUSAN] No, thank you, Mister <PERSON>. I like walking through thedark. It's mysterious.\r\n", "[BARBARA] Be careful, <PERSON>, there'll probably be fog again tonight.\r\n", "[SUSAN] Mmm.\r\n", "[BARBARA] See you in the morning.\r\n", "[SUS<PERSON>] I expect so. Good night.\r\n", "[BARBARA] Good night.\r\n", "[IAN] Good night, <PERSON>.\r\n", "[SUSAN] But that's not right.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Totter's Lane)\r\n", "[BARBARA] Over there.\r\n", "[IAN] We're lucky there was no fog. I'd never have found this.\r\n", "[BARBARA] Well, she doesn't seem to have arrived yet. I suppose we aredoing the right thing, aren't we?\r\n", "[IAN] You can't justify curiosity.\r\n", "[BARBARA] But her homework?\r\n", "[IAN] A bit of an excuse, really, isn't it? I've seen far worse. Thetruth is, we're both curious about <PERSON> and we won't be happy until weknow some of the answers.\r\n", "[BARBARA] You can't just pass it off like that. If I thought I was justbeing a busybody, I'd go straight home. I thought you agreed she was abit of a mystery.\r\n", "[IAN] Yes, but I think you'll find there's a very simple explanation toall this.\r\n", "[BARBARA] Well, I don't know how you explain the fact that a fifteenyear old girl does not know how many shillings there are in a pound.\r\n", "[IAN] Really?\r\n", "[BARBARA] Really. She said she thought we were on the decimal system.\r\n", "[IAN] Decimal system?\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Memory - classroom)\r\n", "[SUS<PERSON>] I'm sorry, <PERSON>.\r\n", "[BARBARA] Don't be silly, <PERSON>. The United States has a decimal system.You know perfectly well that we do not.\r\n", "[SUSAN] Of course, the decimal system hasn't started yet.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Totter's Lane)\r\n", "[IAN] I suppose she couldn't be a foreigner? No,doesn't make sense. Nothing about this girl makes sense. For instance,the other day I talking about chemical changes. I'd given out thelitmus paper to show cause and effect\r\n", "[BARBARA] And she knew the answer before you'd started.\r\n", "[IAN] Well, not quite. The answer simply didn't interest her.\r\n", "[SUSAN] Yes, I can see red turns to blue, <PERSON><PERSON><PERSON><PERSON><PERSON>, but that's because we're dealing with two inactivechemicals. They only act in relation to each other.\r\n", "[IAN] But that's the whole point of the experiment, <PERSON>.\r\n", "[SUS<PERSON>] Yes, it's a bit obvious, isn't it? Well, I'm not trying to berude, but couldn't we deal with two active chemicals? Then red couldturn blue all by itself and get on with something else? I'm sorry, itwas just an idea.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Totter's Lane)\r\n", "[IAN] She means it. These simple experiments are<PERSON>'s play to her.\r\n", "[BARBARA] You know, it's almost got to the point where I deliberatelywant to trip her up.\r\n", "[IAN] Yes. Something like that happened the other day. I'd set the classa problem with A, B and C as the three dimensions.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Memory - classroom)\r\n", "[SUSAN] It's impossible unless you use D and E.\r\n", "[IAN] <PERSON> and <PERSON>? Whatever for? Do the problem that's set, <PERSON>.\r\n", "[SUSAN] I can't, Mister <PERSON>. You can't simply work on three ofthe dimensions.\r\n", "[IAN] Three of them? Oh, time being the fourth dimension, I suppose?Then what do you need E for? What do you make the fifth dimension?\r\n", "[SUSAN] Space.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Totter's Lane)\r\n", "[BARBARA] Too many questions and not enoughanswers.\r\n", "[IAN] Stupid? Or just doesn't know. So we have a fifteen year old girlwho is absolutely brilliant at some things, and excruciatingly bad atothers.\r\n", "[BARBARA] There she is.\r\n", "[BARBARA] Look, can we go in now? I hate to think of her alone in thatplace.\r\n", "[IAN] If she is alone. Look, she is fifteen. She might be meeting a boy.Didn't that occur to you?\r\n", "[BARBARA] I almost hope she is.\r\n", "[IAN] What do you mean?\r\n", "[BARBARA] Well, it would be so wonderfully normal. It's silly, isn't it?I feel frightened. As if we're about to interfere in something that isbest left alone.\r\n", "[<PERSON><PERSON>] Come on, let's get it over with.\r\n", "[BARBARA] Well, don't you feel it?\r\n", "[IAN] I take things as they come. Come on.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Junk yard)\r\n", "[IAN] What a mess. We're not turning over any of this stuff to findher..\r\n", "[BARBARA] Over there?\r\n", "[IAN] Blast. I've dropped it.\r\n", "[BARBARA] What?\r\n", "[IAN] The torch.\r\n", "[BARBARA] Well, use a match.\r\n", "[IAN] I haven't got any. Oh, never mind.\r\n", "[BARBARA] Susan?\r\n", "[IAN] <PERSON>? <PERSON>? <PERSON>! <PERSON>. Mister <PERSON> and <PERSON>. Shecan't have got out without us seeing her.\r\n", "[BARBARA] <PERSON>, look at this.\r\n", "[IAN] It's a police box! What on earth's it doing here? These things areusually on the street. Feel it. Feel it. Do you feel it?\r\n", "[BARBARA] It's a faint vibration.\r\n", "[IAN] It's alive!\r\n", "[IAN] It's not connected to anything, unless it's through the floor.\r\n", "[BARBARA] Look, I've had enough. Let's go and find a policeman.\r\n", "[IAN] Yes, all right.\r\n", "[BARBARA] Is that her?\r\n", "[IAN] That's not her. <PERSON>.\r\n", "[SUSAN [OC]] There you are, Grandfather.\r\n", "[BARBARA] It's Susan.\r\n", "[IAN] Shush!\r\n", "[IAN] Excuse me.\r\n", "[DOCTOR] What are you doing here?\r\n", "[IAN] We're looking for a young girl.\r\n", "[DOCTOR] We?\r\n", "[BARBARA] Good evening.\r\n", "[DOCTOR] What do you want?\r\n", "[IAN] One of our pupils, <PERSON>, came into this yard.\r\n", "[DOCTOR] Really? In here? Are you sure?\r\n", "[BARBARA] Yes, we saw her from across the street.\r\n", "[DOCTOR] (aside) One of their pupils, not the police, then.\r\n", "[IAN] I beg your pardon?\r\n", "[DOCTOR] Why were you were spying on her? Who are you?\r\n", "[IAN] We heard a young girl's voice call out to you.\r\n", "[DOCTOR] Your hearing must be very acute. I didn't hear anything.\r\n", "[BARBARA] It came from in here.\r\n", "[DOCTOR] You imagined it.\r\n", "[BARBARA] I certainly did not imagine it.\r\n", "[DOCTOR] Young man, is it reasonable to suppose that anybody would beinside a cupboard like that, hmm?\r\n", "[IAN] Would it therefore be unreasonable to ask you to let us have alook inside?\r\n", "[DOCTOR] I wonder why I've never seen that before. Now isn't thatstrange. Very damp and dirty.\r\n", "[BARBARA] Won't you help us? We're two of her teachers from the CoalHill School. We saw her come in and we haven't seen her leave.Naturally, we're worried.\r\n", "[DOCTOR] Have to be cleaned. Hmm? Oh, I'm afraid it's none of mybusiness. I suggest you leave here.\r\n", "[IAN] Not until we're satisfied that <PERSON> isn't in there. And frankly,I don't understand your attitude.\r\n", "[DOCTOR] Yours leaves a lot to be desired.\r\n", "[IAN] Will you open the door?\r\n", "[DOCTOR] There's nothing in there.\r\n", "[IAN] Then what are you afraid to show us?\r\n", "[DOCTOR] A<PERSON><PERSON>? Oh, go away.\r\n", "[IAN] I think we'd better go and fetch a policeman.\r\n", "[DOCTOR] Very well.\r\n", "[IAN] And you're coming with us.\r\n", "[DOCTOR] Oh, am I? I don't think so, young man. No, I don't think so.\r\n", "[BARBARA] We can't force him.\r\n", "[IAN] But we can't leave him here. Doesn't it seem obvious to you he'sgot her locked up in there? Look at it. There's no door handle. Theremust be a secret lock somewhere.\r\n", "[BARBARA] That was <PERSON>'s voice.\r\n", "[IAN] But of course it was. <PERSON>! <PERSON>! <PERSON>, are you in there? It's<PERSON><PERSON> and <PERSON>, <PERSON>.\r\n", "[DOCTOR] Don't you think you're being rather high-handed, young man? Youthought you saw a young girl enter the yard. You imagine you heard hervoice. You believe she might be inside there. It's not verysubstantial, is it?\r\n", "[BARBARA] But why won't you help us?\r\n", "[DOCTOR] I'm not hindering you. If you both want to make fools ofyourselves, I suggest you do what you said you'd do. Go and find apoliceman.\r\n", "[IAN] While you nip off quietly in the other direction.\r\n", "[DOCTOR] Insulting. There's only one way in and out of this yard. <PERSON><PERSON> be here when you get back. I want to see your faces when you tryto explain away your behaviour to a policeman.\r\n", "[IAN] Nevertheless, we're going to find one. Come on, <PERSON>.\r\n", "[SUSAN [OC]] What are you doing out there?\r\n", "[IAN] She is in there!\r\n", "[DOCTOR] Close the door!\r\n", "[IAN] Barbara!\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(<PERSON><PERSON><PERSON>)\r\n", "[DOCTOR] Close the door, <PERSON>. I believe these people are known to you.\r\n", "[SUSAN] They're two of my schoolteachers. What are you doing here?\r\n", "[BARBARA] Where are we?\r\n", "[DOCTOR] They must have followed you. That ridiculous school. I knewsomething like this would happen if we stayed in one place too long.\r\n", "[SUS<PERSON>] But why should they follow me?\r\n", "[BARBARA] Is this really where you live, <PERSON>?\r\n", "[SUSAN] Yes.\r\n", "[DOCTOR] And what's wrong with it?\r\n", "[IAN] But it was just a telephone box.\r\n", "[DOCTOR] Perhaps.\r\n", "[BARBARA] And this is your grandfather?\r\n", "[SUSAN] Yes.\r\n", "[BARBARA] But why didn't you tell us that?\r\n", "[DOCTOR] I don't discuss my private life with strangers.\r\n", "[IAN] But it was a police telephone box. I walked all around it.<PERSON>, you saw me.\r\n", "[DOCTOR] You don't deserve any explanations. You pushed your way in hereuninvited and unwelcome.\r\n", "[BARBARA] I think we ought to leave.\r\n", "[IAN] No, just a minute. I know this is absurd, but I feel\r\n", "[DOCTOR] Oh dear, dear, dear dear. This is very\r\n", "[IAN] I walked all round it.\r\n", "[DOCTOR] It's stopped again, you know, and I've tried hmm? Oh, youwouldn't understand.\r\n", "[IAN] But I want to understand.\r\n", "[DOCTOR] Yes, yes, yes, yes, yes, yes. By the way, <PERSON>, I managed tofind a replacement for that faulty filament. It's an amateur job, but Ithink it'll serve.\r\n", "[IAN] It's an illusion. It must be.\r\n", "[DOCTOR] What is he talking about now?\r\n", "[SUS<PERSON>] What are you doing here?\r\n", "[DOCTOR] You don't understand, so you find excuses. Illusions, indeed?You say you can't fit an enormous building into one of your smallersitting rooms.\r\n", "[IAN] No.\r\n", "[DOCTOR] But you've discovered television, haven't you?\r\n", "[IAN] Yes.\r\n", "[DOCTOR] Then by showing an enormous building on your television screen,you can do what seemed impossible, couldn't you?\r\n", "[IAN] Well, yes, but I still don't know\r\n", "[DOCTOR] Not quite clear, is it. I can see by your face that you're notcertain. You don't understand. And I knew you wouldn't. Never mind. Nowthen, which switch was it? No. No, no. Ah yes, that is it. The point isnot whether you understand. What is going to happen to you, hmm?They'll tell everybody about the ship now.\r\n", "[IAN] Ship?\r\n", "[DOCTOR] Yes, yes, ship. This doesn't roll along on wheels, you know.\r\n", "[BARBARA] You mean it moves?\r\n", "[SUSAN] The Tardis can go anywhere.\r\n", "[BARBARA] Tardi<PERSON>? I don't understand you, <PERSON>.\r\n", "[SUSAN] Well, I made up the name <PERSON><PERSON><PERSON> from the initials, Time AndRelative Dimension In Space. I thought you'd both understand when yousaw the different dimensions inside from those outside.\r\n", "[IAN] Just let me get this straight. A thing that looks like a policebox, standing in a junkyard, it can move anywhere in time and space?\r\n", "[SUSAN] Yes.\r\n", "[DOCTOR] Quite so.\r\n", "[IAN] But that's ridiculous.\r\n", "[SUSAN] Why won't they believe us?\r\n", "[BARBARA] How can we?\r\n", "[DOCTOR] Now, now, don't get exasperated, <PERSON>. Remember the RedIndian. When he saw the first steam train, his savage mind thought itan illusion, too.\r\n", "[IAN] You're treating us like children.\r\n", "[DOCTOR] Am I? The children of my civilisation would be insulted.\r\n", "[IAN] Your civilisation?\r\n", "[DOCTOR] Yes, my civilisation. I tolerate this century, but I don'tenjoy it. Have you ever thought what it's like to be wanderers in thefourth dimension? Have you? To be exiles? <PERSON> and I are cut off fromour own planet, without friends or protection. But one day we shall getback. Yes, one day. One day.\r\n", "[SUSAN] It's true. Every word of it's true. You don't know what you'vedone coming in here. Grand<PERSON>, let them go now, please. Look, ifthey don't understand, they can't, they can't hurt us at all. I<PERSON>erstand these people better than you. Their minds reject things theydon't understand.\r\n", "[DOCTOR] No.\r\n", "[IAN] He can't keep us here.\r\n", "[BARBARA] <PERSON>, listen to me. Can't you see that all this is anillusion? It's a game that you and your grandfather are playing, if youlike, but you can't expect us to believe it.\r\n", "[SUSAN] It's not a game!\r\n", "[BARBARA] But <PERSON>, it's\r\n", "[SUSAN] It's not! Look, I love your school. I loved England in thetwentieth century. The last five months have been the happiest of mylife.\r\n", "[BARBARA] But you are one of us. You look like us, you sound like us.\r\n", "[SUSAN] I was born in another time, another world.\r\n", "[IAN] Now look here, <PERSON>, you. Oh, come on, <PERSON>, let's get out ofhere.\r\n", "[SUS<PERSON>] It's no use, you can't get out. He won't let you go.\r\n", "[IAN] He closed the doors from over there. I saw him. Now, which is it?Which is it? Which control operates the door?\r\n", "[DOCTOR] You still think it's all an illusion?\r\n", "[IAN] I know that free movement time and space is a scientific dream Idon't expect to find solved in a junkyard.\r\n", "[DOCTOR] Your arrogance is nearly as great as your ignorance.\r\n", "[IAN] Will you open the door? Open the door! <PERSON>, will you help us?\r\n", "[SUSAN] I mustn't.\r\n", "[IAN] Very well, then. I'll have to risk it myself.\r\n", "[DOCTOR] I can't stop you.\r\n", "[SUSAN] Don't touch it! It's live!\r\n", "[BARBARA] Ian! What on earth do you think you're doing?\r\n", "[S<PERSON><PERSON>] Grandfather, let them go now, please.\r\n", "[DOCTOR] And by tomorrow we shall be a public spectacle, a subject fornews and idle gossip.\r\n", "[SUSAN] But they won't say anything.\r\n", "[DOCTOR] My dear child, of course they will. Put yourself in theirplace. They are bound to make some sort of a complaint to theauthorities, or at the very least talk to their friends. If I do letthem go, <PERSON>, you realise of course we must go, too.\r\n", "[SUSAN] No, Grandfather, we've had all this out before.\r\n", "[DOCTOR] There's no alternative, child.\r\n", "[S<PERSON><PERSON>] I want to stay! But they're both kind people. Why won't youtrust them? All you've got to do is ask them to promise to keep oursecret\r\n", "[DOCTOR] It's out of the question.\r\n", "[SUS<PERSON>] I won't go, <PERSON><PERSON>. I won't leave the twentieth century.I'd rather leave the Tardis and you.\r\n", "[DOCTOR] Now you're being sentimental and childish.\r\n", "[SUSAN] No, I mean it.\r\n", "[DOCTOR] Very well. Then you must go with them. I'll open the door.\r\n", "[BARBARA] Are you coming, <PERSON>?\r\n", "[SUSAN] Oh, no, Grandfather! No!\r\n", "[DOCTOR] Let me go.\r\n", "[SUSAN] No!\r\n", "[DOCTOR] Get back to the ship, child. Hold it.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave)\r\n", "[MOTHER] Where is the fire <PERSON><PERSON> makes?\r\n", "[HUR] (a woman) In his hands. We are not going to the wood.\r\n", "[ZA] My father made fire.\r\n", "[MOTHER] They killed him for it. It is better that we live as we havealways done.\r\n", "[ZA] He showed me how to sharpen the stones and trap the bear and thetiger. He should have shown me this, too.\r\n", "[MOTHER] So that everyone would bow to you as they did to him?\r\n", "[Z<PERSON>] Tell me what my father did to make fire.\r\n", "[MOTHER] I never saw him make it. That is all I know.\r\n", "[ZA] Out of my sight, old woman. You should have died with him.\r\n", "[MOTHER] <PERSON><PERSON> will never make fire.\r\n", "[ZA] Put on more of the dead fire.\r\n", "[HUR] The old men are talking against you, <PERSON><PERSON>. They say it would bebetter for the stranger Ka<PERSON> to lead us.\r\n", "[<PERSON><PERSON>] <PERSON><PERSON>?\r\n", "[HUR] They say you sit all day rubbing your hands together while hebrings us meat.\r\n", "[ZA] Without meat, we go hungry. Without fire, we die.\r\n", "[HUR] Old men see no further than tomorrow's meat. They will make <PERSON><PERSON><PERSON> leader. My father will give me to him.\r\n", "[Z<PERSON>] <PERSON><PERSON> is no leader.\r\n", "[HUR] The leader is the one who makes fire.\r\n", "[ZA] Where has the fire gone? Where? Where?\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(<PERSON><PERSON><PERSON>)\r\n", "[BARBARA] <PERSON>? <PERSON>.\r\n", "[IAN] I'm all right. I must have hit my head. The movement's stopped.\r\n", "[SUSAN] The base is steady.\r\n", "[DOCTOR] Layer of sand, rock formation. Good.\r\n", "[SUSAN] We've left 1963.\r\n", "[DOCTOR] Oh, yes, undoubtedly. I'll be able to tell you where presently.Zero? That's not right. I'm afraid this yearometer is not calculatingproperly. Hm! Well, anyway, the journey's finished. (to <PERSON>) What areyou doing down there?\r\n", "[BARBARA] What have you done?\r\n", "[<PERSON><PERSON>] <PERSON>, you don't believe all this nonsense.\r\n", "[SUSAN] Well, look at the scanner screen.\r\n", "[DOCTOR] Yes, look up there. They don't understand and I suspect theydon't want to. Well, there you are. A new world for you.\r\n", "[IAN] Sand and rock?\r\n", "[DOCTOR] Yes, that's the immediate view outside the ship.\r\n", "[BARBARA] But where are we?\r\n", "[IAN] You mean that's what we'll see when we go outside?\r\n", "[SUSAN] Yes, you'll see it for yourself.\r\n", "[IAN] I don't believe it.\r\n", "[DOCTOR] You really are a stubborn young man, aren't you?\r\n", "[IAN] All right, show me some proof. Give me some concrete evidence. I'm<PERSON><PERSON>, <PERSON>, I don't want to hurt you, but it's time you were broughtback to reality.\r\n", "[SUSAN] But you're wrong, Mister <PERSON>.\r\n", "[DOCTOR] They are saying I'm a charlatan. What concrete evidence wouldsatisfy you?\r\n", "[IAN] Just open the doors, <PERSON>.\r\n", "[DOCTOR] Eh? Doctor who? What's he talking about?\r\n", "[BARBARA] They're so sure, <PERSON>.\r\n", "[IAN] Yes, I know.\r\n", "[BARBARA] And remember the difference between the outside of the policebox and the inside.\r\n", "[IAN] Yes, I know, but. Are you going to open the doors or aren't you?\r\n", "[DOCTOR] No.\r\n", "[IAN] You see?\r\n", "[DOCTOR] Not until I'm quite sure it's safe to do so. Well, yes, good.Yes, it is, it's good. Excellent, excellent. You've got the radiationcounter there. What's it read?\r\n", "[SUSAN] It's reading normal, Grandfather.\r\n", "[DOCTOR] Splendid, splendid. Well, I think I'll take my Geiger counterwith me in any case. So, you still challenge me, young man?\r\n", "[IAN] Well, just open the doors and prove your point.\r\n", "[DOCTOR] You're so narrow-minded, aren't you? Don't be so insular.\r\n", "[<PERSON><PERSON><PERSON>] Grandfather, do you know where we are?\r\n", "[DOCTOR] Yes, we've gone back in time, all right. One or two samples and<PERSON> shall be able to make an estimate. With rock pieces and a few plants.But I do wish this wouldn't keep letting me down. However, we can goout now.\r\n", "[IAN] Just a minute. You say we've gone back in time?\r\n", "[DOCTOR] Yes, quite so.\r\n", "[IAN] So that when we go out of that door, we won't be in a junkyard inLondon in England in the year 1963?\r\n", "[DOCTOR] That is quite correct. But your tone suggests ridicule.\r\n", "[IAN] But it is ridiculous. Time doesn't go round and round in circles.You can't get on and off whenever you like in the past or the future.\r\n", "[DOCTOR] Really? Where does time go, then?\r\n", "[IAN] It doesn't go anywhere. It just happens and then it's finished.\r\n", "[DOCTOR] (to <PERSON>) You're not as doubtful as your friend, I hope?\r\n", "[BARBARA] No.\r\n", "[IAN] <PERSON>, you can't\r\n", "[BARBARA] I can't help it. I just believe them, that's all.\r\n", "[DOCTOR] If you could touch the alien sand and hear the cries of strangebirds and watch them wheel in another sky, would that satisfy you?\r\n", "[IAN] Yes.\r\n", "[DOCTOR] Now, see for yourself.\r\n", "[IAN] It's not true. It can't be.\r\n", "[SUSAN] That's not on the screen.\r\n", "[DOCTOR] Well I've no more time to argue with you. I must get somesamples, <PERSON>.\r\n", "[SUS<PERSON>] Be careful, Grandfather.\r\n", "[DOCTOR] (exiting, muttering) Oh, dear, it's disgusting really.\r\n", "[BARBARA] <PERSON>, come out and look.\r\n", "[SUSAN] Oh here, lean on me.\r\n", "[IAN] No, thank you. I'm all right, thanks.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Wasteland)\r\n", "[BARBARA] Well?\r\n", "[IAN] But there must be some explanation.\r\n", "[DOCTOR] It's still a police box. Why hasn't it changed? Dear, dear, howvery disturbing.\r\n", "[BARBARA] What do you think it could be? <PERSON>, look at this.\r\n", "[IAN] I don't know. It hasn't got any horns or antlers. Could be ahorse. Could be anything. Incredible. A police box in the midst of. Oh,it just doesn't make sense.\r\n", "[SUSAN] It should have changed. Wonder why it hasn't happened this time.\r\n", "[BARBARA] The ship, you mean?\r\n", "[SUSAN] Yes, it's been an Ionic column and a sedan chair.\r\n", "[BARBARA] Disguising itself wherever it goes.\r\n", "[S<PERSON><PERSON>] Yes, that's right. But it hasn't happened this time. I wonderwhy not. I wonder if this old head would help <PERSON><PERSON>. Where is he?\r\n", "[BARBARA] You're very quiet.\r\n", "[IAN] I was wrong, wasn't I.\r\n", "[BARBARA] Oh, look, I don't understand it any more than you do. The<PERSON>ide of the ship, suddenly finding ourselves here. Even some of thethings Doctor <PERSON> says\r\n", "[IAN] That's not his name. Who is he? Doctor who? Perhaps if we knew hisname we might have a clue to all this.\r\n", "[BARBARA] Look, <PERSON>, the point is, it's happened.\r\n", "[IAN] Yes, it has. But it's impossible to accept. I know I'm here\r\n", "[<PERSON><PERSON><PERSON>] I can't see him anywhere.\r\n", "[BARBARA] He can't be far away. I had a feeling just now as if we werebeing watched. Grandfather.\r\n", "[S<PERSON><PERSON>] Grandfather!\r\n", "[IAN] Come on!\r\n", "[IAN] Look.\r\n", "[SUSAN] What is it?\r\n", "[BARBARA] Some of his things.\r\n", "[S<PERSON><PERSON>] Grandfather, where are you?\r\n", "[IAN] <PERSON>, don't panic.\r\n", "[SUSAN] I must find him.\r\n", "[IAN] <PERSON>.\r\n", "[SUSAN] I must see.\r\n", "[IAN] Well, be careful then.\r\n", "[BARBARA] <PERSON>, look.\r\n", "[<PERSON>AN] (broken <PERSON><PERSON><PERSON> counter) It's not much good any more.\r\n", "[BARBARA] Well, maybe he saw something and went off to investigate.\r\n", "[<PERSON><PERSON>] Leaving this? (his hat)\r\n", "[BARBARA] Well, what do you think happened?\r\n", "[IAN] I don't know. Perhaps he was excited and went off to investigatesomething as you suggest, but. He may have been taken.\r\n", "[<PERSON><PERSON><PERSON>] I can't see him. I can't find him anywhere. There's not a signof him.\r\n", "[IAN] <PERSON>m down, <PERSON>.\r\n", "[BARBARA] <PERSON>, don't worry. What's the matter?\r\n", "[SUSAN] It's his notes. He'd never leave his notebook. It's tooimportant to him. It's got the key codes of all the machines in theship. It's got notes of everywhere we've been to. Something terriblehas happened to him, I know it has. We must find him.\r\n", "[BARBARA] <PERSON>, <PERSON>. We'll find him, I promise you. He can't be faraway.\r\n", "[IAN] What's on the other side of those rocks?\r\n", "[SUSAN] There's a line of trees. There's a gap in them. There might be apath on the other side.\r\n", "[IAN] (gathering up the <PERSON>'s things) All right, we'll try therefirst. Come on. Strange.\r\n", "[BARBARA] What?\r\n", "[IAN] This sand. It's cold. It's nearly freezing.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave)\r\n", "[HORG] <PERSON><PERSON> says, where he comes from, he's often seen men make fire.\r\n", "[<PERSON><PERSON>] <PERSON><PERSON> is a liar.\r\n", "[HORG] He says <PERSON><PERSON> will soon show him how it is done.\r\n", "[ZA] All his tribe died in the last cold. If he had not found us, hewould have died too.\r\n", "[HUR] What else did he say?\r\n", "[HORG] He says <PERSON><PERSON> only shows the secret to the leader.\r\n", "[<PERSON><PERSON>] I am leader. <PERSON><PERSON> will show me. I am the son of the great firemaker,but he does not show me how to put flames into the sticks. <PERSON><PERSON> comes. Ido not kill him. I let him eat with us and sleep in our caves. I willhave to spill some blood and make people bow to me.\r\n", "[ZA] This is a strange creature.\r\n", "[KAL] Is <PERSON><PERSON>, son of the firemaker, afraid of an old man? When will <PERSON><PERSON><PERSON> fire come from his hands?\r\n", "[ZA] When <PERSON><PERSON> decides it.\r\n", "[KAL] <PERSON><PERSON> is for strong men. <PERSON><PERSON> has sent me this creature to make firecome from his fingers. I have seen it. Inside, he's full of fire. Thesmoke comes from his mouth.\r\n", "[ZA] As lies come out of yours. He wears strange skins.\r\n", "[<PERSON><PERSON>] <PERSON><PERSON> is afraid. There was a strange tree. The creature was in it. <PERSON><PERSON><PERSON><PERSON> have run away had he seen it.\r\n", "[ZA] Silence!\r\n", "[KAL] When I saw fire come from his fingers I remembered <PERSON><PERSON>, son of thefiremaker. And when the cold comes, you will all die if you wait for <PERSON><PERSON> make fire for you. I, <PERSON><PERSON>, am a true leader. We fought like the<PERSON><PERSON> and the bear. My strength was too much for him. He lay down tosleep. And I, <PERSON><PERSON>, carried him here to make fire for you.\r\n", "[ZA] Why do you listen to <PERSON><PERSON>?\r\n", "[HORG] <PERSON><PERSON> has many good skins. He has forgotten what the cold is like.\r\n", "[ZA] Tomorrow, I kill many bears. You all have warm skins.\r\n", "[HORG] I say tomorrow you will rub your hands together and hold them tothe dry sticks and ask <PERSON><PERSON> to send you fire. And the bears will staywarm in their own skins.\r\n", "[ZA] What I say I will do, I will do.\r\n", "[KAL] The firemaker is dead. You all carry dry sticks with you. Button<PERSON>, I make them burn. I am leader.\r\n", "[HUR] The creature has opened its eyes.\r\n", "[DOCTOR] Where's my, where.\r\n", "[KAL] Do you want fire, or do you want to die in the cold?\r\n", "[ALL] Fire! Fire!\r\n", "[K<PERSON>] When it's cold, the tiger comes to our caves again at night. <PERSON><PERSON><PERSON> give you to the tiger. <PERSON><PERSON> will give you to the cold. <PERSON><PERSON> rubs hishands and waits for <PERSON><PERSON> to remember him. My creature can make fire comefrom his fingers. I have seen it. But I, <PERSON><PERSON>, brought him here. Thecreature is mine.\r\n", "[Z<PERSON>] He's just an old man in strange skins. <PERSON><PERSON> has been with us toolong. It is time he died.\r\n", "[HORG] I say there is truth in both of you. <PERSON><PERSON> speaks truth, but firecannot live in men. And <PERSON><PERSON> speaks the truth that we die without fire.\r\n", "[HUR] Will my father listen to a woman? If this old man can make firecome from his fingers, let us see it now.\r\n", "[ZA] I say what is to be done here, not old men and women.\r\n", "[K<PERSON>] <PERSON><PERSON> tries to talk like his father, the firemaker. <PERSON><PERSON> does not wantto see fire made. But I, <PERSON><PERSON>, am not afraid of fire. I will make mycreature make fire.\r\n", "[ZA] I will take him to the Cave of Skulls and he will tell me thesecret!\r\n", "[DOCTOR] I can make fire for you. Let me go and I'll make all the fireyou want. You don't have to be afraid of me. I'm an old man. How can anold man like me harm any of you?\r\n", "[<PERSON><PERSON>] What does he say?\r\n", "[HORG] Fire? He says he can make fire for us.\r\n", "[<PERSON><PERSON>] He makes it for me and I give you fire. I am firemaker.\r\n", "[Z<PERSON>] He will make it for me.\r\n", "[DOCTOR] (searching his pockets) My matches. Where are they? I must getback. Must get back to the ship.\r\n", "[<PERSON><PERSON>] <PERSON><PERSON>'s creature, he makes fire only for <PERSON><PERSON>.\r\n", "[DOCTOR] Take me back to my ship and I will make fire for you. All thefire you want.\r\n", "[ZA] This is more of your lies. The old man cannot make fire.\r\n", "[<PERSON><PERSON>] There was a tree and the creature came from in it. And the fire,it came out of his fingers.\r\n", "[ZA] You want to be strong like <PERSON><PERSON>, son of the great firemaker. You allheard him say that there would be fire. There is no fire. <PERSON><PERSON> does nottell you lies. He does not say, I will do this thing, and then not doit. He does not say, I will make you warm, and then leave you to thedark. He does not say, I will fight away the tiger with fire, and thenlet him come to you in the dark. Do you want a liar for your chief?\r\n", "[ALL] No!\r\n", "[KAL] Make fire. Make fire.\r\n", "[HUR] You are trapped in your own lies, <PERSON><PERSON>.\r\n", "[ZA] Great chief who is afraid of nothing. Oh great <PERSON><PERSON>, save us fromthe cold. Save us from the tiger.\r\n", "[KAL] Make fire. Make fire come from your fingers as I saw you.\r\n", "[DOCTOR] I have no matches. I cannot make fire. I cannot make fire!\r\n", "[Z<PERSON>] Let the old man die. And we'll watch the great <PERSON><PERSON> as he kills hisstrong enemy.\r\n", "[KAL] Make fire! Make fire! Or I kill you now!\r\n", "[ZA] Or we'll keep them and make them hunt for us. It's good to have<PERSON><PERSON> to laugh at!\r\n", "[S<PERSON><PERSON>] Grandfather!\r\n", "[DOCTOR] If he dies, there will be no fire.\r\n", "[MOTHER] Kill her. Kill her.\r\n", "[Z<PERSON>] Wait. You cannot kill all our enemies. When <PERSON><PERSON> gives fire back tothe sky, let him look down on them. Then that is when they die, and <PERSON><PERSON>will give us fire again. Take them to the Cave of Skulls.\r\n", "[SUSAN] No! Grandfather!\r\n", "[DOCTOR] All right.\r\n", "[ZA] The woman is mine.\r\n", "[HORG] My daughter is for the leader of the tribe.\r\n", "[ZA] Yes, the woman is mine.\r\n", "[HORG] I do not like what has happened.\r\n", "[ZA] Old men never like new things to happen.\r\n", "[HORG] I was a great leader of many men.\r\n", "[ZA] Many men, yes. They all dies when <PERSON><PERSON> left the sky and the greatcold was on the ground. And <PERSON><PERSON> will give me fire again. To me. Not toy<PERSON>. Just as you will give me her.\r\n", "[HUR] <PERSON><PERSON> will be a strong leader of many men. If you give me to him, hewill remember and always give you meat.\r\n", "[MOTHER] There were leaders before there was fire. Fire will kill us allin the end. You should have killed the four strangers. Kill them.\r\n", "[<PERSON><PERSON>] I have said we will wait until <PERSON><PERSON> shines again. Then they die.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "[IAN] Are you all right? Did they hurt you?\r\n", "[BARBARA] No<PERSON> <PERSON>, I'm frightened.\r\n", "[IAN] Try and hang on.\r\n", "[BARBARA] But how are we going to get out of this?\r\n", "[DOCTOR] We should use our cunning. I hope you can get yourself free,<PERSON><PERSON>. I can't. The stench in here. The stench. I'm sorry. It'sall my fault. I'm desperately sorry.\r\n", "[<PERSON><PERSON><PERSON>] Don't blame yourself, Grand<PERSON>.\r\n", "[DOCTOR] Look at that. Look at it. (skulls)\r\n", "[IAN] They're all the same. They've been split open.\r\n", "[DOCTOR] Oh, I'm sorry. I'm sorry, it's all myfault. I'm desperately sorry.\r\n", "[S<PERSON><PERSON>] Oh, don't blame yourself, Grand<PERSON>.\r\n", "[DOCTOR] Look at those. Look at them. (the skulls)\r\n", "[IAN] Yes, they're all the same. They've been split wide open.\r\n", "[SUSAN] I've found another piece with a rough edge.\r\n", "[IAN] Thank you.\r\n", "[IAN] It's no good, it keeps crumbling.\r\n", "[DOCTOR] Oh, it's hopeless, hopeless. Even if we do get free, we shallnever move that stone.\r\n", "[IAN] There's air coming in here from somewhere.\r\n", "[BARBARA] Yes, there is. I can feel it on my face.\r\n", "[IAN] It may only be a small opening. Don't count on it.\r\n", "[DOCTOR] Well you obviously are.\r\n", "[IAN] Of course I am. Any hope is better than none. Don't just lie therecriticising us. Do something. Help us all to get out of here. Oh, thisstone's no good.\r\n", "[BARBARA] Well, don't give up, <PERSON>. Please.\r\n", "[IAN] Oh, all right.\r\n", "[DOCTOR] No, no, don't waste time. Try those bones, they may be. They'resharper, perhaps.\r\n", "[IAN] That's a good idea.\r\n", "[S<PERSON><PERSON>] Grandfather, I knew you'd think of something.\r\n", "[DOCTOR] We must all take it in turns and try and cut his hands free.\r\n", "[IAN] Surely we should get the girls free?\r\n", "[DOCTOR] No, no, we've got to free you first. You're the strongest, andyou may have to defend us.\r\n", "[IAN] All right.\r\n", "[DOCTOR] <PERSON>, you have a go. My arms are tired.\r\n", "[SUSAN] All right.\r\n", "[DOCTOR] And don't think of failing.\r\n", "[BARBARA] What?\r\n", "[DOCTOR] Well, try and remember, if you can, how you and the othersfound your way here. Concentrate on that please.\r\n", "[BARBARA] Yes, yes, I'll try. You're trying to help me.\r\n", "[DOCTOR] Fear makes companions of all of us. That's right.\r\n", "[BARBARA] I never thought once you were afraid.\r\n", "[DOCTOR] Fear is with all of us, and always will be. Just like thatother sensation that lives with it.\r\n", "[BARBARA] What's that?\r\n", "[DOCTOR] Your companion referred to it. <PERSON><PERSON>, that's right.\r\n", "[MOTHER] You will not make fire.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Outside the cave)\r\n", "[ZA] Now tell me.\r\n", "[HUR] I saw the old woman take your knife.\r\n", "[<PERSON><PERSON>] Why did you let her? She's old, you could have held her.\r\n", "[HUR] Why did she take it?\r\n", "[ZA] She has gone into the forest.\r\n", "[HUR] No. She's going to kill the strangers.\r\n", "[ZA] Did she say this?\r\n", "[HUR] No, but she took your knife. She is afraid of fire.\r\n", "[ZA] You should have stopped her.\r\n", "[HUR] <PERSON><PERSON> was in the cave. Leaders are awake when others sleep. Thestrange tribe will not be able to show you how to make fire if the oldwoman kills them.\r\n", "[Z<PERSON>] If I stop her from killing them, they will give fire to me. And notto <PERSON><PERSON>. The woman could not have got into the cave. The great stone isstill there. Why do you tell me this?\r\n", "[HUR] No! No!\r\n", "[ZA] The old woman is talking to them.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "[MOTHER] I will set you free if you will go awayand not make fire. Fire will bring trouble and death to the tribe.\r\n", "[DOCTOR] There will be no fire.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Outside the cave)\r\n", "[HUR] No, we cannot move the great stone.\r\n", "[ZA] The old woman is talking to them. I will move it.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "[MOTHER] Hurry, hurry. You must go across the tuft and into the trees.\r\n", "[IAN] Yes.\r\n", "[HUR] She set them free!\r\n", "[MOTHER] They would have made fire. They would have made fire! No.\r\n", "[ZA] They have gone into the night.\r\n", "[HUR] They have taken fire with them.\r\n", "[ZA] The beasts will kill them. They will kill us if we follow.\r\n", "[HUR] Now, you are leader. You are as strong as the beasts. You will bestronger still when you know how fire is made. Stronger than <PERSON><PERSON>.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Forest)\r\n", "[DOCTOR] Stop. Just a minute, let me get my\r\n", "[IAN] We can't stop here.\r\n", "[DOCTOR] Just a moment.\r\n", "[IAN] Look, we've got to go further on.\r\n", "[DOCTOR] I know. I know that. But I must get. Breathe. I must breathe.\r\n", "[IAN] Try. Try. I shall have to carry you.\r\n", "[DOCTOR] There's no need for that. Don't be so childish. I'm not senile.Just let me get my breath for a moment.\r\n", "[SUSAN] Oh, Grandfather, come on.\r\n", "[DOCTOR] Yes. I'm not so young, you know.\r\n", "[BARBARA] Are you sure this is the right way?\r\n", "[IAN] Yes, I think so.\r\n", "[BARBARA] I can't remember. I simply can't remember.\r\n", "[IAN] We're free, <PERSON>. Think about that. Free.\r\n", "[BARBARA] Yes. Yes.\r\n", "[SUS<PERSON>] I'm sure I remember this place. But we didn't come round it, wewent across it.\r\n", "[BARBARA] Yes, there was a sort of trail.\r\n", "[IAN] If that's true, we must be quite near the ship. How are youfeeling?\r\n", "[DOCTOR] I'm all right. Don't keep on looking upon me as the weakestlink of the party.\r\n", "[IAN] What's the matter?\r\n", "[BARBARA] I don't know. I saw something. Over there in the bushes.\r\n", "[DOCTOR] What nonsense.\r\n", "[BARBARA] The bushes moved. I saw them. I saw them! Oh, we're nevergoing to get out of this awful place! Never! Never! Never!\r\n", "[I<PERSON>] <PERSON>.\r\n", "[SUSAN] What do you think it could have been, Grand<PERSON>?\r\n", "[DOCTOR] Oh, sheer nonsense, child. Imagination.\r\n", "[BARBARA] We'll die in this place.\r\n", "[<PERSON><PERSON>] <PERSON>, no we won't. We're going to get back to the ship and thenwe'll be safe.\r\n", "[BARBARA] Oh, <PERSON>, what's happening to us?\r\n", "[IAN] <PERSON>, <PERSON>, we got out of the cave, didn't we?\r\n", "[SUS<PERSON>] I'm so cold.\r\n", "[DOCTOR] I'm hot with all this exertion.\r\n", "[IAN] We'll rest for a couple of minutes.\r\n", "[SUSAN] Oh, good. Is there any chance of them following us?\r\n", "[DOCTOR] I expect so.\r\n", "[IAN] Yes, that's why I don't want to stop here too long.\r\n", "[DOCTOR] Do you think I want to?\r\n", "[IAN] No. We'll change the order. <PERSON> and <PERSON> go in front, <PERSON> and<PERSON>'ll bring up the rear. <PERSON> seems to remember the way better than anyof us.\r\n", "[DOCTOR] You seem to have elected yourself leader of this little party.\r\n", "[IAN] There isn't time to vote on it.\r\n", "[DOCTOR] Just so long as you understand I won't follow your ordersblindly.\r\n", "[IAN] If there were only two of us, you could find your own way back tothe ship.\r\n", "[DOCTOR] Aren't you a tiresome young man!\r\n", "[IAN] And you're a stubborn old man. But you will lead, the girls inbetween, and I'll bring up the rear. Because that's the safest way.<PERSON> was probably right. I thought we heard something when westopped back there.\r\n", "[DOCTOR] Oh, sheer imagination.\r\n", "[IAN] Why are you so confident about it?\r\n", "[DOCTOR] I won't allow myself to be frightened out of my wits by mereshadows, that's all.\r\n", "[IAN] All right.\r\n", "[HUR] Look, there is a branch broken.\r\n", "[ZA] They have strange feet.\r\n", "[HUR] They wear skins on their feet.\r\n", "[ZA] There are marks here.\r\n", "[HUR] They've gone this way.\r\n", "[ZA] It was wrong to do this. We should not have followed them.\r\n", "[HUR] We cannot turn back now.\r\n", "[IAN] I think we'd better get going. <PERSON>, will you lead?\r\n", "[DOCTOR] Yes, yes, yes, yes.\r\n", "[<PERSON><PERSON>] Come on, <PERSON>.\r\n", "[IAN [OC]] <PERSON>, <PERSON>.\r\n", "[Z<PERSON>] Down here. That was one of the women.\r\n", "[SUSAN] A dead animal.\r\n", "[DOCTOR] It must have just been killed. By a larger animal, too.\r\n", "[IAN] <PERSON><PERSON>. <PERSON><PERSON>. That must be them. They followed us. Quick, quick.Over there.\r\n", "[IAN] Keep down, and not a sound.\r\n", "[Z<PERSON>] Wait. There is danger. I will go.\r\n", "[IAN] Quick, now's our chance. Let's get away. Run!\r\n", "[BARBARA] Look at them. We can't just leave them! I don't care whatthey've done.\r\n", "[IAN] <PERSON><PERSON>, come on.\r\n", "[BARBARA] I think he's dead. There isn't any danger.\r\n", "[IAN] <PERSON>, for heaven's sake.\r\n", "[BARBARA] No.\r\n", "[<PERSON><PERSON><PERSON>] I'm going too.\r\n", "[DOCTOR] Susan! You stay here with me.\r\n", "[SUSAN] No, Grandfather. We can't leave them.\r\n", "[DOCTOR] Silence! We're going back to the ship.\r\n", "[SUSAN] No!\r\n", "[DOCTOR] What are you doing? They must be out of their minds.\r\n", "[HUR] No, keep away.\r\n", "[IAN] Let me look at him.\r\n", "[HUR] No.\r\n", "[IAN] I am your friend. You understand? Friend. I want to help him.\r\n", "[HUR] Friend?\r\n", "[IAN] I want water.\r\n", "[HUR] Water.\r\n", "[IAN] Go and fetch some water for his wounds.\r\n", "[HUR] Water is there.\r\n", "[BARBARA] Please, show me. Give me your handkerchief.\r\n", "[IAN] Here you are.\r\n", "[SUS<PERSON>] Is he all right?\r\n", "[IAN] I think so. He must have buried his axe head in the animal. Thankyou.\r\n", "[HUR] Water comes out of the skin!\r\n", "[IAN] Yes. I think most of this is the animal's blood.\r\n", "[SUSAN] Good.\r\n", "[BARBARA] There's a scar on the side of his head.\r\n", "[IAN] Well, we've lost our chance of getting away. Your flat must belittered with stray cats and dogs.\r\n", "[BARBARA] These are human beings, <PERSON>.\r\n", "[IAN] Yes, I know.\r\n", "[DOCTOR] What exactly do you think you're doing?\r\n", "[IAN] Have you got any antiseptic in the ship?\r\n", "[SUSAN] Yes, lots.\r\n", "[DOCTOR] One minute ago we were trying desperately to get away fromthese savages.\r\n", "[IAN] All right, now we're helping them. You're a doctor, do something.\r\n", "[DOCTOR] I'm not a doctor of medicine.\r\n", "[<PERSON><PERSON><PERSON>] Grandfather, we can make friends with them.\r\n", "[DOCTOR] Oh, don't be ridiculous, child.\r\n", "[BARBARA] Why? You treat everybody and everything as something lessimportant than yourself.\r\n", "[DOCTOR] You're trying to say that everything you do is reasonable, andeverything I do is inhuman. Well, I'm afraid your judgement's at fault,<PERSON>, not mine. Haven't you realised if these two people canfollow us, any of these people can follow us? The whole tribe mightdescend upon us at any moment.\r\n", "[HUR] The tribe is asleep.\r\n", "[DOCTOR] And what about the old woman who cut our bonds? You understand?\r\n", "[IAN] He's right. We're too exposed here. We'll make a stretcher andcarry him.\r\n", "[DOCTOR] You're not going to take him back to the ship?\r\n", "[IAN] Take your coat off, <PERSON>. <PERSON>, try and find me two poles.Long ones, fairly straight.\r\n", "[BARBARA] The old woman won't give us away. She helped.\r\n", "[DOCTOR] Do you think so? These people have logic and reason, have they?Can't you see their minds change as rapidly as night and day? She'sprobably telling the whole tribe at this very moment.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "[KAL] The creatures. Where? Where?\r\n", "[MOTHER] Gone.\r\n", "[KAL] The great stone. They could not move it.\r\n", "[MOTHER] <PERSON><PERSON> moved it.\r\n", "[KAL] <PERSON><PERSON> has gone with them?\r\n", "[MOTHER] <PERSON><PERSON> and <PERSON><PERSON> went after them.\r\n", "[<PERSON><PERSON>] There were skins around their hands and their feet. They could notmove. <PERSON><PERSON> helped them to get free? They're gone with <PERSON><PERSON> to show himfire?\r\n", "[MOTHER] They won't make fire. There won't be fire any more.\r\n", "[K<PERSON>] Old woman, you helped them.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Forest)\r\n", "[IAN] It's not going to work like this. What can wedo?\r\n", "[HUR] No! He's mine.\r\n", "[SUSAN] I was only trying to help.\r\n", "[IAN] She doesn't understand, <PERSON>. She's jealous of you.\r\n", "[HUR] I don't understand what you are doing. You are like a mother witha child. Why do you not kill?\r\n", "[IAN] How can we explain to her? She doesn't understand kindness,friendship.\r\n", "[BARBARA] We will make him well again. We will teach you how to makefire. In return, you show us the way back to our cave.\r\n", "[ZA] Listen to them. They do not kill.\r\n", "[IAN] Come on. Let's get on with this stretcher. Let's try the sleevesinside. That's it.\r\n", "[ZA] Water.\r\n", "[HUR] Water.\r\n", "[IAN] How about giving us a hand, <PERSON>?\r\n", "[<PERSON><PERSON><PERSON>] He's always like this if he doesn't get his own way.\r\n", "[BARBARA] The old woman won't give us away. And now that we've got thesetwo on our side, we should get back to the ship.\r\n", "[SUSAN] Yes.\r\n", "[DOCTOR] Get your hand off me.\r\n", "[IAN] What are you doing?\r\n", "[DOCTOR] Well, I, I was going to get him to draw our way back to theTardis.\r\n", "[IAN] We've been too long as it is. Is the stretcher ready?\r\n", "[BARBARA] Yes.\r\n", "[IAN] Right, you take one end of it.\r\n", "[DOCTOR] You don't expect me to carry him, do you?\r\n", "[IAN] Do you want the women to do the job for you?\r\n", "[DOCTOR] Oh, very well.\r\n", "[IAN] Right, now move him over very carefully. Now, back again, gently.\r\n", "[IAN] Good. Right, now, <PERSON>, you get in front with her.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave)\r\n", "[KAL] They have gone. <PERSON><PERSON> and <PERSON><PERSON> have gone with<PERSON> and we must go after them.\r\n", "[HORG] <PERSON><PERSON> would not help them to get away.\r\n", "[KAL] She has gone with them.\r\n", "[HORG] The old woman sleeps in the cave too, and she has gone.\r\n", "[KAL] The old woman is in the Cave of Skulls.\r\n", "[HORG] <PERSON><PERSON> would not go with them!\r\n", "[<PERSON><PERSON>] Ask the old woman. She will tell what is done.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "[KAL] She will tell.\r\n", "[KAL] My eyes tell me what has happened. As they do when I sleep and Isee things. <PERSON><PERSON> and <PERSON><PERSON> came to free them, and find a way to make fire.The old woman saw them. <PERSON><PERSON> killed old woman.\r\n", "[HORG] The old woman is dead. It must have been as your eyes said itwas.\r\n", "[K<PERSON>] <PERSON><PERSON> has gone with them, taking them to their cave. <PERSON><PERSON> takes awayfire. Now <PERSON>, <PERSON><PERSON>, lead. Go!\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Forest)\r\n", "[<PERSON><PERSON>] Hold the branches back, <PERSON>.\r\n", "[SUSAN] The Tardis! There's the Tardis!\r\n", "[IAN] Back! Back! Go back.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Forest)\r\n", "[IAN] Back. Get back.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave)\r\n", "[HORG] They are coming.\r\n", "[<PERSON><PERSON>] <PERSON><PERSON> and the woman went with them. I, <PERSON><PERSON>, stop them.\r\n", "[HUR] They saved <PERSON><PERSON> from death near the stream.\r\n", "[KAL] They set them free from the Cave of Skulls and went with them.\r\n", "[HUR] The old woman cut them free.\r\n", "[K<PERSON>] <PERSON><PERSON> is so weak a woman speaks for him.\r\n", "[HUR] It was the old woman. She showed them a new way out of the Cave ofSkulls.\r\n", "[KAL] The old woman does not speak. She does not say she did this or didthat. The old woman is dead. <PERSON><PERSON> killed the old woman.\r\n", "[HUR] No!\r\n", "[KAL] <PERSON><PERSON> killed the old woman with his knife.\r\n", "[HUR] No.\r\n", "[KAL] Here. Here is the knife he killed her with.\r\n", "[DOCTOR] This knife has no blood on it. I said, this knife has no bloodon it.\r\n", "[KAL] It is a bad knife. It does not show the things it does.\r\n", "[DOCTOR] It is a finer knife than yours.\r\n", "[K<PERSON>] I, <PERSON><PERSON>, say it is a bad knife.\r\n", "[DOCTOR] This knife can cut and stab. I have never seen a better knife.\r\n", "[K<PERSON>] I will show you one.\r\n", "[DOCTOR] This knife shows what it has done. There is blood on it. (<PERSON><PERSON><PERSON>) Who killed the old woman?\r\n", "[Z<PERSON>] I did not kill her.\r\n", "[DOCTOR] (to <PERSON><PERSON>) You killed the old woman.\r\n", "[<PERSON><PERSON>] Yes! She set them free. She set them free. She did this. I, <PERSON><PERSON>,killed her.\r\n", "[DOCTOR] Is this your strong leader? One who kills your old women? He isa bad leader. He will kill you all. Yes, all. (to <PERSON>) Follow myexample.\r\n", "[DOCTOR] Drive him out. Out.\r\n", "[IAN] Yes, drive him out. He killed the old woman.\r\n", "[TRI<PERSON>] Drive him out.\r\n", "[IAN] Remember, <PERSON><PERSON> is not stronger than the whole tribe.\r\n", "[Z<PERSON>] <PERSON><PERSON> is no longer one of this tribe. We will watch for him. We willall fight <PERSON><PERSON> if he comes back. We will watch for him. Take them to theCave of Skulls.\r\n", "[IAN] Take us back to the desert and we will make fire for you.\r\n", "[ZA] The great stone will close one place, and you will stand by anotherI will show you. Take them.\r\n", "[DOCTOR] Don't struggle.\r\n", "[ZA] They are inside the cave. You see them come out, kill them.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "[DOCTOR] This place is evil.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave)\r\n", "[Z<PERSON>] Tell me what happened after I fought the beastin the forest.\r\n", "[HUR] You were stronger than the beast. It took away your axe in itshead. You lay on the earth. I believed you were dead.\r\n", "[ZA] Tell me what they did.\r\n", "[HUR] The young man of their tribe came towards you, but he did notkill. He told me his name.\r\n", "[ZA] Name?\r\n", "[HUR] His name is <PERSON>.\r\n", "[ZA] They come from the other side of the mountains.\r\n", "[HUR] Nothing lives there.\r\n", "[ZA] There are other tribes there. This new tribe must come from there.Tell me more of what happened.\r\n", "[HUR] I did not understand them. Their hands moved slowly and theirfaces were not fierce. It was like a mother guarding her baby.\r\n", "[ZA] They are a new tribe. Not like us. Not like <PERSON><PERSON>. The young one,whose name is <PERSON>, spoke to me.\r\n", "[HUR] Do you remember it?\r\n", "[ZA] He said, <PERSON><PERSON> is not stronger than the whole tribe.\r\n", "[HUR] I do not understand.\r\n", "[ZA] The whole tribe drove <PERSON><PERSON> away with the stones. The whole tribe cancollect more fruit than one. The whole tribe can kill a beast where oneof the tribe would die.\r\n", "[HUR] Do you think they come from Orb?\r\n", "[ZA] No. They are a tribe who know how fire is made, but they do notwant to tell us.\r\n", "[HUR] Then you will not kill them?\r\n", "[<PERSON><PERSON>] <PERSON><PERSON> says the leader must know how fire is made. I do not want tobe driven into the forest like <PERSON><PERSON>. I must make fire, or they must die,as the old men say. I will speak with him. I must hear more things toremember. The leader would have things to remember.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "[SUSAN] I think this is what you want, <PERSON>.\r\n", "[IAN] Thank you.\r\n", "[BARBARA] Here are some leaves, and some dead grass.\r\n", "[IAN] Yes, well spread them around the hole. Don't put them inside. Ihope this is going to work. Now, spread them around a bit more. Yes,that's it.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Outside the second exit)\r\n", "[ZA] (to guard) I will speak with them. You waithere.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "[SUSAN] I can smell something.\r\n", "[BARBARA] Yes, so can I.\r\n", "[SUSAN] It's burning! It's burning!\r\n", "[IAN] It's a long way off yet.\r\n", "[ZA] What is this?\r\n", "[DOCTOR] We are making fire.\r\n", "[ZA] You are called Friend?\r\n", "[IAN] Yes.\r\n", "[DOCTOR] Don't stop.\r\n", "[Z<PERSON>] <PERSON><PERSON> said you were called <PERSON>. I am called <PERSON><PERSON>. You are the leaderof your tribe?\r\n", "[IAN] No. He is our leader.\r\n", "[SUSAN] Are you going to set us free?\r\n", "[ZA] The tribe say you are from Orb and when you are returned to him onthe stone of death, we will have fire again.\r\n", "[BARBARA] But that's not true.\r\n", "[ZA] I think you are from the other side of the mountains. If you showme how to make fire, I will take you back to the foot of the mountains.If you do not show me, I cannot stop you dying on the old stone.\r\n", "[IAN] Put some more leaves and grass round it. I think it's beginning towork.\r\n", "[DOCTOR] Do you understand? We are making fire for you.\r\n", "[Z<PERSON>] I am watching.\r\n", "[IAN] The whole tribe should be watching. Everyone should know how tomake fire.\r\n", "[ZA] Everyone cannot be leader.\r\n", "[IAN] No, that's perfectly true. But in our tribe, the firemaker is theleast important man.\r\n", "[ZA] Ha! I do not believe this.\r\n", "[DOCTOR] He is the least important because we can all make fire.\r\n", "[<PERSON><PERSON><PERSON>] I hope he doesn't make <PERSON><PERSON> prove that.\r\n", "[IAN] Look, I think it's beginning to work. <PERSON>, <PERSON>, blow gently.That's it!\r\n", "[SUSAN] We've done it.\r\n", "[IAN] Yes.\r\n", "[ZA] Fire. Fire.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave)\r\n", "[HORG] <PERSON><PERSON> strikes the old stone and <PERSON><PERSON> does notbring them out. We have no meat, and no fruit from the trees, and noroots. <PERSON><PERSON> is no leader.\r\n", "[HUR] <PERSON><PERSON> would kill you if he could hear you. He's talking now with themin the Cave of Skulls. You should lie on the old stone until your bloodruns into the earth.\r\n", "[HORG] <PERSON><PERSON> is letting them go away, just as the old woman set them free.\r\n", "[HUR] <PERSON><PERSON> told one of them to watch and guard them until he came out ofthe Cave of Skulls.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "(Cave)\r\n", "[HORG] <PERSON><PERSON> is above us and there is no fire. Bringthem out from the Cave of Skulls and Za as well. Come on.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "[IAN] Take this, and show it to your tribe.\r\n", "[ZA] You, stay here.\r\n", "[IAN] We will come with you.\r\n", "[ZA] No, you'll stay here.\r\n", "[IAN] I will come with you.\r\n", "[DOCTOR] Give him a chance. Give him a chance. Let him show the tribefire, establish himself as leader, then he'll let us go.\r\n", "[IAN] But we ought to go with him now.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave)\r\n", "[ZA] Fire.\r\n", "[TRIBE] Fire.\r\n", "[<PERSON><PERSON>] <PERSON><PERSON> is dead. I give you fire. I am leader. We will give food andwater to the new tribe in the Cave of Skulls.\r\n", "[HORG] There is no meat.\r\n", "[ZA] I will go into the forest and get meat.\r\n", "[HORG] Yes. I remember how the meat and fire joined together.\r\n", "[ZA] Good. Watch the new tribe. They must be here when I return.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Cave of Skulls)\r\n", "[IAN] It didn't work. They're going to keep ushere.\r\n", "[IAN] Why are you keeping us here?\r\n", "[HUR] <PERSON><PERSON> has gone into the forest to find meat. There will be more foodlater.\r\n", "[BARBARA] But why can't we go outside?\r\n", "[SUS<PERSON>] Please let us go. It's terrible in here.\r\n", "[HUR] <PERSON><PERSON> is leader.\r\n", "[SUS<PERSON>] But we helped you. We gave you fire.\r\n", "[HUR] We have fire now.\r\n", "[IAN] Yes. And I was the fool who gave it to you. Why didn't I wait?\r\n", "[BARBARA] Well at least we're alive. We wouldn't be if we hadn't giventhem fire.\r\n", "[BARBARA] Ian.\r\n", "[SUSAN] They brought us some meat.\r\n", "[BARBARA] And the Doctor found a stone with a hole in it, and theyfilled it with water.\r\n", "[IAN] All the comforts of home.\r\n", "[ZA] The animal was hard to kill. The meat on it is good. They havebrought you fruit and water has been put into a stone. Is this thestone? Has anyone hurt you?\r\n", "[DOCTOR] When are you going to let us go, hmm?\r\n", "[ZA] You will stay here. I have the meat and I have the stick, and apiece of skin. I can make fire now. Your tribe and my tribe will jointogether.\r\n", "[IAN] We don't want to stay here.\r\n", "[ZA] Why? There is no better place the other side of the mountains. <PERSON><PERSON> try to leave here.\r\n", "[DOCTOR] Quench the fire. Take the fire away from them. Scaring them,somehow.\r\n", "[SUSAN] (putting a skull on top of a burning brand) Hey, <PERSON><PERSON>,look! It's almost alive.\r\n", "[IAN] Not alive, <PERSON>. Almost dead. We're going to make four torches.We'll find the sticks. And we'll use the fat from the meat. And then\r\n", "[SUSAN] And then?\r\n", "[IAN] And then, to all intents and purposes, we're going to die.\r\n", "[IAN] When I give the sign.\r\n", "[SUSAN] What are they?\r\n", "[ZA] Look. It is nothing but fire and the bones of the dead. They havegone! While we look at their fire, they have gone!\r\n", "[HUR] Into the night. The dark will hide them.\r\n", "[ZA] With fire, it is day.\r\n", "\r\n", "---------------------------------------\r\n", "\r\n", "(Desert)\r\n", "(<PERSON><PERSON><PERSON>)\r\n", "[IAN] Come on, <PERSON>, get us off! Get us off!\r\n", "[DOCTOR] Yes.\r\n", "[DOCTOR] Yes, it's matching up.\r\n", "[SUSAN] We're beginning to land.\r\n", "[DOCTOR] Oh, how I wish.\r\n", "[IAN] Have you taken us back to our own time?\r\n", "[DOCTOR] You know I can't do that. Please be reasonable.\r\n", "[IAN] What?\r\n", "[BARBARA] Please, you must take us back. You must.\r\n", "[DOCTOR] You see, this isn't operating properly. Or rather, the code isstill a secret. When you put the right data, precise information to asecond of the beginning of a journey, then we can fix a destination,but I had no data at my disposal.\r\n", "[BARBARA] Are you saying that you don't know how to work this thing?\r\n", "[DOCTOR] Well of course I can't. I'm not a miracle worker.\r\n", "[SUSAN] You can't blame <PERSON><PERSON>. We left the other place tooquickly, that's all.\r\n", "[IAN] Just a minute. Did you try and take us back to our own time?\r\n", "[DOCTOR] Well, I got you away from that other time, didn't I?\r\n", "[IAN] That isn't what I asked you.\r\n", "[DOCTOR] It's the only way I can answer you, young man. Now. Now weshall see.\r\n", "[DOCTOR] It could be anywhere. Dear, dear, dear, dear. It's no help tous at all. Well, I suggest before we go outside and explore, let usclean ourselves up.\r\n", "[SUSAN] Oh, yes.\r\n", "[DOCTOR] Now what does the radiation read, <PERSON>?\r\n", "[SUSAN] It's reading normal, Grandfather.\r\n", "\n"]}], "source": ["print(doctor[\"TEXT\"].values[0])"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>METADATA</th>\n", "      <th>SOURCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Doctor Who (23 Nov, 1963; First Doctor) - An U...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Doctor Who (21 Dec, 1963; First Doctor) - The ...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Doctor Who (8 Feb, 1964; First Doctor) - The E...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Doctor Who (22 Feb, 1964; First Doctor) - <PERSON>...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Doctor Who (11 Apr, 1964; First Doctor) - The ...</td>\n", "      <td>{\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...</td>\n", "      <td>drwho/jean<PERSON>dev</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                TEXT  \\\n", "0  Doctor Who (23 Nov, 1963; First Doctor) - An U...   \n", "1  Doctor Who (21 Dec, 1963; First Doctor) - The ...   \n", "2  Doctor Who (8 Feb, 1964; First Doctor) - The E...   \n", "3  Doctor Who (22 Feb, 1964; First Doctor) - <PERSON>...   \n", "4  Doctor Who (11 Apr, 1964; First Doctor) - The ...   \n", "\n", "                                            METADATA           SOURCE  \n", "0  {\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...  drwho/jeanmidev  \n", "1  {\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...  drwho/jeanmidev  \n", "2  {\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...  drwho/jeanmidev  \n", "3  {\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...  drwho/jeanmidev  \n", "4  {\"show\": \"Doctor Who\", \"season\": \"s01\", \"episo...  drwho/jeanmidev  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["doctor.to_parquet(\"drwho.pq\", row_group_size=100, engine=\"pyarrow\", index=False)\n", "doctor.head()  # https://www.kaggle.com/datasets/jeanmidev/doctor-who?select=all-scripts.csv"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["306"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["len(doctor)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Star Trek"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['DS9', 'TOS', 'TAS', 'TNG', 'VOY', 'ENT'])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Star Trek via http://www.chakoteya.net/StarTrek/index.html and also https://github.com/GJBroughton/Star_Trek_Scripts/\n", "r = requests.get(\"https://github.com/GJBroughton/Star_Trek_Scripts/raw/master/data/all_scripts_raw.json\")\n", "trek = r.json()\n", "trek.keys()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████████████████| 6/6 [00:22<00:00,  3.68s/it]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>METADATA</th>\n", "      <th>SOURCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>The Deep Space Nine - Emissary\\n\\nEmissary\\nSt...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>The Deep Space Nine - Past Prologue\\n\\nPast\\nP...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>The Deep Space Nine - A Man Alone\\n\\nA\\nMan Al...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>The Deep Space Nine - Babel\\n\\nBabel\\nStardate...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>The Deep Space Nine - Captive Pursuit\\n\\nCapti...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>703</th>\n", "      <td>The Enterprise - In A Mirror, Darkly - part 1\\...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"ENT\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>704</th>\n", "      <td>The Enterprise - In A Mirror, Darkly - part 2\\...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"ENT\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>705</th>\n", "      <td>The Enterprise - Demons\\n\\nDemons\\n[Mission Da...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"ENT\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>706</th>\n", "      <td>The Enterprise - Terra Prime\\n\\nTerra\\nPrime\\n...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"ENT\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>707</th>\n", "      <td>The Enterprise - These Are The Voyages...\\n\\nT...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"ENT\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>708 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                                                  TEXT  \\\n", "0    The Deep Space Nine - Emissary\\n\\nEmissary\\nSt...   \n", "1    The Deep Space Nine - Past Prologue\\n\\nPast\\nP...   \n", "2    The Deep Space Nine - A Man Alone\\n\\nA\\nMan Al...   \n", "3    The Deep Space Nine - Babel\\n\\nBabel\\nStardate...   \n", "4    The Deep Space Nine - Captive Pursuit\\n\\nCapti...   \n", "..                                                 ...   \n", "703  The Enterprise - In A Mirror, Darkly - part 1\\...   \n", "704  The Enterprise - In A Mirror, Darkly - part 2\\...   \n", "705  The Enterprise - Demons\\n\\nDemons\\n[Mission Da...   \n", "706  The Enterprise - Terra Prime\\n\\nTerra\\nPrime\\n...   \n", "707  The Enterprise - These Are The Voyages...\\n\\nT...   \n", "\n", "                                              METADATA              SOURCE  \n", "0    {\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...  startrek/chakoteya  \n", "1    {\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...  startrek/chakoteya  \n", "2    {\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...  startrek/chakoteya  \n", "3    {\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...  startrek/chakoteya  \n", "4    {\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...  startrek/chakoteya  \n", "..                                                 ...                 ...  \n", "703  {\"show\": \"Star Trek\", \"season\": \"ENT\", \"episod...  startrek/chakoteya  \n", "704  {\"show\": \"Star Trek\", \"season\": \"ENT\", \"episod...  startrek/chakoteya  \n", "705  {\"show\": \"Star Trek\", \"season\": \"ENT\", \"episod...  startrek/chakoteya  \n", "706  {\"show\": \"Star Trek\", \"season\": \"ENT\", \"episod...  startrek/chakoteya  \n", "707  {\"show\": \"Star Trek\", \"season\": \"ENT\", \"episod...  startrek/chakoteya  \n", "\n", "[708 rows x 3 columns]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["picard = {\"TEXT\": [], \"METADATA\": [], \"SOURCE\": []}\n", "for series in tqdm(trek.keys()):\n", "    for episode in trek[series].keys():\n", "        script = trek[series][episode].replace(\"[\", \"(\").replace(\"]\", \")\").strip()\n", "        try:\n", "            title = \" \".join(re.findall(r\"^.+?\\-\\s*(.+?)\\r?\\n\", script)[0].splitlines()).strip()\n", "        except IndexError:\n", "            title = \" \".join(script.split(\"Stardate:\")[0].splitlines()).strip()\n", "        metadata = {\n", "            \"show\": \"Star Trek\",\n", "            \"season\": series,\n", "            \"episode\": f\"e{episode.split()[1].z<PERSON>(2)}\",\n", "            \"title\": title,\n", "        }\n", "        text = \"\"\n", "        for i, line in enumerate(script.splitlines()):\n", "            if i == 0:\n", "                text += re.sub(r\"(?i)((?:trans?)scripts?)\\s*\", \"\", line.strip()) + \"\\r\\n\"\n", "                continue\n", "            if line == \"<Back\":\n", "                break\n", "            match = re.findall(r\"(?i)\\s*([\\w\\d\\s\\.]+(?:\\([\\w\\d\\s\\.]+\\))?)\\s*\\:\\s*(.+?)$\", line)\n", "            if match:\n", "                speaker, voice = match[0]\n", "                if speaker not in (\"Stardate\", \"Original Airdate\"):\n", "                    text += f\"[{speaker}] {voice}\\r\\n\"\n", "                else:\n", "                    text += f\"{line.strip()}\\r\\n\"\n", "            else:\n", "                text += f\"{line.strip()}\\r\\n\"\n", "\n", "        text = text.strip().replace(\"&amp;\", \"&\")\n", "        text = \"\\r\\n\".join(text.splitlines())\n", "        text = re.sub(r\"(\\r*\\n)\", \"\\n\", text)\n", "        text = re.sub(r\"\\n{2,}\", \"\\n\\n\", text).strip()\n", "\n", "        picard[\"TEXT\"].append(text)\n", "        picard[\"METADATA\"].append(json.dumps(metadata))\n", "        picard[\"SOURCE\"].append(\"startrek/chakoteya\")\n", "\n", "picard = pd.DataFrame(picard)\n", "picard"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Next Generation - Time's Arrow part two\n", "\n", "Time's\n", "Arrow, part 2\n", "Stardate:\n", "46001.3\n", "Original Airdate: 21 Sep, 1992\n", "\n", "Last\n", "[the Next Generation. LAFORGE] They found <PERSON>'s head a mile beneath San Francisco. Been down\n", "there about five centuries.\n", "[DATA] At some future date I will be transported back to nineteenth\n", "century Earth, where I will die. It has occurred. It will occur.\n", "[GUINAN] Do I know you, Mister? \n", "[DATA] Data. Yes. We were on a ship together. The Enterprise. \n", "[GUINAN] Is that a clipper ship? \n", "[DATA] It is a starship. \n", "[CLEMENS] Starship? \n", "[RIKER] My God. They're delivering more of them for the others to\n", "ingest.\n", "[GUINA<PERSON>] Did my father send you here? Because if he did, you must go\n", "back and tell him I'm not done listening to\n", "[DATA] I was not sent by your father. Our ship encountered a species who\n", "appears to be threatening nineteenth century Earth.\n", "[<PERSON>IKE<PERSON>] I'm not willing to accept that he's dead and just leave it at\n", "that.\n", "[PICARD] We cannot make Mister Data our priority. \n", "[RIKER] What is more important than Data? \n", "[GUINAN] Do you remember the first time we met? \n", "[PICARD] Of course. \n", "[GUINAN] Don't be so sure. If you don't go on this mission, we'll never\n", "meet.\n", "And now, the conclusion.\n", "(Street)\n", "[C<PERSON><PERSON><PERSON>] I have long been interested in the notion\n", "of time travellers. In fact, I wrote a book about it. It chronicles the\n", "tale of a man of our era who fouled the sixth century by introducing\n", "newfangled gadgets and weapons all in the name of progress. This idea\n", "of time travel is not so far fetched as it might seem. In fact, I have\n", "learned that, even now, there are people from the future right here in\n", "San Francisco and I have no doubt that their intent is to foul our\n", "world just as my Yankee did in <PERSON>'s time. Well, sir, let me\n", "serve notice. As soon as I have the necessary evidence, I intend to\n", "expose them and make it absolutely clear that they are not welcome\n", "here.\n", "[REPORTER] Yes, sir. And will this be a sequel to Connecticut Yankee,\n", "Mister Twain?\n", "[CLEMENS] The name's <PERSON><PERSON><PERSON>, son. <PERSON>. That's with an e. \n", "[REPORTER] With an e. Got it. \n", "(<PERSON> comes out of his hotel)\n", "[C<PERSON><PERSON>NS] Excuse me. \n", "(As <PERSON><PERSON><PERSON> follows <PERSON>, the Devidians walk the other way)\n", "\n", "(Morgue)\n", "\n", "[RIKE<PERSON>] (dressed as a policeman) The coroner said\n", "this is where they kept all the cholera victims.\n", "[CRUSHER] (taking tricorder scans) It doesn't make sense that so many\n", "people are dying of cholera. It just isn't that virulent. Will, this is\n", "strange.\n", "[RIKER] What is it? \n", "[CRUSHER] The cerebellum, the cerebral cortex, the brain stem, the\n", "entire nervous system has been depleted of electrochemical energy.\n", "Here's another one. Same neural depletion. These people did not die of\n", "cholera. They died because their neural energy was drained somehow.\n", "[RIKER] Drained and taken to Devidia Two for those aliens to ingest? If\n", "you were a time traveller with a taste for human neural energy where\n", "would you get your supply?\n", "[CRUSHER] I would travel back to a time when there were plagues and\n", "epidemics, so I could murder and use disease as a cover.\n", "\n", "(Rented room)\n", "\n", "(all the away team are in period costume)\n", "[CRUSHER] Over half the victims whose neural energy had been drained\n", "came from the Sisters of Hope Infirmary.\n", "[RIKER] A charity hospital near the waterfront. \n", "[TROI] If the aliens have been killing people there, well, someone might\n", "have noticed something unusual.\n", "[PICARD] And if they're moving among humans, they must have taken on\n", "human form. But the question is, how do we know who we're looking for?\n", "[RIKER] The tricorder indicated triolic activity from most of the bodies\n", "we looked at. If the aliens left that sort of signature, they'll be\n", "detectable.\n", "[LAFORGE] You know, we might even be able to rig some kind of alien\n", "alarm system.\n", "[RIKER] Any luck contacting Data? \n", "[LAFORGE] None. The tricorder's broadcasting random emissions on all\n", "frequencies his sub-processors might pick up, but the range is limited.\n", "Almost anything could interfere.\n", "(there's a knock at the door)\n", "[LAFOR<PERSON>] She's back. \n", "(<PERSON><PERSON><PERSON> takes off his visor)\n", "[CARMICHAEL (OC)] Mister Pike<PERSON>! \n", "[PICARD] Yes, Mrs. <PERSON>. \n", "[CARMI<PERSON><PERSON><PERSON>] (Irish accent) Mister <PERSON><PERSON>. I'll be reminding you that\n", "it's one o'clock.\n", "[PICARD] Yes. \n", "[CARMICHAEL] One o'clock on a Thursday. I'm sure I made it clear to you\n", "that the rent is always due, payable in full, by one o'clock on\n", "Wednesdays.\n", "[PICARD] Ah, yes, er, the rent. Mrs. <PERSON>, even now my troupe are\n", "in rehearsals for a new production.\n", "[CARMICHAEL] Oh? I haven't heard of any new production. What play is it?\n", "\n", "[PICARD] The play? A Midsummer Night's Dream. We have performed in\n", "London, Paris, Milan. Milan. To sold out houses. I assure you, you will\n", "have the rent, in full, with a bonus.\n", "[CARMICHAEL] Oh, no, no, no, no. I've heard you silver-tongued devils\n", "before. I'll have the rent in full tomorrow by one o'clock or you'll be\n", "out performing on the street.\n", "\n", "(<PERSON>'s hotel room)\n", "\n", "[BELLBOY] I wouldn't do this for just anybody,\n", "Mister <PERSON><PERSON><PERSON>. And I hope you won't spread it around that I let you\n", "in. People start talking.\n", "(<PERSON><PERSON><PERSON> starts searching the room)\n", "[CLEMENS] Of course not, <PERSON>. And I assure you that Mister Data would\n", "be most upset if you didn't. If I can't find that Letter of Intent that\n", "he left me, our major investor is going to pull out and take his\n", "business elsewhere.\n", "[BELLBOY] It has to do with Mister Data's engine, doesn't it? \n", "[CLEMENS] Engine? \n", "[BELLBOY] Yes. \n", "[CLEMENS] Oh, yes. Yes, this is exactly what it's all about. Ow! \n", "(he gets a shock from it)\n", "[BELLBOY] You know, Mister <PERSON>, I'm going to do you another favour\n", "today. You're always looking for good stories, right? Well, I've got a\n", "real humdinger for you. The story of my life. Now, I know you may think\n", "I'm young, but I've covered a lot of ground and if I do say so myself,\n", "it'd make for some pretty fascinating reading. So, what do you think?\n", "[CLEMENS] About what? \n", "[BELLBOY] About writing my life story. You and me. Literary partners, of\n", "course.\n", "[<PERSON><PERSON><PERSON><PERSON>] Young man, I have a maxim that I have always lived by. No one\n", "is more qualified to write your story than you are.\n", "[BELLBOY] Me? Be a writer? You think I could do that? \n", "[CLEMENS] As long as you write what you know. You got any passions, boy?\n", "Any dreams?\n", "[BELLBOY] I'd like to do some travelling, maybe go to sea. And Alaska.\n", "I've had the strangest notion to go see Alaska.\n", "[C<PERSON><PERSON><PERSON>] That's a great idea, son. That's exactly what I would do if I\n", "were your age. Alaska, the Klondike, the Aurora Borealis. That's it.\n", "Follow your dreams and write about 'em.\n", "[BELLBOY] Thank you, Mister <PERSON>. You know, that is exactly what I'm\n", "going to do.\n", "[CLEMENS] You do that, son. \n", "[BELLBOY] You'll see my name in print, too. \n", "[CLEMENS] I'm sure I will. \n", "[BELLBOY] Don't forget. The name's London. Jack London. \n", "[C<PERSON>MENS] Goodbye now. Bye-bye. \n", "(and ushers <PERSON> out of the room and locks the door so he can examine\n", "the Contraption closely. There's the sound of a door, and <PERSON><PERSON><PERSON> hides\n", "in the wardroom. <PERSON> and <PERSON><PERSON><PERSON> enter)\n", "[G<PERSON>NA<PERSON>] I found the Head Surveyor, I found the cavern. You will not be\n", "able to get to it. The entrance is on a mine shaft that is the middle\n", "of the Presidio on an army base.\n", "[DATA] Perhaps you could arrange for us to get in. \n", "[GUINAN] How? \n", "[DATA] With permission to dig for the mine shaft. \n", "[GUINAN] Oh, no, <PERSON>. I've done everything you've asked \n", "[DATA] I have full confidence in your persuasive abilities. \n", "[GUINAN] Well, I suppose it's more interesting than throwing a tea\n", "dance.\n", "[DATA] Strange. The transceiver assembly has been removed. Without it I\n", "will be unable to track the time shifts.\n", "[<PERSON><PERSON><PERSON><PERSON>] Twain. \n", "[DATA] <PERSON><PERSON><PERSON>? \n", "[G<PERSON>NA<PERSON>] Yes. He's been driving me crazy. He watches the house, he\n", "follows me down the street asking me questions. If anyone took this you\n", "can believe it was him.\n", "[DATA] If you are correct, he must be warned. The device has been\n", "modified in such a way that prolonged contact with human tissue would\n", "be highly toxic.\n", "(there's a thump from the wardrobe. <PERSON> opens the door to reveal the\n", "guilty party. Data picks up the\n", "transceiver that <PERSON><PERSON><PERSON> dropped.)\n", "[CLEMENS] Madame <PERSON>. Mister Data. \n", "[GUINA<PERSON>] Shame on you, Mister <PERSON><PERSON><PERSON>. Shame. \n", "[C<PERSON>ME<PERSON>] Shame, madam? I think not. I find no shame in my efforts to\n", "uncover your plot.\n", "[GUINA<PERSON>] I keep telling you, there is no plot. \n", "[CLEMENS] Yes, you do keep telling me that. What an interesting pair you\n", "are. Where in Switzerland did you say you were from, Mister <PERSON>?\n", "[DATA] I am French, sir, not Swiss. \n", "[C<PERSON>MENS] Oh yes, that's right, now I remember. A Frenchman with a\n", "talent for poker, from what I hear. You know, I was talking to a friend\n", "of mine, <PERSON><PERSON>. He says to say hello and wonders when you're\n", "coming back.\n", "[DATA] I do not believe I know Mister <PERSON>. \n", "[CLEMENS] No? He works at the County Assayer's office. He says you've\n", "been in there a number of times claiming to be a geological engineer\n", "and wanting information about mining operations during the 1850's. You\n", "remember him now?\n", "[DATA] I am unfamiliar with the gentleman's name. I have spoken to\n", "several people at that office.\n", "[CLEMENS] Yes, I know. And in the Hall of Records, and in the Geological\n", "Society, and a little-known mineral shop in Chinatown. You do get\n", "around, don't you?\n", "[DATA] As apparently you do, Mister <PERSON>. \n", "[CLEMENS] I must admit you've got me mystified. This contraption, for\n", "instance. It's very unusual. It looks quite futuristic. Tell me, might\n", "it have something to do with time shifts?\n", "[DATA] In a sense. The time shift is a gearing system I have invented\n", "for the horseless carriage.\n", "[CLEMENS] Do not insult me. You have come here to this century and\n", "brought your infernal technology with you for God only knows what\n", "purpose, but I have no doubt it will be the people of this century who\n", "will suffer for it.\n", "[G<PERSON><PERSON><PERSON>] My dear Mister <PERSON>, I do think we've heard enough. Mister\n", "<PERSON>'s business is his own, and I will thank you to leave now.\n", "[CLEMENS] Pardon me, <PERSON><PERSON>, but it is my business too. It is the\n", "business of all humanity, I believe, to stop both of you from whatever\n", "it is you are doing here. And that is what I intend to do.\n", "(Sisters of Hope Infirmary)\n", "\n", "(<PERSON><PERSON> is up a ladder working on a gas lamp)\n", "[DOCTOR] Just what are you doing with those lamps? \n", "[PICARD] Replacing the burners. City ordinance. Makes it safer in case\n", "of earthquake.\n", "[DOCTOR] There hasn't been an earthquake here in thirty years. \n", "[PICARD] Well, that's takes care of this ward. Time to be moving on. \n", "(<PERSON><PERSON> leaves)\n", "[DOCTOR] Earthquakes. Nurse. (<PERSON><PERSON><PERSON> hides her tricorder) If you can\n", "handle things here, I've got a meeting with the Board of Patrons.\n", "[CRUSHER] I'll try to manage. \n", "(The doctor leaves)\n", "[LAFOR<PERSON>] There, by that bed. I can see an afterimage of triolic waves.\n", "They've been here recently. I'd say within the last twenty four hours.\n", "[CRUSHER] That's the bed where the man died last night. \n", "[TROI] This man was complaining about a strange doctor and nurse who\n", "visited that patient. Perhaps they're still in the hospital.\n", "(a patient starts coughing)\n", "[CRUSHER] (gives a drink) Here. This'll make you more comfortable. \n", "(<PERSON><PERSON>'s gas lamp detector starts flashing and <PERSON><PERSON><PERSON>'s tricorder\n", "beeps. She keys her comm. badge then goes over to the slim man and\n", "woman.)\n", "[CRUSHER] Do you need any help, Doctor? Doctor <PERSON><PERSON><PERSON><PERSON> felt he had\n", "entered the algid stage. He was cyanotic, pulse unobservable. That's an\n", "interesting cane.\n", "(The 'nurse' points her bag at Crusher as <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON>\n", "rush in)\n", "[LAFOR<PERSON>] Doctor! \n", "(<PERSON><PERSON><PERSON> gets the snake cane off the male and <PERSON><PERSON><PERSON> stuns him to no\n", "effect. The couple vanish)\n", "(<PERSON>'s contraption goes crazy, he checks a map and rushes out.)\n", "[POLICEM<PERSON>] Stand aside, stand aside. Let me in here. People said there\n", "was gunfire in here. What's going on?\n", "[RIKER] Just a little misunderstanding. I was just clearing these people\n", "out of here.\n", "[POLICEMAN] I haven't seen you before. \n", "[RIKER] I was just assigned here this morning. I was working downtown. \n", "[POLICEM<PERSON>] I worked downtown for three years. I don't remember you. \n", "[PICARD] We'll be on our way. \n", "[POLICEMAN] No, wait. Wait. I'm going to ask all of you to come down to\n", "the station for questioning. Including you. Where'd you get that?\n", "That's a gentleman's cane. Never seen a cane like this before. I'll\n", "have to confiscate it for evidence.\n", "[RIKER] I just want you to know that I have the utmost respect for the\n", "law.\n", "(then he punches his lights out)\n", "[PICARD] Let's go. \n", "(Infirmary street)\n", "\n", "(as the team come onto the street, a carriage\n", "careers around the corner)\n", "[RIKER] Data! \n", "(they climb on)\n", "[<PERSON><PERSON><PERSON><PERSON>] Boy, are we glad to see you. \n", "[DATA] I suggest we postpone our greetings for another occasion. \n", "[PICARD] Agreed. Get us out of here. \n", "(the cops come running, blowing their whistles)\n", "\n", "(Rented room)\n", "\n", "[LAFORGE] If we tune a phaser to the approximate\n", "frequency of triolic waves and lay down a field burst, it might\n", "respond.\n", "(the phaser shot turns the cane head into a writhing snake's head)\n", "[TROI] The ophidian the aliens were carrying. \n", "(it sends off energy charges before reverting to the cane)\n", "[RIKER] What the? \n", "[LAFORGE] These look like minute distortions in the space-time\n", "continuum, like the one we saw on Devidia Two.\n", "[RIKER] They were so small, and they lasted only a second. \n", "[DATA] The aliens appear to be able to concentrate the distortion and\n", "direct it to a specific time and place.\n", "[CRUSHER] Maybe they have something, a mechanism that focuses it. \n", "[LAFORGE] Any device like that would produce significant levels of\n", "triolic waves. Like the ones in the cavern where <PERSON>'s head was found.\n", "\n", "[DATA] I have located that cavern. \n", "(knocking on the door and frantic activity)\n", "[CARMI<PERSON><PERSON><PERSON> (OC)] Mister <PERSON><PERSON>! I know you're in there. Open the door! \n", "[TROI] Mrs. <PERSON>. \n", "[DATA] How now, spirit. Whither wander you? \n", "[CRUSHER] Over hill, over dale, thorough bush, thorough brier, \n", "[TROI] Mrs. <PERSON>, thank goodness you're here. \n", "[PICARD] We need someone to read a part. You're just in time. \n", "[CARMICHAEL] Mister <PERSON><PERSON>, I need to be talking to you. \n", "[CRUSHER] My mistress would that he be gone. \n", "[RIKER] Ill met by moonlight, proud Titan<PERSON>. \n", "[PICARD] Now, Mrs. <PERSON> Right there. \n", "[RIKER] Ill met by moonlight, proud Titan<PERSON>. \n", "[CARMICHAEL] What, jealous <PERSON><PERSON><PERSON>. Fairies skip hence. I have foresworn\n", "his bed and company.\n", "[PICARD] Well, I don't think I need to hear any more. That was truly\n", "unique.\n", "[CARMICHAEL] Really? \n", "[PICARD] Ladies and gentlemen, I think we have found our Titania. Don't\n", "you agree?\n", "(a round of applause)\n", "[CARMICHAEL] Well, I did do a church play when I was a lass. \n", "[PICARD] Well, there you are. We start rehearsals tomorrow. \n", "(<PERSON><PERSON> kisses her on both cheeks, and she turns into a simpering girl)\n", "\n", "(<PERSON>'s hotel room)\n", "\n", "(<PERSON><PERSON><PERSON> is pacing when <PERSON> enters)\n", "[GUINAN] You're back. I have wonderful news. I've found a way to get\n", "into the Presidio and into the mine shaft.\n", "(<PERSON><PERSON> enters)\n", "[DATA] It is all right. \n", "[GUINAN] Do you know me? \n", "[PICARD] Very well. \n", "[GUINAN] Do I know you? \n", "[PICARD] Not yet. But you will.\n", "\n", "(Infirmary street)\n", "\n", "[REPORTER] Thanks for your help, officer. \n", "[POLICEMAN] Now, be sure you put in the part about me spotting that\n", "phony policeman.\n", "[REPORTER] Yes, sir. \n", "[C<PERSON>MENS] Hello, son. \n", "[REPORTER] Mister <PERSON><PERSON><PERSON>. What brings you here? \n", "[C<PERSON><PERSON>NS] Writer's curiosity. I heard that two people vanished into thin\n", "air in this Infirmary.\n", "[REPORTER] One of the patients said that, yes, sir, but the police deny\n", "it.\n", "[CLEMENS] I bet they do. \n", "[REPORTER] They say a band of outlaws set off an explosion in the\n", "hospital and then escaped.\n", "[CLEMENS] Well, what did these outlaws look like? \n", "[REPORTER] The carriage they escaped in was driven by an albino. \n", "[CLEMENS] Oh. Mister Data. \n", "[REPORTER] You know him? \n", "[CLEMENS] You bet I do. You say there were others with him? \n", "[REPORTER] Yes, at least a dozen. \n", "[CLEMENS] His accomplices. They've come from the future. My <PERSON>, it's an\n", "invasion.\n", "(he hails a cab)\n", "[REPORTER] An invasion from the future? Mister <PERSON><PERSON><PERSON>, what can you\n", "tell me about this? Do you have any proof?\n", "[CLEMENS] When's your deadline, boy? \n", "[REPORTER] Five o'clock, sir. \n", "[C<PERSON><PERSON><PERSON>] I'll meet you at your paper at four thirty with a story that\n", "will make your career.\n", "[REPORTER] Thanks, <PERSON> \n", "[CLEMENS] Take me to the Presidio, driver, and don't spare the whip.\n", "\n", "(Cavern)\n", "\n", "[RIKER] The triolic levels are as high as they were\n", "on Devidia Two. There's no indication of a control mechanism.\n", "[LAFOR<PERSON>] I'm not so sure. My visor is picking up crystalline fractures.\n", "These cavern walls have undergone some kind of selective molecular\n", "polarisation. In fact, if I'm right this whole cavern has been\n", "configured to focus the space-time distortion. Just like a lens.\n", "Captain, we think we might be on to something. The cavern itself seems\n", "to be acting as a focusing mechanism. I'm willing to bet it's the same\n", "at their habitat back on Devidia Two.\n", "[PICARD] If we can get back there and destroy that site, it might put an\n", "end to their time travelling.\n", "[RIKER] We have the ophidian. \n", "[LAFOR<PERSON>] Truthfully, I don't know that we can get back. The aliens use\n", "triolic energy as a power source. The energy our phasers generates\n", "might not be entirely compatible.\n", "[PICARD] We have no choice but to try. \n", "[CLEMENS] An event I would most certainly enjoy witnessing. However, I\n", "will regretfully waive that opportunity for the\n", "privilege of taking you all in to the authorities.\n", "[DATA] Mister <PERSON>, it is imperative that we continue our mission. \n", "[CLEMENS] Mister <PERSON>, I have listened to your stories and your excuses\n", "and your evasions, and I will listen no longer. It is my moral duty to\n", "protect mankind from whatever devious plan you have in mind. Now, move\n", "along. I suspect that even time travellers are vulnerable to the Colt\n", "forty five. Now, let's go. I made a young fellow a promise and I don't\n", "want to be late.\n", "(the two aliens appear and the male grabs the cane from <PERSON><PERSON>. Data\n", "tackles him and gets it back. The cane activates, the woman disappears,\n", "there's a flash, everyone is thrown to the floor and <PERSON>'s head comes\n", "adrift. The time portal is open. The male runs through it)\n", "[PICARD] Follow him! \n", "(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> do so, then <PERSON><PERSON><PERSON> as <PERSON><PERSON> checks on\n", "G<PERSON><PERSON>. The portal closes.)\n", "\n", "(Devidia cavern)\n", "\n", "[RIKER] Is everybody all right? \n", "[CRUSHER] I think so. \n", "[RIKER] You! \n", "[CLEMENS] Where are we? And when? \n", "[RIKER] This is the twenty fourth century, we're on Devidia Two, and\n", "you're not supposed to be here.\n", "[CLEMENS] Well it seems to me I have as much right to be in your time as\n", "you had to be in mine. I wanted to see how you've conducted my future\n", "affairs.\n", "[TROI] Your future affairs? \n", "[CLEMENS] The affairs of mankind. \n", "[RIKER] But the disappearance of <PERSON>, one of the most noted\n", "literary figures of the nineteenth century\n", "[CLEMENS] Thank you. \n", "[RIKER] That's not supposed to happen. \n", "[CLEMENS] I only took advantage of an irresistible opportunity, as any\n", "good writer would.\n", "[WORF (OC)] Bridge to Away team. Acknowledge. \n", "[RIKER] We're here, Mister <PERSON>. Stand by to transport five. \n", "[LAFORGE] Commander. \n", "(<PERSON>'s headless body is lying on the floor, clutching the cane)\n", "[RIKER] Mister <PERSON>? \n", "[WOR<PERSON> (OC)] Yes, Commander. \n", "[RIKER] Make that six to transport.\n", "\n", "(Transporter room)\n", "\n", "[CLEMENS] Where are we now? \n", "[RIKER] The Federation Starship Enterprise. Ensign, call security. I\n", "want an escort for this man.\n", "[CLEMENS] Security? What for? Are you afraid I'm going to go around\n", "stealing things?\n", "(<PERSON><PERSON> enters)\n", "[C<PERSON>MENS] A werewolf! \n", "[RIKER] It's a long story, Mister <PERSON>. I'll brief you later. \n", "[LAFORGE] Let's have <PERSON>'s body taken to the science lab. I'll try and\n", "re-attach the head we have.\n", "[CRUSHER] <PERSON><PERSON><PERSON>, that head is over five hundred years old. \n", "[LAFORGE] Yeah, but it's the best chance we've got. \n", "[RIKER] I want Mister <PERSON><PERSON><PERSON> kept under escort at all times. \n", "[TROI] Commander, perhaps I could handle that. I'd be happy to take\n", "Mister <PERSON><PERSON><PERSON> to his quarters.\n", "[RIKER] Good idea. If you would accompany the Counsellor. \n", "[C<PERSON>ME<PERSON>] <PERSON><PERSON>, I'd be delighted. So, this is a space ship? You ever\n", "run into <PERSON><PERSON>'s comet?\n", "\n", "(Cavern)\n", "\n", "(<PERSON><PERSON><PERSON> is just coming round)\n", "[PICARD] Gently. Don't sit up too quickly. \n", "[GUINAN] Where'd everybody go? \n", "[PICARD] I hope they're all safely back on the Enterprise by now. \n", "[GUINAN] But you're still here. \n", "[PICARD] You were hurt. I had to make sure you were all right. \n", "[GUINAN] And so you stayed for that? \n", "[PICARD] I didn't want anything to happen to you. You're far too\n", "important to me.\n", "[GUINAN] You know an awful lot about me. \n", "[PICARD] Believe me, in the future the tables will be turned. \n", "[GUINAN] So we become friends? \n", "[PICARD] It goes far beyond friendship. \n", "[GUINA<PERSON>] Oh, but I'll have to wait almost five hundred years, and when\n", "we meet I won't be able to tell you about this, will I?\n", "[PICARD] No. Because for me, none of this will have happened yet. \n", "[GUINAN] What's that? \n", "(<PERSON>'s head)\n", "[PICARD] That's history fulfilling itself.\n", "\n", "(Ten Forward)\n", "\n", "[GUINAN] History has to fulfill itself. Even Picard\n", "knew that.\n", "[RIKER] You were there in the cavern. You know what happened. What am I\n", "supposed to do?\n", "[GUINAN] If I told you what happened in that cavern, it would affect any\n", "decision you'd make now. I can't do that. I won't.\n", "[RIKER] Not telling me might affect my decision. Did you think of that?\n", "We're talking about <PERSON><PERSON><PERSON>. I can't sit around and hope it all\n", "works out. I've got to do something.\n", "\n", "(Corridor)\n", "\n", "(<PERSON><PERSON> is back in her purple catsuit)\n", "[CLEMENS] Any place that doesn't stock a good cigar doesn't rank high in\n", "my book.\n", "[TROI] If you must have one, I'm sure we can replicate it for you. \n", "[CLEMENS] You think one of these imitations can take the place of a hand\n", "wrapped Havana?\n", "[TROI] I wouldn't know. \n", "[CLEMENS] Well, that's the problem I see here. All this technology it\n", "only serves to take away life's simple pleasures. You don't even let a\n", "man open the door for a lady.\n", "[TROI] I think what we've gained far outweighs anything that might have\n", "been lost.\n", "[<PERSON><PERSON><PERSON><PERSON>] Oh? Well, I'm not so impressed with this future. Huge\n", "starships, and weapons that can no doubt destroy entire cities, and\n", "military conquest as a way of life?\n", "[TROI] Is that what you see here? \n", "[CLEMENS] Well, I know what you say, that this is a vessel of\n", "exploration and that your mission is to discover new worlds.\n", "(a Bolian comes out of the turbolift as they get in)\n", "\n", "(Turbolift)\n", "\n", "[CLEMENS] That's what the Spanish said. \n", "[TROI] Deck thirty six. \n", "[CLEMENS] And the Dutch and the Portuguese. It's what all conquerors\n", "say. I'm sure that's what you told that blue-skinned fellow I just saw,\n", "before you brought him here to serve you.\n", "[TROI] He's one of the thousands of species that we've encountered. We\n", "live in a peaceful Federation with most of them. The people you see are\n", "here by choice.\n", "[CLEMENS] So there're a privileged few who serve on these ships, living\n", "in luxury and wanting for nothing. But what about everyone else? What\n", "about the poor? You ignore them.\n", "[TROI] Poverty was eliminated on Earth a long time ago, and a lot of\n", "other things disappeared with it. Hopelessness, despair, cruelty.\n", "[CLEMENS] Young lady, I come from a time when men achieve power and\n", "wealth by standing on the backs of the poor, where prejudice and\n", "intolerance are commonplace and power is an end unto itself. And you're\n", "telling me that isn't how it is anymore?\n", "[TROI] That's right. \n", "[CLEMENS] Well, maybe it's worth giving up cigars for after all.\n", "\n", "(Science lab)\n", "\n", "(the one where <PERSON> was born and <PERSON><PERSON>\n", "de-Borgified)\n", "[TROI] Any luck? \n", "[LAFORGE] Not so far. His activating units won't initialise. I thought\n", "they would have been protected by his buffering\n", "programme, but I guess five hundred years is just too long a wait.\n", "[CLEMENS] My watch. \n", "[LAFORGE] Yeah. It was found in the cavern where <PERSON>'s head was. I\n", "guess after five hundred years, that's not likely to work either.\n", "[CLEMENS] Mister <PERSON>, I fear I sadly misjudged you. As I have misjudged\n", "many things.\n", "\n", "(Cavern)\n", "\n", "(the female alien is waking up, and flickering\n", "between human and alien)\n", "[PICARD] Can you communicate? \n", "[ALIEN] Yes. \n", "[PICARD] You're injured. \n", "[ALIEN] Why have you interfered with us? \n", "[PICARD] You hunt us. You kill us. We cannot allow that. \n", "[ALIEN] We need your energy. \n", "[PICARD] Perhaps we can find a substitute. \n", "[ALIEN] No. There is none. We must continue. \n", "[PICARD] We know how you move back and forth through time. My crew have\n", "returned to the twenty fourth century to destroy your transport site on\n", "Devidia Two.\n", "[ALIEN] Destroy it? Your weapons will only amplify the time distortion.\n", "You will annihilate your own world.\n", "(and she disappears)\n", "(Observation lounge)\n", "\n", "[RIKER] I'm going back for Captain <PERSON>. Mister\n", "<PERSON><PERSON>, assemble an Away team to accompany me to the surface. Doctor, I\n", "need to know anything you can tell me about that ophidian.\n", "[CRUSHER] I've just started running some tests. If I can have a few\n", "hours.\n", "[RIKER] I can't give the alien any more time. \n", "[CRUSHER] Will, I haven't been able to determine if our phaser energy\n", "can generate a stable field. The risk would be\n", "[RIKER] I'll take that risk. \n", "[WORF] Sir. Permission to speak frankly. \n", "[RIKER] Go ahead. \n", "[WORF] Our priority is to stop the aliens from any more incursions to\n", "Earth. Any delay is unacceptable.\n", "[RIKER] If I can save Captain <PERSON><PERSON>, I consider that very acceptable. \n", "[WORF] The Captain would not. I recommend we target photon torpedoes on\n", "the alien habitat and destroy it. Immediately.\n", "[TROI] He's right, <PERSON>. \n", "[RIKER] Power up the photons, Mister <PERSON>. Alert me when they're ready.\n", "\n", "(Science lab)\n", "\n", "[LAFORGE] Computer, initialise the reload circuits. \n", "[COMPUTER] Reload circuits are initialising. \n", "[LAFORGE] Okay. Data? This ought to do it. \n", "(does something to his positronic net)\n", "[LAFORGE] I don't get it. I don't understand why isn't this working.\n", "Computer, run me a diagnostic on the input polarisers.\n", "[COMPUTER] There is intermittent contact in the input polarisers. \n", "[LAFORGE] Intermittent? \n", "(<PERSON><PERSON><PERSON> opens the back of <PERSON>'s head)\n", "[LAFORGE] What? An iron filing. How'd that get in there?\n", "\n", "(Cavern)\n", "\n", "(<PERSON><PERSON> picks up <PERSON>'s head and opens the back,\n", "then uses a piece of metal to do something to the circuits)\n", "\n", "(Bridge)\n", "\n", "[WORF] Commander, I have set the photons to fire in\n", "staggered rounds, detonating in ten second intervals.\n", "[RIKER] Very well. Fire when ready. \n", "[WORF] The sequence will be ready to initiate in one minute.\n", "\n", "(Science lab)\n", "\n", "[LAFORGE] Computer, run another diagnostic on the\n", "input polarisers.\n", "[COMPUTER] Polariser circuits are functioning. \n", "[LAFORGE] Well, then, that ought to do it. Okay, Data. Come on, now. \n", "[DATA] Torpedoes. Phasing. Alien. I am processing a binary message\n", "entered into my static memory by Captain <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, are we\n", "planning to fire on the alien habitat?\n", "[LAFORGE] Yeah, but \n", "[DATA] It is imperative that we do not. I will explain later. \n", "[LAFORGE] La Forge to Riker. Hold your fire!\n", "\n", "(Bridge)\n", "\n", "[DATA] The binary message left by the Captain is not\n", "entirely clear. He seemed to be concerned about the phase differential\n", "of our photon torpedoes. That firing them might produce catastrophic\n", "effects.\n", "[CRUSHER] Then how do we destroy their habitat? \n", "[DATA] If I am correct, we must modify our weapons so that the force of\n", "the explosion is re-phased into the aliens' time continuum.\n", "[LAFORGE] If we outfit the photons with phase discriminators we could\n", "get the variance we need.\n", "[RIKER] How long will it take you? \n", "[LAFORGE] At least a couple of hours. \n", "[RIKER] Fine. I'll have time to go get him. \n", "[DATA] Sir? \n", "[RIKER] I'm going back to the nineteenth century to get the Captain.\n", "Doctor?\n", "[CRUSHER] My analysis of the phasers suggests you'd be able to open the\n", "rift, but it won't be stable enough to transport more than one person.\n", "[RIKER] You mean if I go back, only one of us can return? \n", "[CRUSHER] That's right. \n", "[CLEMENS] Then I have the perfect solution for you. I'm the one who\n", "should return to the nineteenth century and remain there so your\n", "Captain can return here.\n", "[RIKER] There's a risk. We're not sure how stable the rift will be. \n", "[CLEMENS] There's risk in everything. The point is, it's the right\n", "choice. I've got more books to write, and your Captain has a job to do\n", "here.\n", "[RIKE<PERSON>] <PERSON><PERSON><PERSON>, you'll brief him on what he needs to know? \n", "[LAFORGE] Aye, sir. \n", "[C<PERSON>ME<PERSON>] I'm glad I have the chance to thank you. \n", "[DATA] For what, sir? \n", "[CLEMENS] Why, for starting me out on the greatest adventure a man's\n", "ever had. And for helping a bitter old man to open his eyes and see\n", "that the future turned out pretty well after all.\n", "\n", "(Cavern)\n", "\n", "[GUINAN] I'm thirsty. \n", "[PICARD] I'm going to get help. We have to get you out of here. \n", "[GUINAN] No, don't go. They'll be back for you soon. \n", "[PICARD] No, you need help. \n", "[CLEMENS] This thing put me down in the middle of Market Street. Took\n", "forever to get here.\n", "\n", "(Bridge)\n", "\n", "[WORF] Commander <PERSON> has completed the\n", "reconfiguration of the photon torpedoes.\n", "[RIKER] If <PERSON><PERSON><PERSON> got back, the Captain should have been here by now. \n", "[WORF] We have no way of knowing if Mister <PERSON><PERSON><PERSON> was successful. \n", "[RIKER] Re-establish your firing pattern, Mister <PERSON>. We'll wait five\n", "more minutes.\n", "[WORF] Aye, sir.\n", "\n", "(Cavern)\n", "\n", "[CLEMENS] No time for chit chat, sir. According to\n", "Mister <PERSON>, who did get your message by the way, a frequency\n", "setting of point oh four seven on your phaser will correctly activate\n", "this creature.\n", "[PICARD] Now you have to get help. <PERSON><PERSON><PERSON> needs medical attention. \n", "[<PERSON><PERSON><PERSON><PERSON>] I promise you she will be attended to. \n", "[PICARD] And there is a bill to be settled at Mrs. <PERSON>'s boarding\n", "house.\n", "[CLEMENS] I'll settle it. \n", "[PICARD] Thank you. I wish, I wish time would have allowed me to know\n", "you better.\n", "[CLEMENS] You'll just have to read my books. What I am is pretty much\n", "there.\n", "[GUINA<PERSON>] I'll see you in five hundred years, <PERSON><PERSON>. \n", "[PICARD] And I'll see you in a few minutes.\n", "\n", "(Bridge)\n", "\n", "[DATA] Commander, I am picking up massive triolic\n", "wave activity on the surface.\n", "[RIKER] Is it the Captain? \n", "[DATA] There are no human life signs. \n", "[RIKER] No sign of a temporal distortion? \n", "[DATA] No, sir, but triolic activity is increasing. \n", "[RIKER] The aliens. Mister <PERSON>, are the photons ready? \n", "[WORF] Yes, sir. \n", "[RIKER] Fire. \n", "[WORF] Torped<PERSON> away, sir. \n", "[DATA] Sir, I am detecting a temporal distortion on the surface and\n", "human life signs.\n", "[RIKE<PERSON>] <PERSON><PERSON><PERSON>, get him out of there! \n", "(KaBOOM on Devidia)\n", "[RIKER] Transporter room, have you got him? \n", "[P<PERSON><PERSON><PERSON> (OC)] He has indeed, Commander. And believe me, it's good to be\n", "back.\n", "[WORF] We have destroyed the target. There is no further indication of\n", "triolic activity.\n", "[RIKER] Ensign, lay in a course to the nearest Starbase. Warp six. \n", "Captain's log, stardate 46001.3. Everyone who\n", "should be in the nineteenth century is safely there, and those who\n", "should be in the twenty fourth are here. Mister <PERSON> has been restored\n", "to us, head and all, and <PERSON> will write the books he was to\n", "have written after our encounter.\n", "\n", "(Cavern)\n", "\n", "[CLEMENS] Now be careful, boys. Don't jostle her too\n", "much. Don't worry, <PERSON><PERSON>, you're going to be fine.\n", "(as young <PERSON><PERSON><PERSON> is stretchered away, <PERSON><PERSON><PERSON> picks up his broken watch\n", "then puts it down again to be found with the other artefacts in 500\n", "years time)\n"]}], "source": ["print(picard[\"TEXT\"].values[400])"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>METADATA</th>\n", "      <th>SOURCE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>The Deep Space Nine - Emissary\\n\\nEmissary\\nSt...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>The Deep Space Nine - Past Prologue\\n\\nPast\\nP...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>The Deep Space Nine - A Man Alone\\n\\nA\\nMan Al...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>The Deep Space Nine - Babel\\n\\nBabel\\nStardate...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>The Deep Space Nine - Captive Pursuit\\n\\nCapti...</td>\n", "      <td>{\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...</td>\n", "      <td>startrek/chakoteya</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                TEXT  \\\n", "0  The Deep Space Nine - Emissary\\n\\nEmissary\\nSt...   \n", "1  The Deep Space Nine - Past Prologue\\n\\nPast\\nP...   \n", "2  The Deep Space Nine - A Man Alone\\n\\nA\\nMan Al...   \n", "3  The Deep Space Nine - Babel\\n\\nBabel\\nStardate...   \n", "4  The Deep Space Nine - Captive Pursuit\\n\\nCapti...   \n", "\n", "                                            METADATA              SOURCE  \n", "0  {\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...  startrek/chakoteya  \n", "1  {\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...  startrek/chakoteya  \n", "2  {\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...  startrek/chakoteya  \n", "3  {\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...  startrek/chakoteya  \n", "4  {\"show\": \"Star Trek\", \"season\": \"DS9\", \"episod...  startrek/chakoteya  "]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["picard.to_parquet(\"picard.pq\", row_group_size=100, engine=\"pyarrow\", index=False)\n", "picard.head()  # http://www.chakoteya.net/StarTrek/index.html and also https://github.com/GJBroughton/Star_Trek_Scripts/"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["708"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["len(picard)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.2"}}, "nbformat": 4, "nbformat_minor": 4}