"""add origin column to message_tree_state

Revision ID: 49d8445b4c90
Revises: f856bf19d32b
Create Date: 2023-01-28 11:57:45.580027

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "49d8445b4c90"
down_revision = "f856bf19d32b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("message", sa.Column("synthetic", sa.<PERSON>(), server_default=sa.text("false"), nullable=False))
    op.add_column("message", sa.Column("model_name", sa.String(length=1024), nullable=True))
    op.add_column("message_tree_state", sa.Column("origin", sa.String(length=1024), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("message_tree_state", "origin")
    op.drop_column("message", "model_name")
    op.drop_column("message", "synthetic")
    # ### end Alembic commands ###
