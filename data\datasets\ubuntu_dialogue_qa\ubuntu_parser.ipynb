{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Convert the Ubuntu Dialogue Corpus into Q&A pairs\n", "\n", "Select the short Q&A discussions only from the Ubuntu Dialogue Corpus.\n", "\n", "**Acknowledgments**\n", "https://www.kaggle.com/datasets/rtatman/ubuntu-dialogue-corpus\n", "\n", "This dataset was ORIGINALLY collected by <PERSON>, <PERSON> , <PERSON><PERSON><PERSON>† and <PERSON><PERSON>. It is made available here under the Apache License, 2.0. If you use this data in your work, please include the following citation:\n", "\n", "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, \"The Ubuntu Dialogue Corpus: A Large Dataset for Research in Unstructured Multi-Turn Dialogue Systems\", SIGDial 2015. URL: http://www.sigdial.org/workshops/conference16/proceedings/pdf/SIGDIAL40.pdf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/LAION-AI/Open-Assistant/blob/data/datasets/ubuntu_dialogue/ubuntu_parser.ipynb)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# uncomment and run below lines to set up if running in colab\n", "# !git clone https://github.com/LAION-AI/Open-Assistant.git\n", "# %cd Open-Assistant/data/datasets/ubuntu_dialogue/\n", "# !pip install -r requirements.txt"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# download data, you can get your kaggle.json file from your account page https://www.kaggle.com/me/account\n", "import kaggle\n", "\n", "kaggle.api.dataset_download_files(\"rtatman/ubuntu-dialogue-corpus\", unzip=True)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# global settings\n", "\n", "FOLDER = \"Ubuntu-dialogue-corpus\"  # input folder containing ubuntu dialogue csv files\n", "SOURCE = \"ubuntu-dialogue\"  # source to use in the parquet for each row"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "\n", "from tqdm import tqdm\n", "\n", "import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████████████| 3/3 [07:34<00:00, 151.36s/it]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>from</th>\n", "      <th>to</th>\n", "      <th>text</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2008-04-23 14:55:00+00:00</td>\n", "      <td>bad_image</td>\n", "      <td>NaN</td>\n", "      <td>Hello folks, please help me a bit with the fol...</td>\n", "      <td>3_126125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2008-04-23 14:56:00+00:00</td>\n", "      <td>bad_image</td>\n", "      <td>NaN</td>\n", "      <td>Did I choose a bad channel? I ask because you ...</td>\n", "      <td>3_126125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2008-04-23 14:57:00+00:00</td>\n", "      <td>lord<PERSON><PERSON></td>\n", "      <td>bad_image</td>\n", "      <td>the second sentence is better english   and we...</td>\n", "      <td>3_126125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2009-08-01 06:22:00+00:00</td>\n", "      <td>mechtech</td>\n", "      <td>NaN</td>\n", "      <td>Sock Puppe?t</td>\n", "      <td>3_64545</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2009-08-01 06:22:00+00:00</td>\n", "      <td>mechtech</td>\n", "      <td>NaN</td>\n", "      <td>WTF?</td>\n", "      <td>3_64545</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       date       from         to  \\\n", "0 2008-04-23 14:55:00+00:00  bad_image        NaN   \n", "1 2008-04-23 14:56:00+00:00  bad_image        NaN   \n", "2 2008-04-23 14:57:00+00:00  lord<PERSON><PERSON>  bad_image   \n", "3 2009-08-01 06:22:00+00:00   mechtech        NaN   \n", "4 2009-08-01 06:22:00+00:00   mechtech        NaN   \n", "\n", "                                                text        id  \n", "0  Hello folks, please help me a bit with the fol...  3_126125  \n", "1  Did I choose a bad channel? I ask because you ...  3_126125  \n", "2  the second sentence is better english   and we...  3_126125  \n", "3                                       Sock Puppe?t   3_64545  \n", "4                                               WTF?   3_64545  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["def load(file):\n", "    data = pd.read_csv(file)\n", "    data[\"date\"] = pd.to_datetime(data[\"date\"])\n", "    data[\"id\"] = data[[\"folder\", \"dialogueID\"]].apply(lambda x: f\"{x[0]}_{x[1].split('.tsv')[0]}\", axis=1)\n", "    data.drop(columns=[\"folder\", \"dialogueID\"], inplace=True)\n", "    return data\n", "\n", "\n", "data = None\n", "for file in tqdm(os.listdir(FOLDER)):\n", "    data = pd.concat([data, load(os.path.join(FOLDER, file))])\n", "\n", "data.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████| 1852868/1852868 [08:17<00:00, 3725.97it/s]\n"]}], "source": ["# clean up the df, remove duplicates and answers that are way too short, etc.\n", "clean = {col: [] for col in [\"INSTRUCTION\", \"RESPONSE\", \"SOURCE\", \"METADATA\"]}\n", "\n", "for name, group in tqdm(data.groupby(\"id\")):\n", "    if len(group) < 3 or len(group) > 5:  # 3, 4, 5 len\n", "        continue  # back and forth will most likely not be parsed correctly\n", "\n", "    group.sort_values(by=[\"date\"], ascending=True, inplace=True)\n", "    instruction = str(group[\"text\"].values[0]).strip()\n", "    insturction_user = group[\"from\"].values[0]\n", "    if not instruction or pd.isna(instruction) or len(instruction) < 12:\n", "        continue\n", "    if not re.findall(\n", "        r\"(?i)(?:\\?|what|who|where|why|when|how|whose|explain|tell|does|way|can|know|able|best|recommend)\", instruction\n", "    ):\n", "        continue  # probably not a question\n", "\n", "    all_recipients = \"|\".join(\n", "        [re.escape(item) for item in set(group[\"to\"].tolist() + group[\"from\"].tolist()) if pd.notna(item)]\n", "    )\n", "    response = None\n", "    response_user = None\n", "    for _, row in group.iterrows():\n", "        if row[\"to\"] == insturction_user:\n", "            candidate = str(row[\"text\"]).strip()\n", "            if (\n", "                not row[\"text\"]\n", "                or pd.isna(row[\"text\"])\n", "                or re.findall(r\"(?i)^(yes|yep|yeah|no|nah|nope|sure|yes\\s*sir)\\W*$\", candidate)\n", "            ):\n", "                continue  # answer is not expressive\n", "            if len(candidate) < 3:\n", "                continue  # too short\n", "            if re.findall(r\"(?i)(?:wrong|o[nf].*?topic|else\\s*where|ask.+?in|\\#\\w+|google|you.+?mean)\", candidate):\n", "                continue  # probably off topic\n", "            if re.findall(r\"\\b(\" + all_recipients + r\")\\b\", candidate):\n", "                continue  # answer includes user name(s)\n", "            response = candidate\n", "            response_user = row[\"from\"]\n", "        elif response_user is not None and row[\"to\"] == response_user and row[\"from\"] == insturction_user:\n", "            if re.findall(r\"(?i)(?:thank|thx|works|working|great)\", str(row[\"text\"])):\n", "                clean[\"INSTRUCTION\"].append(instruction)\n", "                clean[\"RESPONSE\"].append(response)\n", "                clean[\"SOURCE\"].append(SOURCE)\n", "                clean[\"METADATA\"].append(json.dumps({\"user_question\": insturction_user, \"user_answer\": response_user}))\n", "                break"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>INSTRUCTION</th>\n", "      <th>RESPONSE</th>\n", "      <th>SOURCE</th>\n", "      <th>METADATA</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>hi, is there a CLI command to roll back any up...</td>\n", "      <td>your recourse is to re-install fresh the older...</td>\n", "      <td>ubuntu-dialogue</td>\n", "      <td>{\"user_question\": \"edd\", \"user_answer\": \"n8tus...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A LiveCD iso can be burned to a DVD-R and run ...</td>\n", "      <td>I hope so, or the custom DVDs I've done are wo...</td>\n", "      <td>ubuntu-dialogue</td>\n", "      <td>{\"user_question\": \"usrl\", \"user_answer\": \"<PERSON>hos...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>hello, is there a way to adjust gamma settings...</td>\n", "      <td>for me i have my nvidia settings manager and i...</td>\n", "      <td>ubuntu-dialogue</td>\n", "      <td>{\"user_question\": \"nucco_\", \"user_answer\": \"sp...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>does ubuntu come with a firewall by default?</td>\n", "      <td>no iptables rule is loaded by deault on ubuntu</td>\n", "      <td>ubuntu-dialogue</td>\n", "      <td>{\"user_question\": \"aeleon\", \"user_answer\": \"er...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Can someone tell me howto get rid of Google Ch...</td>\n", "      <td>sudo dpkg -l |grep -i chrom ----&gt; sudo apt-get...</td>\n", "      <td>ubuntu-dialogue</td>\n", "      <td>{\"user_question\": \"frold\", \"user_answer\": \"shi...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                         INSTRUCTION  \\\n", "0  hi, is there a CLI command to roll back any up...   \n", "1  A LiveCD iso can be burned to a DVD-R and run ...   \n", "2  hello, is there a way to adjust gamma settings...   \n", "4       does ubuntu come with a firewall by default?   \n", "5  Can someone tell me howto get rid of Google Ch...   \n", "\n", "                                            RESPONSE           SOURCE  \\\n", "0  your recourse is to re-install fresh the older...  ubuntu-dialogue   \n", "1  I hope so, or the custom DVDs I've done are wo...  ubuntu-dialogue   \n", "2  for me i have my nvidia settings manager and i...  ubuntu-dialogue   \n", "4     no iptables rule is loaded by deault on ubuntu  ubuntu-dialogue   \n", "5  sudo dpkg -l |grep -i chrom ----> sudo apt-get...  ubuntu-dialogue   \n", "\n", "                                            METADATA  \n", "0  {\"user_question\": \"edd\", \"user_answer\": \"n8tus...  \n", "1  {\"user_question\": \"usrl\", \"user_answer\": \"Ghos...  \n", "2  {\"user_question\": \"nucco_\", \"user_answer\": \"sp...  \n", "4  {\"user_question\": \"aeleon\", \"user_answer\": \"er...  \n", "5  {\"user_question\": \"frold\", \"user_answer\": \"shi...  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["clean = pd.DataFrame(clean)\n", "clean.sort_values(by=\"RESPONSE\", key=lambda x: x.str.len(), inplace=True, ascending=False)\n", "clean.drop_duplicates(subset=[\"INSTRUCTION\"], inplace=True)\n", "clean.sort_index(inplace=True)\n", "clean.head()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retrieved 0.87% of all questions (16173)\n"]}], "source": ["print(f\"Retrieved {len(clean) / len(data['id'].unique()) * 100.:.2f}% of all questions ({len(clean)})\")  # 19921"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Q > hi, is there a CLI command to roll back any updates/upgrades I made recently?\n", "A > your recourse is to re-install fresh the older version\n", "\n", "Q > A LiveCD iso can be burned to a DVD-R and run with no problems, right?\n", "A > I hope so, or the custom DVDs I've done are worthless. ;)\n", "\n", "Q > hello, is there a way to adjust gamma settings in totem? my videos aren't playing with the correct colours\n", "A > for me i have my nvidia settings manager and i change the video gamma settings from there...\n", "\n", "Q > does ubuntu come with a firewall by default?\n", "A > no iptables rule is loaded by deault on ubuntu\n", "\n", "Q > Can someone tell me howto get rid of Google Chrome? Im not able to uninstall it...\n", "A > sudo dpkg -l |grep -i chrom ----> sudo apt-get remove 'on what appears'\n", "\n", "Q > wow. for the life of me i can never remember this command. whats the command that outputs your ati hardare information? shows if you have direct rendering?\n", "A > glxinfo | grep dri ?\n", "\n", "Q > ack!  what the heck kind of Linux distro doesn't install traceroute by default?\n", "A > ubuntu\n", "\n", "Q > is there a way to see if a hard disk has bad blocks on ubuntu? fsck does the job?\n", "A > have you considered, however, monitoring your HD's state using the SMART sensors? (the 'smartmontools' package can be used to query them)\n", "\n", "Q > anyone know how to turn off opening things with a single click...its driving me crazy and I want to go back to doubleclicking\n", "A > open a file browser, go to edit|preferences | behavious, and change double to single\n", "\n", "Q > is there a graphical way to search for an nfs server on gutsy?\n", "A > does Places > Network work for you?\n", "\n", "Q > What's the best way for a bash script to pick up variables from /etc/environment? Should I just use source?\n", "A > if not, parse it\n", "\n", "Q > Hi, My Western Digital USB Passport Drive doesn't appear under ubuntu 7.10. Works fine in XP and from earlier versions of Ubuntu. Any ideas?\n", "A > I have one of those too.. it works nicely in ubuntu... needs a properly power-supplied usb port though\n", "\n", "Q > hi, I'm a bit low on disk space and I saw that /usr/src/linux-source* folder takes 2gb of space, so i was wondering whether is it safe to remove it?\n", "A > yes it safe to remove it\n", "\n", "Q > Where can I find a detailed description of scrollkeeper?\n", "A > http://scrollkeeper.sourceforge.net/documentation.shtml ?\n", "\n", "Q > whats the human readable command for hardware info?\n", "A > I'm sure you can guess ls hw :)\n", "\n", "Q > How do I move a file from one place to another in console?\n", "A > use mv\n", "\n", "Q > is there any reason why I should not use proposed packages ?? I just enabled proposed because I need ff3rc1\n", "A > you could download the package from packages.ubuntu.com and install it without enabling the proposed repo.\n", "\n", "Q > Where can I find full DVD of hardy for amd64 ???\n", "A > http://www.acc.umu.se/~mighty/ubuntu/ubuntu-8.04-dvd-amd64.iso.torrent\n", "\n", "Q > where can i find a log of the latest updates ubuntu has done?\n", "A > /var/log/dpkg\n", "\n", "Q > hi folks. i am trying to convert screencast I made to mpeg4     but when I try    ffmpeg -i ~/out.mpg -ar 22050 blah.mp4          I get Unsupported codec for output     .   Any clue which package I need for this?\n", "A > you probably the ffmpeg binaries from medibuntu to get mp4 support due to patent issues\n", "\n", "Q > btw. what was the name of the ubuntu installer again? i keep forget ;)\n", "A > ubiquity ?\n", "\n", "Q > how do i see the system statistics for my CPU and video card? i need to know how fast it thinks my video card and CPU are\n", "A > cat /proc/cpuinfo   for cpu, not sure what gfx card would be\n", "\n", "Q > Dumb question.  How do I come back to the desktop after ctrl-alt-f1?\n", "A > Ctrl-Alt-F7\n", "\n", "Q > Hi there, I typed in 'find $HOME -depth 0 -name foo' and it returned the complaint 'paths must precede expression.'  I think the syntax is correct .. can someone identify what I did wrong, please?\n", "A > depth doesnt take a number\n", "\n", "Q > Incredible that man page says absolutely nothing about my question I wonder why I asked it here in the first place.\n", "A > dpkg -l '*package*'\n", "\n", "Q > How To setup a Static IP Address ? i dont have fixed ip.... please help...\n", "A > I believe in gnome there is Preferences(or Administration)->Network\n", "\n", "Q > how do i avoid to be prompted for password when ubuntu starts up?\n", "A > try 'User Accounts'\n", "\n", "Q > Are there any programs for ubuntu that can help me build a website.\n", "A > look into Quant<PERSON> and Kompozer\n", "\n", "Q > is there a way to stop network manager for the current session?\n", "A > service network-manager stop\n", "\n", "Q > can someone point me in the right direction for command line syncing of an ipod touch?\n", "A > i don't really know if that link is relevant, but hey ..\n", "\n", "Q > hi how do i find files using terminal. i learnt last time, it uses iname or something. thanks in advance\n", "A > find / -name '*yeah*' 2>/dev/null\n", "\n", "Q > whats the command to detect drives connected again?\n", "A > sudo fdisk -l   .  or just 'mount' to see what's mounted\n", "\n", "Q > Should ubuntu have an xorg config by default?\n", "A > I don't think x<PERSON><PERSON> has an xorg.conf anymore but there is definitely an xorg channel\n", "\n", "Q > how to start again console-keymaps?  i installed it using apt-get and when it finish it ask for setups and now i try to remove and install to do the setup again it won't run the setup.. help pls.\n", "A > sudo dpkg-reconfigure console-keymaps\n", "\n", "Q > How can I locate packages from the latest ubuntu release?  Intrepid?  I want to browse a mirror with a web browser and look at the .deb files that are available.\n", "A > packages.ubuntu.com, probably\n", "\n", "Q > How can I print out all of the enviroment variables that are set on my computer?\n", "A > thats it, env\n", "\n", "Q > is visudo preferred now over gksudo?\n", "A > all visudo does is edit /etc/sudoers\n", "\n", "Q > Is there a package that I can provide a PDF to that will read it to me using text to speech?\n", "A > the program pdftotext converts a pdf to a text file, if that's useful to you.\n", "\n", "Q > can anyone help me set up gtkpod? I am trying to figure out the ipod's mount point. I can see it on my desktop, but I don't know how to find out where it's mounted.\n", "A > ''mount''\n", "\n", "Q > having trouble opening synaptic package manager. error message says I should use 'dpkg --configure -a' . I tried that in the terminal. It said I need to be super user. now what?\n", "A > put 'sudo' before the dpkg command\n", "\n", "Q > Can someone tell me what command I can run on the command line to launch the restricted-driver manager?\n", "A > well. jockey-gtk I guess\n", "\n", "Q > I made a custom livecd, but what I wanted to do was change the default login shell for the livecd, any ideas?\n", "A > change the shell in /etc/passwd\n", "\n", "Q > Can you run conky in a terminal?\n", "A > It's a X app, so no\n", "\n", "Q > hi all. can i on my gutsy disabling cpu frequency scaling? how to? i want to use my notebook as a desktop. i want to disable all energy policy\n", "A > Usually you can set that in the BIOS.\n", "\n", "Q > hi. new ati driver (8.2) on ati web site! someone have tried? can i install without uninstall old driver?\n", "A > erm, overwrite the old one, that is\n", "\n", "Q > How can I append something to an enviroment variable?\n", "A > VARIABLE=$VARIABLE:blah ?\n", "\n", "Q > how to set DNS manually? cant find good tutorial. please\n", "A > FYI i believe NetworkManager will overright resolv.conf (when it wants to), there should/might be a way to set DNS in N-M.  Good luck.\n", "\n", "Q > anyone know the gname of the chm reader?\n", "A > apt-cache search chm --names-only\n", "\n", "Q > how do I login in a nested window in Ubuntu 8.04?\n", "A > have a look at properties of fast user switcher, and install xnest\n", "\n", "Q > !alis | raven i'd search for a mplayer/mencoder channel if i where you\n", "A > In previous versions of Ubuntu they didn't treat USB drives differently, now they do. I personally wish that they would always default to installing grub's boot sector to the MBR of the drive containing Ubuntu, and there is no technical problem preventing them from doing so, they have just decided not to do things that way.\n", "\n", "Q > Hi. When i log into ubuntu  (7.10, fully updated) my gnome session starts and shows desktop icons, top panel, buttom panel, some if not all desktop icons for a second or so. Next, it's only the background (again, also for a few seconds) and then everything pops up again and functions perfectly.  Any idea how to get rid of this?\n", "A > disable compiz?\n", "\n", "Q > Hi ! I'm looking for a compatible color laser multifunction printer. Tried the Dell 2135cn with no luck, I'm thinking of the 2145cn, but have no clue of what other manufacturers may offer. Any idea ?\n", "A > I would suggest you look for a HP one. HP have always had awesome print support in linux\n", "\n", "Q > i'm trying to setup upstart, the service works when i do 'sudo start servicename' etc, but doesnt get started on reboot.. any ideas?\n", "A > /etc/rc.local is ran just before the login screen. If you add commands in there it will be executed (add them ABOVE the exit 0 line)\n", "\n", "Q > how do I change my login window via text?\n", "A > baobab is a nice graphical disk space usage display; it shows up in the accessories menu as disk usage analyzer\n", "\n", "Q > I would like to install Apache httpd, I don't see it in the add/remove applications dialogue. I have no problems with compiling from source, but is that the best way forward?\n", "A > System>Admin>synaptic\n", "\n", "Q > Hi, how can i change the default boot kernel on clients?\n", "A > using pxe??\n", "\n", "Q > I have just installed Ubuntu 8.04 on a 4GB RAM based computer. Do you know how can I install (apt-get) a 'server' kernel for PAE support ?\n", "A > no sorry, thats out of my league\n", "\n", "Q > I have apache/php/sql installed on my windows machine, how can I browse to this from my ubuntu computer? I have tried //mike and xx.xx.xx.xx ip address from the address bar :|\n", "A > is the windows box a WAMP server for real?  Is there a firewall stopping it?\n", "\n", "Q > I'm getting the error 'The file /boot/grub/stage1 not read correctly, can someone help me correct this?\n", "A > i guess id rerun grub-install\n", "\n", "Q > I changed accepted 'modes' into 800x600 in Screen subsection, hoping it would help. how can i restart X from the console without restarting computer?\n", "A > what does it accept?\n", "\n", "Q > maybe my question was a little bit too specific... but really i was wondering if there is anyway at all to help test my emulated middle button for the mouse\n", "A > Open up a text editor. Highlight some text, and then press your left and right mouse buttons together into the text editor. It should paste what you've highlighted.\n", "\n", "Q > can anyone tell me how to make my extra internal hard drive automatically mount when i log in?\n", "A > : try https://help.ubuntu.com/community/AutomaticallyMountPartitions\n", "\n", "Q > Whats a good Python IDE for linux?\n", "A > idle\n", "\n", "Q > How do I extract a RAR split up into multiple parts? All I can manage to do is go through and individually extract each piece, which means any files split up end up with just one part\n", "A > if it actually is a multipart rar, 'unrar x first.part.rar' will extract all of them.\n", "\n", "Q > I'm trying to install my graphics card's drivers through the command line, but I can't remember how to anymore. Does anyone remember the command(s) to run the .run file?\n", "A > ./file.run\n", "\n", "Q > does anyone know why compiling ndiswrapper from source might fail?\n", "A > then again it's been a while since I installed it\n", "\n", "Q > Q: What package should i install for this (checking for Qt... configure: error: Qt (>= Qt 3.0) (headers and libraries) not found. Please check your installation!), thanks\n", "A > libqt-dev\n", "\n", "Q > This is freenode right?\n", "A > irc.freenode.net\n", "\n", "Q > hi. could anyone please give me a quick hint on how to install the adobe flash pugin for firefox in hardy?\n", "A > find out it's name (enter about:plugins as the URL, it ends in .so)\n", "\n", "Q > Does anyone here know how I can install the Windows7 boot manager from within Ubuntu?\n", "A > Why? And only in VirtualBox as far as I know\n", "\n", "Q > how do i change from unity to gnome 3? I can't find any 'Sessions' part of system settings.\n", "A > upon login there's a cog next to your name, no?\n", "\n", "Q > is php5 supported by gutsy gibbon?\n", "A > apt-get into it.\n", "\n", "Q > Is there any way to kill 'gdmgreeter' (and everything involving a GUI/X For that matter, including gdm/X itself) so it doesn't restart again automatically?\n", "A > sudo /etc/init.d/gdm stop\n", "\n", "Q > Where can I get drivers for a NVIDIA GeForce Go 7400 to run with OpenGL?\n", "A > I'm not sure, I've been sifting through forum posts all day for my problem, did you try your restricted devices manager?\n", "\n", "Q > anyway to su to become root?\n", "A > sudo su -\n", "\n", "Q > can anyone recomend an ircd. i want the most secure and the most stable and with most veriety options and control\n", "A > but freenode is running hyperion-1.0.2b if that helps\n", "\n", "Q > I recently enabled gutsy-backports updates because of this one package I was trying to get running.  When I enabled it though, it was marked with a - instead of a checkmark and I can't un-enable it.  This is bothering me because now my updates notification keeps showing me all these compvis updates and other things I don't want to muck with.  How can I disable these updates?\n", "A > you can disable it by editing /etc/apt/sources.list\n", "\n", "Q > Guys where can i find adobe flash x64? I installed both from software center but got the same i386 crap\n", "A > I am just curious why do you need x64 flash?\n", "\n", "Q > Hello, does anyone know a good java compiler for beginner beginners?\n", "A > Eclipse\n", "\n", "Q > can anyone recommend an image viewer that doesn't require installing half of gnome (eog) or half of kde (gwenview)?\n", "A > rist<PERSON>to?\n", "\n", "Q > How can i find out what graphics card i have?\n", "A > type sudo lspci\n", "\n", "Q > whats pclos?\n", "A > pclinuxos\n", "\n", "Q > high guys - any irssi users?\n", "A > I am one.  WHy do you ask?\n", "\n", "Q > hi I installed a new gpu but ubuntu wont find it, what can I do to 'rescan' for the newly installed one?\n", "A > you sure it's installed correctly?\n", "\n", "Q > hi, someone there knows a great mounting program for linux, like deamon tools for windows.\n", "A > http://www.ubuntugeek.com/mount-and-unmout-iso-images-without-burning-them.html\n", "\n", "Q > hi all... any idea about Envy for nvidia driver installation ?\n", "A > It's probably unnecessary.  Look at the Restricted Drivers Manager.\n", "\n", "Q > When I run $PATH the output ends with 'No such file or directory.' Is that in reference to the last path in the list? If so, I have this path, what's going wrong?\n", "A > run? $PATH is a system variable..\n", "\n", "Q > I am connected to an university network which blocks ALL incoming connections... is there some way to make a permanent connection to a remote server, and somehow tunnel back along the connection? is this what VPN is?\n", "A > reverse*   http://articles.techrepublic.com.com/5100-10878_11-5779944.html\n", "\n", "Q > any idea why <PERSON><PERSON><PERSON> stopped working after I did 'dist-upgrade' and restarted?\n", "A > gutsy to hardy?\n", "\n", "Q > i have xubuntu. am i in the wrong channel?\n", "A > there is still add/remove, etc.\n", "\n", "Q > from a terminal how can I kill x? I just found out that alt-sysReq k will kill <PERSON>, how can do that remotely say from ssh?\n", "A > sudo service gdm stop\n", "\n", "Q > can anyone tell me how i mount smbfs drives on the desktop as icons?\n", "A > do you have the drives mounted somewhere with samba already?\n", "\n", "Q > can anybody help me???!!! on vaio notebook vgn-fe31zr 7.10 dont boot. Other bootcds working properly\n", "A > where did you get the ubuntu CD from?\n", "\n", "Q > hi, I have crontab to run a script, when it was * * * * * /home/<USER>/update.php it didnt work and when I changed it to * * * * * php /home/<USER>/update.php it works fine the question is what is the rright way to run it?\n", "A > are you sure the file /home/<USER>/update.php is set to executable?  what does this say?  ls -l /home/<USER>/update.php\n", "\n", "Q > Does anyone know if cube is enabled on Extra mode? I tried ctrl+alt+left click and it does nothing.\n", "A > you have to get advanced desktop settings\n", "\n", "Q > I heard folks complaining about the flash update last night - that it broke flash completely - anyone know if it's ok? I haven't updated yet\n", "A > its broke\n", "\n", "Q > what do i have to install to get the command 'repo'?\n", "A > what about reading http://source.android.com/download/using-repo ^-^ ? there's an 'installing repo' section\n", "\n", "Q > how do I remove a directory from the PATH?\n", "A > tail -f\n", "\n", "Q > ok, so i have this computer, that i *should* be able to run ubuntu on, but it doesnt agree with any live cds or similar, so i figured i could use my main computer that will correctly run a ubuntu boot disk to install to the hardrive i plan to use on the other computer[its a 6 gig wd] and then throw it in, then run some command as i recall reading about someone doing something similar on the forums, both computers run an celeron, this\n", "A > Try dpkg --configure -a\n", "\n", "Q > can someone tell me how to zip a folder up from the command line? (and I mean zip, not tar)\n", "A > ~ zip -r name_of_file.zip files\n", "\n"]}], "source": ["for index, row in clean.iterrows():\n", "    print(\"Q >\", row[\"INSTRUCTION\"])\n", "    print(\"A >\", row[\"RESPONSE\"])\n", "    print()\n", "    if index > 100:\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.2"}}, "nbformat": 4, "nbformat_minor": 4}