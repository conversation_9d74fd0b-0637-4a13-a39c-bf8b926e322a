"""add troll_stats

Revision ID: 4d7e0b0ebe84
Revises: 9e7ec4a9e3f2
Create Date: 2023-02-02 15:44:12.647260

"""
import sqlalchemy as sa
import sqlmodel
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "4d7e0b0ebe84"
down_revision = "9e7ec4a9e3f2"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "troll_stats",
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("base_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column(
            "modified_date", sa.DateTime(timezone=True), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False
        ),
        sa.Column("time_frame", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("troll_score", sa.Integer(), nullable=False),
        sa.Column("rank", sa.Integer(), nullable=True),
        sa.Column("red_flags", sa.Integer(), nullable=False),
        sa.Column("upvotes", sa.Integer(), nullable=False),
        sa.Column("downvotes", sa.Integer(), nullable=False),
        sa.Column("spam_prompts", sa.Integer(), nullable=False),
        sa.Column("quality", sa.Float(), nullable=True),
        sa.Column("humor", sa.Float(), nullable=True),
        sa.Column("toxicity", sa.Float(), nullable=True),
        sa.Column("violence", sa.Float(), nullable=True),
        sa.Column("helpfulness", sa.Float(), nullable=True),
        sa.Column("spam", sa.Integer(), nullable=False),
        sa.Column("lang_mismach", sa.Integer(), nullable=False),
        sa.Column("not_appropriate", sa.Integer(), nullable=False),
        sa.Column("pii", sa.Integer(), nullable=False),
        sa.Column("hate_speech", sa.Integer(), nullable=False),
        sa.Column("sexual_content", sa.Integer(), nullable=False),
        sa.Column("political_content", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["user.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_id", "time_frame"),
    )
    op.create_index("ix_troll_stats__timeframe__user_id", "troll_stats", ["time_frame", "user_id"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_troll_stats__timeframe__user_id", table_name="troll_stats")
    op.drop_table("troll_stats")
    # ### end Alembic commands ###
