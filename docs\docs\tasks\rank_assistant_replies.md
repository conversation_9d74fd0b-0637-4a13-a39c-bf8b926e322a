# Ranking assistant replies

In this task, you will be shown a conversation between a user and the assistant.
Below, there are at least 2 **assistant replies** - messages written by people
acting as the assistant. Your job is to rank the replies based on how well they
adhere to the guidelines, the first reply being the best, and the last the
worst.

First and foremost, that means a reply has to fulfill the request given by the
previous prompt and not contain any factual inaccuracies, unless specifically
requested. Beyond this, pay attention to the other aspects mentioned in the
guidelines to determine the order of the replies.

If the reply is longer than the initial text field, you can click the `...`
symbol to the right of each reply to see the complete message. Make sure to not
just sort the best and worst assistant reply, but compare each reply to settle
on a ranking.

Please make sure to read the
[guidelines](https://projects.laion.ai/Open-Assistant/docs/guides/guidelines#ranking-assistant)
before submitting.
