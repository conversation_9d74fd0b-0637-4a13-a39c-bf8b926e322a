# Open Assistant Discord bot

This is a bot for the Open Assistant project. It is a Discord bot that allows
you to interact with Open Assistant.

## Start bot

1. Install the dependencies with `npm install`
2. Change .env.sample to .env and fill in the values
3. Start redis database with `npm run redis:start`
4. Run the bot with `npm start` for development mode remember to restart the bot
   after every change.
