"""change user_stats ranking counts

Revision ID: 7c98102efbca
Revises: 619255ae9076
Create Date: 2023-01-15 00:02:45.622986

"""
import sqlalchemy as sa
import sqlmodel
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "7c98102efbca"
down_revision = "619255ae9076"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("user_stats")
    op.create_table(
        "user_stats",
        sa.Column("user_id", UUID(as_uuid=True), nullable=False),
        sa.Column("modified_date", sa.DateTime(), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False),
        sa.Column("base_date", sa.DateTime(), nullable=True),
        sa.Column("time_frame", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("leader_score", sa.Integer(), nullable=False),
        sa.Column("prompts", sa.Integer(), nullable=False),
        sa.Column("replies_assistant", sa.Integer(), nullable=False),
        sa.Column("replies_prompter", sa.Integer(), nullable=False),
        sa.Column("labels_simple", sa.Integer(), nullable=False),
        sa.Column("labels_full", sa.Integer(), nullable=False),
        sa.Column("rankings_total", sa.Integer(), nullable=False),
        sa.Column("rankings_good", sa.Integer(), nullable=False),
        sa.Column("accepted_prompts", sa.Integer(), nullable=False),
        sa.Column("accepted_replies_assistant", sa.Integer(), nullable=False),
        sa.Column("accepted_replies_prompter", sa.Integer(), nullable=False),
        sa.Column("reply_ranked_1", sa.Integer(), nullable=False),
        sa.Column("reply_ranked_2", sa.Integer(), nullable=False),
        sa.Column("reply_ranked_3", sa.Integer(), nullable=False),
        sa.Column("streak_last_day_date", sa.DateTime(), nullable=True),
        sa.Column("streak_days", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("user_id", "time_frame"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user_stats",
        sa.Column("reply_prompter_ranked_3", sa.INTEGER(), server_default="0", autoincrement=False, nullable=False),
    )
    op.add_column(
        "user_stats",
        sa.Column("reply_assistant_ranked_1", sa.INTEGER(), server_default="0", autoincrement=False, nullable=False),
    )
    op.add_column(
        "user_stats",
        sa.Column("reply_assistant_ranked_2", sa.INTEGER(), server_default="0", autoincrement=False, nullable=False),
    )
    op.add_column(
        "user_stats",
        sa.Column("reply_prompter_ranked_2", sa.INTEGER(), server_default="0", autoincrement=False, nullable=False),
    )
    op.add_column(
        "user_stats",
        sa.Column("reply_prompter_ranked_1", sa.INTEGER(), server_default="0", autoincrement=False, nullable=False),
    )
    op.add_column(
        "user_stats",
        sa.Column("reply_assistant_ranked_3", sa.INTEGER(), server_default="0", autoincrement=False, nullable=False),
    )
    op.drop_column("user_stats", "reply_ranked_3")
    op.drop_column("user_stats", "reply_ranked_2")
    op.drop_column("user_stats", "reply_ranked_1")
    # ### end Alembic commands ###
