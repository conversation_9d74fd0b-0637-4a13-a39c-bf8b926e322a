[oasst]
pg1-path=/var/lib/postgresql/data

[global]
repo1-retention-full=3
repo1-type=s3
repo1-path=/oasst-prod
repo1-s3-region=us-east-1
repo1-s3-endpoint=s3.amazonaws.com
# repo1-s3-bucket=$S3_BUCKET_NAME
# repo1-s3-key=$AWS_ACCESS_KEY
# repo1-s3-key-secret=$AWS_SECRET_KEY

# Force a checkpoint to start backup immediately.
start-fast=y
# Use delta restore.
delta=y

# Enable ZSTD compression.
compress-type=zst
compress-level=6

log-level-console=info
log-level-file=debug
