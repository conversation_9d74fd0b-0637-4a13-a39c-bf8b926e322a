name: Deploy to node

on:
  workflow_call:
    inputs:
      stack-name:
        required: false
        type: string
        default: dev
      image-tag:
        required: false
        type: string
        default: latest
      backend-port:
        required: false
        type: string
        default: 8080
      website-port:
        required: false
        type: string
        default: 3000
      inference-server-port:
        required: false
        type: string
        default: 8085

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.stack-name }}
    env:
      WEB_ADMIN_USERS: ${{ secrets.DEV_WEB_ADMIN_USERS }}
      WEB_MODERATOR_USERS: ${{ secrets.DEV_WEB_MODERATOR_USERS }}
      WEB_DISCORD_CLIENT_ID: ${{ secrets.DEV_WEB_DISCORD_CLIENT_ID }}
      WEB_DISCORD_CLIENT_SECRET: ${{ secrets.DEV_WEB_DISCORD_CLIENT_SECRET }}
      WEB_GOOGLE_CLIENT_ID: ${{ secrets.DEV_WEB_GOOGLE_CLIENT_ID }}
      WEB_GOOGLE_CLIENT_SECRET: ${{ secrets.DEV_WEB_GOOGLE_CLIENT_SECRET }}
      WEB_EMAIL_SERVER_HOST: ${{ secrets.DEV_WEB_EMAIL_SERVER_HOST }}
      WEB_EMAIL_SERVER_PASSWORD: ${{ secrets.DEV_WEB_EMAIL_SERVER_PASSWORD }}
      WEB_EMAIL_SERVER_PORT: ${{ secrets.DEV_WEB_EMAIL_SERVER_PORT }}
      WEB_EMAIL_SERVER_USER: ${{ secrets.DEV_WEB_EMAIL_SERVER_USER }}
      WEB_NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
      S3_BUCKET_NAME: ${{ secrets.S3_BUCKET_NAME }}
      S3_REGION: ${{ secrets.S3_REGION }}
      AWS_ACCESS_KEY: ${{ secrets.AWS_ACCESS_KEY }}
      AWS_SECRET_KEY: ${{ secrets.AWS_SECRET_KEY }}
      INIT_PROMPT_DISABLED_LANGS: ${{ vars.INIT_PROMPT_DISABLED_LANGS }}
      MAX_ACTIVE_TREES: ${{ vars.MAX_ACTIVE_TREES }}
      MAX_INITIAL_PROMPT_REVIEW: ${{ vars.MAX_INITIAL_PROMPT_REVIEW }}
      MAX_TREE_DEPTH: ${{ vars.MAX_TREE_DEPTH }}
      MAX_CHILDREN_COUNT: ${{ vars.MAX_CHILDREN_COUNT }}
      LONELY_CHILDREN_COUNT: ${{ vars.LONELY_CHILDREN_COUNT }}
      P_LONELY_CHILD_EXTENSION: ${{ vars.P_LONELY_CHILD_EXTENSION }}
      P_ACTIVATE_BACKLOG_TREE: ${{ vars.P_ACTIVATE_BACKLOG_TREE }}
      NUM_REQUIRED_RANKINGS: ${{ vars.NUM_REQUIRED_RANKINGS }}
      NUM_REVIEWS_REPLY: ${{ vars.NUM_REVIEWS_REPLY }}
      NUM_REVIEWS_INITIAL_PROMPT: ${{ vars.NUM_REVIEWS_INITIAL_PROMPT }}
      MIN_ACTIVE_RANKINGS_PER_LANG: ${{ vars.MIN_ACTIVE_RANKINGS_PER_LANG }}
      MAX_PROMPT_LOTTERY_WAITING: ${{ vars.MAX_PROMPT_LOTTERY_WAITING }}
      GOAL_TREE_SIZE: ${{ vars.GOAL_TREE_SIZE }}
      MESSAGE_SIZE_LIMIT: ${{ vars.MESSAGE_SIZE_LIMIT }}
      SKIP_TOXICITY_CALCULATION: ${{ vars.SKIP_TOXICITY_CALCULATION }}
      STATS_INTERVAL_DAY: ${{ vars.STATS_INTERVAL_DAY }}
      STATS_INTERVAL_WEEK: ${{ vars.STATS_INTERVAL_WEEK }}
      STATS_INTERVAL_MONTH: ${{ vars.STATS_INTERVAL_MONTH }}
      STATS_INTERVAL_TOTAL: ${{ vars.STATS_INTERVAL_TOTAL }}
      DISCORD_API_KEY: ${{ secrets.DISCORD_API_KEY }}
      DISCORD_CHANNEL_ID: ${{ vars.DISCORD_CHANNEL_ID }}
      WEB_NEXT_PUBLIC_CLOUDFLARE_CAPTCHA_SITE_KEY:
        ${{ secrets.WEB_NEXT_PUBLIC_CLOUDFLARE_CAPTCHA_SITE_KEY }}
      WEB_CLOUDFLARE_CAPTCHA_SECRET_KEY:
        ${{ secrets.WEB_CLOUDFLARE_CAPTCHA_SECRET_KEY }}
      WEB_NEXT_PUBLIC_ENABLE_EMAIL_SIGNIN_CAPTCHA:
        ${{ vars.WEB_NEXT_PUBLIC_ENABLE_EMAIL_SIGNIN_CAPTCHA }}
      WEB_NEXT_PUBLIC_ENABLE_EMAIL_SIGNIN:
        ${{ vars.WEB_NEXT_PUBLIC_ENABLE_EMAIL_SIGNIN }}
      LOGURU_LEVEL: ${{ vars.LOGURU_LEVEL }}
      MAINTENANCE_MODE: ${{ vars.MAINTENANCE_MODE }}
      BACKEND_URL: ${{ vars.BACKEND_URL }}
      WEB_NEXT_PUBLIC_BACKEND_URL: ${{ vars.WEB_NEXT_PUBLIC_BACKEND_URL }}
      BACKEND_CORS_ORIGINS: ${{ vars.BACKEND_CORS_ORIGINS }}
      WEB_INFERENCE_SERVER_HOST: ${{ vars.WEB_INFERENCE_SERVER_HOST }}
      WEB_ENABLE_CHAT: ${{ vars.WEB_ENABLE_CHAT }}
      WEB_BYE: ${{ vars.WEB_BYE }}
      WEB_ENABLE_DRAFTS_WITH_PLUGINS: ${{ vars.WEB_ENABLE_DRAFTS_WITH_PLUGINS }}
      WEB_NUM_GENERATED_DRAFTS: ${{ vars.WEB_NUM_GENERATED_DRAFTS }}
      WEB_CURRENT_ANNOUNCEMENT: ${{ vars.WEB_CURRENT_ANNOUNCEMENT }}
      WEB_INFERENCE_SERVER_API_KEY: ${{secrets.WEB_INFERENCE_SERVER_API_KEY}}
      INFERENCE_POSTGRES_PASSWORD: ${{secrets.INFERENCE_POSTGRES_PASSWORD}}
      INFERENCE_ALLOW_DEBUG_AUTH: ${{vars.INFERENCE_ALLOW_DEBUG_AUTH}}
      INFERENCE_DEBUG_API_KEYS: ${{vars.INFERENCE_DEBUG_API_KEYS}}
      INFERENCE_LOG_LEVEL: ${{vars.INFERENCE_LOG_LEVEL}}
      INFERENCE_ROOT_TOKEN: ${{secrets.INFERENCE_ROOT_TOKEN}}
      INFERENCE_API_ROOT: ${{vars.INFERENCE_API_ROOT}}
      INFERENCE_TRUSTED_CLIENT_KEYS: ${{secrets.INFERENCE_TRUSTED_CLIENT_KEYS}}
      INFERENCE_AUTH_SALT: ${{secrets.INFERENCE_AUTH_SALT}}
      INFERENCE_AUTH_SECRET: ${{secrets.INFERENCE_AUTH_SECRET}}
      INFERENCE_AUTH_DISCORD_CLIENT_ID: ${{secrets.INFERENCE_AUTH_DISCORD_CLIENT_ID}}
      INFERENCE_AUTH_DISCORD_CLIENT_SECRET: ${{secrets.INFERENCE_AUTH_DISCORD_CLIENT_SECRET}}
      INFERENCE_AUTH_GITHUB_CLIENT_ID: ${{secrets.INFERENCE_AUTH_GITHUB_CLIENT_ID}}
      INFERENCE_AUTH_GITHUB_CLIENT_SECRET: ${{secrets.INFERENCE_AUTH_GITHUB_CLIENT_SECRET}}
      INFERENCE_CORS_ORIGINS: ${{ vars.INFERENCE_CORS_ORIGINS }}
      INFERENCE_ALLOWED_MODEL_CONFIG_NAMES:
        ${{ vars.INFERENCE_ALLOWED_MODEL_CONFIG_NAMES }}
      INFERENCE_ASSISTANT_MESSAGE_TIMEOUT:
        ${{ vars.INFERENCE_ASSISTANT_MESSAGE_TIMEOUT }}
      INFERENCE_MESSAGE_QUEUE_EXPIRE: ${{ vars.INFERENCE_MESSAGE_QUEUE_EXPIRE }}
      INFERENCE_WORK_QUEUE_MAX_SIZE: ${{ vars.INFERENCE_WORK_QUEUE_MAX_SIZE }}
      INFERENCE_ENABLE_SAFETY: ${{ vars.INFERENCE_ENABLE_SAFETY }}
      INFERENCE_GUNICORN_WORKERS: ${{ vars.INFERENCE_GUNICORN_WORKERS }}
      INFERENCE_CHAT_MAX_MESSAGES: ${{ vars.INFERENCE_CHAT_MAX_MESSAGES }}
      INFERENCE_MESSAGE_MAX_LENGTH: ${{ vars.INFERENCE_MESSAGE_MAX_LENGTH }}
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Run deploy playbook
        uses: dawidd6/action-ansible-playbook@v2
        with:
          # Required, playbook filepath
          playbook: deploy-to-node.yaml
          # Optional, directory where playbooks live
          directory: ansible
          # Optional, SSH private key
          key: ${{secrets.DEV_NODE_PRIVATE_KEY}}
          # Optional, literal inventory file contents
          inventory: |
            [dev]
            dev01 ansible_host=${{secrets.DEV_NODE_IP}} ansible_connection=ssh ansible_user=web-team
          options: |
            --extra-vars "stack_name=${{inputs.stack-name}} \
            image_tag=${{inputs.image-tag}} \
            backend_port=${{inputs.backend-port}} \
            website_port=${{inputs.website-port}} \
            postgres_password=${{secrets.POSTGRES_PASSWORD}} \
            web_api_key=${{secrets.WEB_API_KEY}}"

      - name: Run inference deploy playbook
        uses: dawidd6/action-ansible-playbook@v2
        with:
          # Required, playbook filepath
          playbook: inference/deploy-server.yaml
          # Optional, directory where playbooks live
          directory: ansible
          # Optional, SSH private key
          key: ${{secrets.DEV_NODE_PRIVATE_KEY}}
          # Optional, literal inventory file contents
          inventory: |
            [dev]
            dev01 ansible_host=${{secrets.DEV_NODE_IP}} ansible_connection=ssh ansible_user=web-team
          options: |
            --extra-vars "stack_name=${{inputs.stack-name}} \
            image_tag=${{inputs.image-tag}} \
            server_port=${{inputs.inference-server-port}}"
