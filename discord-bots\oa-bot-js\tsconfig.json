{"compilerOptions": {"target": "es2022", "module": "ESNext", "strict": true, "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@/handlers/*": ["src/handlers/*"]}, "incremental": true, "moduleResolution": "node", "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true}, "include": ["src"], "exclude": ["node_modules", "dist"]}