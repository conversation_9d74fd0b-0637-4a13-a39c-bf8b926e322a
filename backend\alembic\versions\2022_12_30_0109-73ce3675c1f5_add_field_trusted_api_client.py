"""add field trusted api client

Revision ID: 73ce3675c1f5
Revises: 464ec4667aae
Create Date: 2022-12-30 01:09:06.446020

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "73ce3675c1f5"
down_revision = "464ec4667aae"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("api_client", sa.<PERSON>umn("trusted", sa.<PERSON>(), server_default=sa.text("false"), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("api_client", "trusted")
    # ### end Alembic commands ###
