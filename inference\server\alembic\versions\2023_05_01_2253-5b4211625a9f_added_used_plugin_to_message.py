"""added used plugin to message

Revision ID: 5b4211625a9f
Revises: ea19bbc743f9
Create Date: 2023-05-01 22:53:16.297495

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "5b4211625a9f"
down_revision = "ea19bbc743f9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("message", sa.Column("used_plugin", postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("message", "used_plugin")
    # ### end Alembic commands ###
