# Classifying an assistant reply

In this task, you'll be shown a random message written by another person. This
message is mimicking a reply from the assistant, addressing a previous prompt
from a user - an **assistant reply**. Your job is to rate parameters like
quality or politeness, as well as include any applicable labels, such as spam,
PII or sexual content.

If you can't determine whether or not a reply is factually accurate, skip it
instead. If you are unsure of the definition of any of the labels, look up their
definition
[here](https://projects.laion.ai/Open-Assistant/docs/guides/guidelines#label-explanation).

Replies generated from other chatbots such as ChatGPT are **not** permitted and
in many cases against the terms of service of the provider. Answers that were
generated from LLMs should be penalized accordingly. You can use detectors such
as [ZeroGPT](https://www.zerogpt.com/) to determine if a message was written by
a human - just keep in mind that these tools aren't foolproof and can falsely
accuse human-written text as machine-generated, and vice versa.

Please make sure to read the
[guidelines](https://projects.laion.ai/Open-Assistant/docs/guides/guidelines#classifying-assistant)
before submitting.
