"""Added new table for flagged messages

Revision ID: caee1e8ee0bc
Revises: 8c8241d1f973
Create Date: 2023-02-07 19:22:12.696257

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "caee1e8ee0bc"
down_revision = "8c8241d1f973"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "flagged_message",
        sa.Column("message_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "created_date", sa.DateTime(timezone=True), server_default=sa.text("CURRENT_TIMESTAMP"), nullable=False
        ),
        sa.Column("processed", sa.<PERSON>(), nullable=False),
        sa.ForeignKeyConstraint(["message_id"], ["message.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("message_id"),
    )
    op.create_index(op.f("ix_flagged_message_created_date"), "flagged_message", ["created_date"], unique=False)
    op.create_index(op.f("ix_flagged_message_processed"), "flagged_message", ["processed"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_flagged_message_processed"), table_name="flagged_message")
    op.drop_index(op.f("ix_flagged_message_created_date"), table_name="flagged_message")
    op.drop_table("flagged_message")
    # ### end Alembic commands ###
