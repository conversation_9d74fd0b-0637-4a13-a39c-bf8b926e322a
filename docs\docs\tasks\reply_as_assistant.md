# Providing an assistant reply

In this task, you will play the role of the chatbot - the **assistant**. You'll
see a message from a user - a **prompt**. Your job is to provide a reply to the
given prompt. The reply should address the user's requests as well as possible
while adhering to the guidelines.

Remember that you can use [Markdown](https://www.markdownguide.org/basic-syntax)
to format your message. This can make your reply considerably easier to read.
When inserting a code block, add a language specification to give the code
[syntax highlighting](https://www.markdownguide.org/extended-syntax/#syntax-highlighting).
You can find a full list of supported languages and their abbreviations
[here](https://github.com/jincheng9/markdown_supported_languages#heres-a-full-list-of-supported-languages).

You can find some examples addressing some edge cases
[here](https://projects.laion.ai/Open-Assistant/docs/guides/examples).

Please make sure to read the
[guidelines](https://projects.laion.ai/Open-Assistant/docs/guides/guidelines#assistant-reply)
before submitting.
