"""initial revision

Revision ID: 7d5be54acd49
Revises:
Create Date: 2023-03-12 17:42:42.807459

"""
import sqlalchemy as sa
import sqlmodel
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "7d5be54acd49"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("provider", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("provider_account_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("display_name", sqlmodel.sql.sqltypes.AutoString(length=256), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_user_provider"), "user", ["provider"], unique=False)
    op.create_index(op.f("ix_user_provider_account_id"), "user", ["provider_account_id"], unique=False)
    op.create_index("provider", "user", ["provider_account_id"], unique=True)
    op.create_table(
        "worker",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("api_key", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("trusted", sa.Boolean(), nullable=False),
        sa.Column("in_compliance_check_since", sa.DateTime(), nullable=True),
        sa.Column("next_compliance_check", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_worker_api_key"), "worker", ["api_key"], unique=False)
    op.create_table(
        "chat",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("user_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("modified_at", sa.DateTime(), nullable=False),
        sa.Column("title", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_chat_created_at"), "chat", ["created_at"], unique=False)
    op.create_index(op.f("ix_chat_modified_at"), "chat", ["modified_at"], unique=False)
    op.create_index(op.f("ix_chat_user_id"), "chat", ["user_id"], unique=False)
    op.create_table(
        "worker_compliance_check",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("worker_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("compare_worker_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("start_time", sa.DateTime(), nullable=False),
        sa.Column("end_time", sa.DateTime(), nullable=True),
        sa.Column("responded", sa.Boolean(), nullable=False),
        sa.Column("error", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("passed", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["worker_id"],
            ["worker.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_worker_compliance_check_compare_worker_id"),
        "worker_compliance_check",
        ["compare_worker_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_worker_compliance_check_worker_id"), "worker_compliance_check", ["worker_id"], unique=False
    )
    op.create_table(
        "worker_event",
        sa.Column("worker_config", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("worker_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("time", sa.DateTime(), nullable=False),
        sa.Column("event_type", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.ForeignKeyConstraint(
            ["worker_id"],
            ["worker.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_worker_event_worker_id"), "worker_event", ["worker_id"], unique=False)
    op.create_table(
        "message",
        sa.Column("work_parameters", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("worker_config", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("role", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("chat_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("parent_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("content", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("error", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("state", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("work_begin_at", sa.DateTime(), nullable=True),
        sa.Column("work_end_at", sa.DateTime(), nullable=True),
        sa.Column("worker_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("worker_compat_hash", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("score", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["chat_id"],
            ["chat.id"],
        ),
        sa.ForeignKeyConstraint(
            ["worker_id"],
            ["worker.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_message_chat_id"), "message", ["chat_id"], unique=False)
    op.create_index(op.f("ix_message_role"), "message", ["role"], unique=False)
    op.create_index(op.f("ix_message_worker_compat_hash"), "message", ["worker_compat_hash"], unique=False)
    op.create_table(
        "report",
        sa.Column("id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("message_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("report_type", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("reason", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.ForeignKeyConstraint(
            ["message_id"],
            ["message.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_report_message_id"), "report", ["message_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_report_message_id"), table_name="report")
    op.drop_table("report")
    op.drop_index(op.f("ix_message_worker_compat_hash"), table_name="message")
    op.drop_index(op.f("ix_message_role"), table_name="message")
    op.drop_index(op.f("ix_message_chat_id"), table_name="message")
    op.drop_table("message")
    op.drop_index(op.f("ix_worker_event_worker_id"), table_name="worker_event")
    op.drop_table("worker_event")
    op.drop_index(op.f("ix_worker_compliance_check_worker_id"), table_name="worker_compliance_check")
    op.drop_index(op.f("ix_worker_compliance_check_compare_worker_id"), table_name="worker_compliance_check")
    op.drop_table("worker_compliance_check")
    op.drop_index(op.f("ix_chat_user_id"), table_name="chat")
    op.drop_index(op.f("ix_chat_modified_at"), table_name="chat")
    op.drop_index(op.f("ix_chat_created_at"), table_name="chat")
    op.drop_table("chat")
    op.drop_index(op.f("ix_worker_api_key"), table_name="worker")
    op.drop_table("worker")
    op.drop_index("provider", table_name="user")
    op.drop_index(op.f("ix_user_provider_account_id"), table_name="user")
    op.drop_index(op.f("ix_user_provider"), table_name="user")
    op.drop_table("user")
    # ### end Alembic commands ###
