"""switch to timestamp with tz

Revision ID: 7f0a28a156f4
Revises: 0964ac95170d
Create Date: 2023-01-19 21:53:01.107137

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "7f0a28a156f4"
down_revision = "0964ac95170d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(table_name="user_stats", column_name="modified_date", type_=sa.DateTime(timezone=True))
    op.alter_column(table_name="user_stats", column_name="base_date", type_=sa.DateTime(timezone=True))
    op.alter_column(table_name="journal_integration", column_name="last_run", type_=sa.DateTime(timezone=True))
    op.alter_column(table_name="message_embedding", column_name="created_date", type_=sa.DateTime(timezone=True))
    op.alter_column(table_name="message_reaction", column_name="created_date", type_=sa.DateTime(timezone=True))
    op.alter_column(table_name="message_toxicity", column_name="created_date", type_=sa.DateTime(timezone=True))
    op.alter_column(table_name="message", column_name="created_date", type_=sa.DateTime(timezone=True))
    op.alter_column(table_name="task", column_name="created_date", type_=sa.DateTime(timezone=True))
    op.alter_column(table_name="task", column_name="expiry_date", type_=sa.DateTime(timezone=True))
    op.alter_column(table_name="text_labels", column_name="created_date", type_=sa.DateTime(timezone=True))
    op.alter_column(table_name="user", column_name="created_date", type_=sa.DateTime(timezone=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(table_name="user_stats", column_name="modified_date", type_=sa.DateTime(timezone=False))
    op.alter_column(table_name="user_stats", column_name="base_date", type_=sa.DateTime(timezone=False))
    op.alter_column(table_name="journal_integration", column_name="last_run", type_=sa.DateTime(timezone=False))
    op.alter_column(table_name="message_embedding", column_name="created_date", type_=sa.DateTime(timezone=False))
    op.alter_column(table_name="message_reaction", column_name="created_date", type_=sa.DateTime(timezone=False))
    op.alter_column(table_name="message_toxicity", column_name="created_date", type_=sa.DateTime(timezone=False))
    op.alter_column(table_name="message", column_name="created_date", type_=sa.DateTime(timezone=False))
    op.alter_column(table_name="task", column_name="created_date", type_=sa.DateTime(timezone=False))
    op.alter_column(table_name="task", column_name="expiry_date", type_=sa.DateTime(timezone=False))
    op.alter_column(table_name="text_labels", column_name="created_date", type_=sa.DateTime(timezone=False))
    op.alter_column(table_name="user", column_name="created_date", type_=sa.DateTime(timezone=False))
    # ### end Alembic commands ###
