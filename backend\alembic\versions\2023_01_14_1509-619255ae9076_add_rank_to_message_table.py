"""add rank to message table

Revision ID: 619255ae9076
Revises: bcc2fe18d214
Create Date: 2023-01-14 15:09:03.462482

"""
import sqlalchemy as sa
import sqlmodel
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "619255ae9076"
down_revision = "bcc2fe18d214"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("message", sa.Column("rank", sa.Integer(), nullable=True))
    op.add_column("message_toxicity", sa.Column("score", sa.Float(), nullable=True))
    op.add_column("message_toxicity", sa.Column("label", sqlmodel.sql.sqltypes.AutoString(length=256), nullable=False))
    op.drop_column("message_toxicity", "toxicity")
    op.add_column("user_stats", sa.Column("time_frame", sqlmodel.sql.sqltypes.AutoString(), nullable=False))
    op.add_column("user_stats", sa.Column("prompts", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("replies_assistant", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("replies_prompter", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("labels_simple", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("labels_full", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("rankings_total", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("rankings_good", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("accepted_prompts", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("accepted_replies_assistant", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("accepted_replies_prompter", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("reply_assistant_ranked_1", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("reply_assistant_ranked_2", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("reply_assistant_ranked_3", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("reply_prompter_ranked_1", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("reply_prompter_ranked_2", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("reply_prompter_ranked_3", sa.Integer(), nullable=False))
    op.add_column("user_stats", sa.Column("streak_last_day_date", sa.DateTime(), nullable=True))
    op.add_column("user_stats", sa.Column("streak_days", sa.Integer(), nullable=True))
    op.drop_column("user_stats", "messages")
    op.drop_column("user_stats", "upvotes")
    op.drop_column("user_stats", "task_reward")
    op.drop_column("user_stats", "compare_wins")
    op.drop_column("user_stats", "compare_losses")
    op.drop_column("user_stats", "downvotes")
    op.drop_column("user_stats", "reactions")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("user_stats", sa.Column("reactions", sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column("user_stats", sa.Column("downvotes", sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column("user_stats", sa.Column("compare_losses", sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column("user_stats", sa.Column("compare_wins", sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column("user_stats", sa.Column("task_reward", sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column("user_stats", sa.Column("upvotes", sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column("user_stats", sa.Column("messages", sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_column("user_stats", "streak_days")
    op.drop_column("user_stats", "streak_last_day_date")
    op.drop_column("user_stats", "reply_prompter_ranked_3")
    op.drop_column("user_stats", "reply_prompter_ranked_2")
    op.drop_column("user_stats", "reply_prompter_ranked_1")
    op.drop_column("user_stats", "reply_assistant_ranked_3")
    op.drop_column("user_stats", "reply_assistant_ranked_2")
    op.drop_column("user_stats", "reply_assistant_ranked_1")
    op.drop_column("user_stats", "accepted_replies_prompter")
    op.drop_column("user_stats", "accepted_replies_assistant")
    op.drop_column("user_stats", "accepted_prompts")
    op.drop_column("user_stats", "rankings_good")
    op.drop_column("user_stats", "rankings_total")
    op.drop_column("user_stats", "labels_full")
    op.drop_column("user_stats", "labels_simple")
    op.drop_column("user_stats", "replies_prompter")
    op.drop_column("user_stats", "replies_assistant")
    op.drop_column("user_stats", "prompts")
    op.drop_column("user_stats", "time_frame")
    op.add_column(
        "message_toxicity",
        sa.Column("toxicity", postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    )
    op.drop_column("message_toxicity", "label")
    op.drop_column("message_toxicity", "score")
    op.drop_column("message", "rank")
    # ### end Alembic commands ###
