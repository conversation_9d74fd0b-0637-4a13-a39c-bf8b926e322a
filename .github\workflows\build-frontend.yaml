name: Build Frontend

on:
  push:
    branches:
      - main
    paths:
      - website/**
  pull_request:
    paths:
      - website/**
  workflow_call:

jobs:
  build-frontend:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./website
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 16.x
          cache: "npm"
          cache-dependency-path: website/package-lock.json
      - run: npm ci
      - run: npx prisma generate
      - run: npm run build
